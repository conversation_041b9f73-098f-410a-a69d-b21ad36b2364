// 文档：https://wiki.bestsign.tech/pages/viewpage.action?pageId=33161502
// 版本：三月 19, 2020 20:20
// 此配置依赖 eslint: "^6.8.0",eslint-plugin-vue: "^6.2.2"
module.exports = {
    root: true,
    parserOptions: {
        parser: '@babel/eslint-parser',
        sourceType: 'module',
    },
    env: {
        browser: true,
        node: true,
        es6: true,
        jest: true,
    },
    extends: ['plugin:vue/recommended', 'eslint:recommended'],

    rules: {
        'vue/order-in-components': [
            1,
            {
                order: [
                    'template',
                    'el',
                    'name',
                    'parent',
                    'functional',
                    ['delimiters', 'comments'],
                    'components',
                    'extends',
                    'mixins',
                    'directives',
                    'filters',
                    'inheritAttrs',
                    'model',
                    ['props', 'propsData'],
                    'data',
                    'computed',
                    'watch',
                    'methods',
                    'LIFECYCLE_HOOKS',
                    'render',
                    'renderError',
                ],
            },
        ],
        'vue/attribute-hyphenation': 'off', // 关闭，存在小驼峰和 - 连接的混写场景
        'vue/attributes-order': [
            0,
            {
                order: [
                    'DEFINITION', // is
                    'LIST_RENDERING', // v-for
                    ['CONDITIONALS', 'GLOBAL', 'UNIQUE'], // v-if, id, ref
                    'RENDER_MODIFIERS', // v-once / v-pre
                    'TWO_WAY_BINDING', // v-model
                    'OTHER_DIRECTIVES', // v-custom-directiv
                    'OTHER_ATTR', // custom-prop="foo" / v-bind:prop="foo" / :prop="foo" / class / style / :class
                    'EVENTS', // @click / v-on
                    'CONTENT', // v-text / v-html
                ],
            },
        ],
        'vue/html-closing-bracket-newline': [
            1,
            {
                // 标签闭括号，单行元素不可换行，多行元素可起新行
                singleline: 'never',
                multiline: 'always',
            },
        ],
        'vue/html-indent': [
            1,
            4,
            {
                attribute: 1,
                // "baseIndent": 1,
                // "closeBracket": 0,
                alignAttributesVertically: false,
                ignores: ['mock/*'],
            },
        ],
        'vue/html-end-tags': 1,
        'vue/html-quotes': [1, 'double'], // html属性 双引号
        'vue/html-self-closing': [0],
        'vue/no-spaces-around-equal-signs-in-attribute': 'warn', // 不允许属性赋值左右有空格
        'vue/require-v-for-key': 1,
        'vue/no-use-v-if-with-v-for': [2], // v-for v-if不可以一起用
        'vue/max-attributes-per-line': [
            2,
            {
                singleline: 5, // 元素属性如果只有一行，则最多允许写5个
                multiline: {
                    max: 1, // 元素属性如果写多行，则每行最多允许写一个
                    allowFirstLine: true, // 元素属性如果写多行，第一个属性允许写在标签那一行
                },
            },
        ],
        'vue/singleline-html-element-content-newline': 'off', // Require a line break before and after the contents of a singleline element 子元素若有1个，必须新起一行写
        'vue/multiline-html-element-content-newline': 'off', // Require a line break before and after the contents of a multiline element 子元素若有多个，必须每个只能写一行，不能连写
        'vue/name-property-casing': [1, 'PascalCase'], // Enforce specific casing for the name property in Vue components PascalCase表示驼峰写法 kebab-case表示横杠写法
        'vue/component-name-in-template-casing': [1, 'PascalCase'], // <template>中组件的名字必须是驼峰写法
        'vue/camelcase': [
            2,
            {
                properties: 'always',
            },
        ], // <template>中属性命名必须是驼峰
        'vue/no-shared-component-data': 2, // data必须使用function
        'vue/prop-name-casing': [2, 'camelCase'], // prop命名必须是驼峰
        'vue/require-default-prop': 2,
        'vue/require-prop-types': 2,
        'vue/require-valid-default-prop': 2, // Array or Object的default必须使用function
        'vue/this-in-template': [2, 'never'],
        'vue/v-bind-style': 1, // v-bind使用缩写:
        'vue/v-on-style': 1, // v-on使用缩写@
        'vue/no-v-html': 'off', // 可以但不推荐，注意XSS攻击

        // javascript

        // 一、Stylistic Issues 这些规则是关于风格指南的

        // 代码缩进
        indent: [
            2,
            4,
            {
                SwitchCase: 1, // 强制 switch 语句中的 case 子句的缩进级别
                outerIIFEBody: 0, // 立即执行函数没有缩进
                MemberExpression: 1, // 强制多行属性链的缩进
            },
        ],
        'vue/script-indent': [
            2,
            4,
            {
                baseIndent: 0, // 第一列贴边
                switchCase: 1,
            },
        ],
        'jsx-quotes': [2, 'prefer-double'],

        // 声明
        'one-var': [
            2,
            {
                initialized: 'never', // （Stylistic Issues. no）要求每个作用域的初始化的变量有多个变量声明
            },
        ],

        // 代码块
        'padded-blocks': [1, 'never'], // （Stylistic Issues. no）禁止块语句和类的开始或末尾有空行
        'operator-linebreak': [
            2,
            'after',
            {
                // （Stylistic Issues. no）先换行，再写操作符例如三元操作符 +等
                overrides: {
                    '?': 'before',
                    ':': 'before',
                },
            },
        ],
        'block-spacing': [2, 'always'], // 强制在代码块中开括号前和闭括号后有空格，例如if (foo) { bar = 0; }
        'brace-style': [
            2,
            '1tbs',
            {
                // 大括号风格要求
                allowSingleLine: false,
            },
        ],
        curly: [1, 'all'], // 控制语句如 if、else if、else、for、while、do 强制使用大括号，如需简写可用三元运算符实现

        // 单引号
        quotes: [
            2,
            'single',
            {
                // 要求尽可能地使用单引号
                avoidEscape: true,
                allowTemplateLiterals: true,
            },
        ],

        // 命名
        camelcase: [
            0,
            {
                // 是否必须驼峰
                properties: 'always',
            },
        ],

        // 分号
        semi: [1, 'always'], // 必须写分号
        'semi-spacing': [
            2,
            {
                before: false, // 分号前不能有空格
                after: true, // 分号不在一行的最后时，分号后必须有空格
            },
        ],

        // 逗号
        'comma-dangle': [2, 'always-multiline'], // 数组和对象的最后一项需要写逗号
        'comma-spacing': [
            1,
            {
                // 逗号前后是否有空格
                before: false,
                after: true,
            },
        ],
        'comma-style': [1, 'last'], // 逗号放在一句的最后，不要另起一行

        // 冒号
        'key-spacing': [
            2,
            {
                beforeColon: false, // 冒号前不要有空格
                afterColon: true, // 冒号后必须有空格
            },
        ],

        // 空格
        'keyword-spacing': [
            2,
            {
                // 关键字（if\while\for）前后是否有空格
                before: true,
                after: true,
            },
        ],
        'space-before-blocks': [2, 'always'], // 语句块{}之前总是至少有一个前置空格
        'space-before-function-paren': [2, 'never'], // function() {} 禁止在参数的(前面有空格
        'space-in-parens': [2, 'never'], // 强制圆括号内没有空格，例如foo( 'bar');
        'space-infix-ops': 2, // 要求操作符周围有空格，例如a+ b是错误的
        'space-unary-ops': [
            2,
            {
                words: true, // 单词类一元操作符，例如delete foo.bar;
                nonwords: false, // 一元操作符: -、+、--、++、!、!!，例如foo++
            },
        ],
        'spaced-comment': [
            2,
            'always',
            {
                // // 或 /* 必须跟随至少一个空白
                markers: ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ','],
            },
        ],
        'template-curly-spacing': [2, 'never'], // ${} 禁止花括号内出现空格
        'object-curly-spacing': [2, 'always'], // 强制在花括号中使用一致的空格，例如不要写var obj = {'foo': 'bar' };
        'array-bracket-spacing': [2, 'never'], // 不允许数组括号内的空格，例如var ary = ['foo', 'bar']

        // 点号
        'dot-location': [1, 'property'], // 对象的点操作符和属性放在同一行

        // 实例化
        'new-cap': [
            2,
            {
                newIsCap: true, // 类new的时候要大写
                capIsNew: true, // 类实例化必须要写new
            },
        ],
        'new-parens': 2, // 调用无参构造函数时必须带括号

        // IIFE
        'wrap-iife': [2, 'inside'], // （Best Practices. no）要求 IIFE 使用括号括起来

        // 其他
        yoda: [2, 'never'], // 不要写if ("red" === color) {}
        'eol-last': 1, // 文件最后一行需要是空行
        eqeqeq: [
            2,
            'always',
            {
                null: 'ignore',
            },
        ],

        // 调试
        'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,

        // 二、Possible Errors 这些规则与 JavaScript 代码中可能的错误或逻辑错误有关

        'no-prototype-builtins': 0, // （Possible Errors. yes）允许直接调用 Object.prototypes 的内置属性

        // 类型检查
        'use-isnan': 2, // 要求使用 isNaN() 检查 NaN
        'valid-typeof': 2, //（Possible Errors. yes）强制 typeof 表达式与有效的字符串进行比较

        // 三、Best Practices 这些规则是关于最佳实践的，帮助你避免一些问题

        'accessor-pairs': 2, // 强制 getter 和 setter 在对象中成对出现

        // 四、ES6

        'yield-star-spacing': [2, 'both'], // （es6. no）yield * other();
        'prefer-const': 2, // 如果一个变量不会被重新赋值，必须使用const进行声明。
        'arrow-spacing': [
            2,
            {
                // 强制箭头函数的箭头前后使用一致的空格
                before: true,
                after: true,
            },
        ],
        'constructor-super': 1, // constructor验证super
        'generator-star-spacing': [
            2,
            {
                before: false,
                after: true,
            },
        ],

        // 五、node

        // 'handle-callback-err': [2, '^(err|error)$'], node err first

        // 六、其他

        'no-array-constructor': 2, // new Array()时，参数只能写length
        'no-caller': 2, // arguments.caller 和 arguments.callee 的使用使一些代码优化变得不可能。在 JavaScript 的新版本中它们已被弃用，同时在 ECMAScript 5 的严格模式下，它们也是被禁用的。
        'no-console': process.env.NODE_ENV === 'production' ? 2 : 0,
        'no-class-assign': 2, // （es6） 禁止给一个是类的变量再次赋值为其他类型
        'no-cond-assign': 2, // （Possible Errors. yes）禁止在if等条件中写赋值语句
        'no-const-assign': 2, // （es6. yes）禁止修改const变量
        'no-control-regex': 0, // （Possible Errors. yes）禁止在正则表达式中出现控制字符
        'no-delete-var': 2, // （Variables. yes）禁止删除变量
        'no-dupe-args': 2, // （Possible Errors. yes）禁止 function 定义中出现重名参数
        'no-dupe-class-members': 2, // （es6. yes）不允许类成员中有重复的名称，禁止重载
        'no-dupe-keys': 2, // （Possible Errors. yes）禁止在对象字面量中出现重复的键
        'no-duplicate-case': 2, // （Possible Errors. yes）禁止重复 case 标签
        'no-empty-character-class': 2, // （Possible Errors. yes）禁止在正则表达式中出现空字符集 /^abc[]/
        'no-empty-pattern': 2, // （Best Practices. yes）禁止使用空解构模式 例如var {} = foo;
        'no-eval': 2, // （Best Practices. no）禁止使用eval
        'no-ex-assign': 2, // （Possible Errors. yes）禁止对 catch 子句中的异常重新赋值。catch (e) {e = 10}
        'no-extend-native': 2, // （Best Practices. no）禁止扩展原生类型
        'no-extra-bind': 2, // （Best Practices. no）函数体中没有使用this的时候，不要使用bind
        'no-extra-boolean-cast': 2, // （Possible Errors. yes）禁止不必要的布尔类型转换 例如var foo = Boolean(!!bar);
        'no-extra-parens': [2, 'functions'], // （Possible Errors. no）写函数时，禁止写冗余的括号 例如var y = (function () {return 1;});
        'no-fallthrough': 2, //（Best Practices. yes）禁止 case 语句 fall through，就是要写break或return
        'no-floating-decimal': 2, // （Best Practices. no）小数点前后要有数字，例如不要写.5，而写0.5
        'no-func-assign': 2, // （Possible Errors. yes）禁止对 function 声明重新赋值
        'no-implied-eval': 2, // （Best Practices. no）禁用隐式的eval() ，例如setTimeout("alert('Hi!');", 100);
        'no-inner-declarations': [2, 'functions'], // （Possible Errors. yes）禁止在嵌套的块中出现变量声明或 function 声明，例如if (test) { function doSomething(){} }
        'no-invalid-regexp': 2, // （Possible Errors. yes）禁止在 RegExp 构造函数中出现无效的正则表达式 (no-invalid-regexp) RegExp('[')
        'no-irregular-whitespace': [
            2,
            {
                skipComments: true,
                skipTemplates: true,
            },
        ], //（Possible Errors. yes） 防止拷贝的代码存在不规则的空白
        'no-iterator': 2, // （Best Practices. no）禁用一个废弃的迭代器属性__iterator__
        'no-label-var': 2, // （Variables. no）不允许标签与变量同名
        'no-labels': [
            2,
            {
                allowLoop: false,
                allowSwitch: false,
            },
        ],
        'no-lone-blocks': 2,
        'no-mixed-spaces-and-tabs': 2,
        'no-multi-spaces': [
            0,
            {
                // 不支持{ prop: 'id',       label: 'ID',        width: '' },所以关闭
                exceptions: {
                    Property: true,
                    VariableDeclarator: true,
                    ImportDeclaration: true,
                },
            },
        ],
        'no-multi-str': 2,
        'no-multiple-empty-lines': [
            2,
            {
                max: 1,
            },
        ],
        'no-native-reassign': 2,
        'no-negated-in-lhs': 2, //
        'no-new-object': 2,
        'no-new-require': 2,
        'no-new-symbol': 2,
        'no-new-wrappers': 2,
        'no-obj-calls': 2,
        'no-octal': 2,
        'no-octal-escape': 2,
        'no-path-concat': 2,
        'no-proto': 2,
        'no-redeclare': 2,
        'no-regex-spaces': 2,
        // 'no-return-assign': [2, 'except-parens'], // 必须要有返回值
        'no-self-assign': 2,
        'no-self-compare': 2,
        'no-sequences': 2,
        'no-shadow-restricted-names': 2,
        'no-spaced-func': 2,
        'no-sparse-arrays': 2,
        'no-this-before-super': 2,
        'no-throw-literal': 2,
        'no-trailing-spaces': 1,
        'no-undef': 2,
        'no-undef-init': 2,
        'no-unexpected-multiline': 2,
        'no-unmodified-loop-condition': 2,
        'no-unneeded-ternary': [
            2,
            {
                defaultAssignment: false,
            },
        ],
        'no-unreachable': 2,
        'no-unsafe-finally': 2,
        'no-unused-vars': [
            1,
            {
                vars: 'all',
                args: 'after-used',
            },
        ],
        'no-useless-call': 2,
        'no-useless-computed-key': 2,
        'no-useless-constructor': 2,
        'no-useless-escape': 0,
        'no-whitespace-before-property': 2,
        'no-with': 2,
    },
    overrides: [
        {
            files: ['*.vue'],
            rules: {
                indent: 'off',
            },
        },
    ],
    globals: {
        Vue: true,
        VueRouter: true,
        'uni': true,
        'wx': true,
        'ROUTES': true,
        //   "_": true,
        //   "WeixinJSBridge": true,
        //   "BASIC_PATH": true,
        //   "_hmt": true,
    },
};
