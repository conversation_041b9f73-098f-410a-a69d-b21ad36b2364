
# 🚀 微信开发者工具导入指南

## 解决错误码 6000100 的步骤

### 1. 更新开发者工具
- 确保使用最新版本的微信开发者工具
- 建议版本: >= 1.06.2307260

### 2. 导入项目
1. 打开微信开发者工具
2. 选择 "导入项目"
3. 项目目录选择: `/Users/<USER>/ssq/小程序/delta-sign-applet/dist/build/mp-weixin`
4. AppID: wx211c5f8feae8d939 (测试环境)

### 3. 开发设置
- ✅ 已关闭 URL 校验 (urlCheck: false)
- ✅ 已启用 ES6 转 ES5
- ✅ 已启用样式补全

### 4. 如果仍有问题
1. 清除缓存: 工具 → 构建npm → 清除缓存
2. 重新编译: 项目 → 重新编译
3. 重启开发者工具

### 5. 网络问题排查
- 检查网络连接
- 关闭代理软件
- 尝试切换网络环境

## 📞 技术支持
如问题仍然存在，请查看 TROUBLESHOOTING-6000100.md 文档
