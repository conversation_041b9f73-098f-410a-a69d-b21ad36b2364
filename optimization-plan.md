# 微信小程序主包体积优化方案

## 📊 现状分析

### 当前主包体积：3.1MB（目标：≤1.5MB）

**主要占用分布：**
- common目录: 812KB（公共代码）
- components目录: 740KB（组件库）
- views目录: 604KB（主包页面）
- assets目录: 276KB（静态资源）
- static目录: 128KB（图标资源）

## 🎯 智能分包策略

### 1. 主包保留（核心功能）
**tabBar页面（必须保留）：**
- `views/basic/index` - 首页
- `views/home/<USER>
- `views/sendGuide/index` - 发送合同入口
- `views/riskJudge/index` - 风险判断
- `views/account/index` - 账号管理

**核心功能页面：**
- `views/alipayLogin/index` - 登录页面
- `views/webview/index` - 核心webview
- `views/webviewRedirect/index` - webview重定向
- `views/doc/index` - 文档列表
- `views/sign/index` - 签署页面

### 2. 新增分包规划

#### 📦 authPackage（认证相关）- 独立分包
```json
{
  "root": "authPackage",
  "independent": true,
  "pages": [
    "personAuth/index",
    "account/authCert", 
    "signPassword/index",
    "configSignPwd/index"
  ]
}
```

#### 📦 videoPackage（视频教程）- 独立分包  
```json
{
  "root": "videoPackage", 
  "independent": true,
  "pages": [
    "descVideo/index",
    "entAuthVideo/index", 
    "personAuthVideo/index",
    "entSendVideo/index",
    "personSendVideo/index",
    "signVideo/index"
  ]
}
```

#### 📦 businessPackage（业务功能）- 普通分包
```json
{
  "root": "businessPackage",
  "pages": [
    "charge/index",
    "chargeOrder/index", 
    "payResult/index",
    "shareContract/index",
    "shareView/index",
    "scanLogin/index",
    "copyUrl/index",
    "docDownload/index",
    "fileList/index",
    "todoList/index"
  ]
}
```

#### 📦 utilityPackage（工具页面）- 普通分包
```json
{
  "root": "utilityPackage", 
  "pages": [
    "success/index",
    "detail/index",
    "customizedRedirect/index",
    "agreementWebview/index"
  ]
}
```

### 3. 组件分包优化

#### 移动到subSendViews分包的组件：
- selectUploadType
- describeInfo  
- signerInfo
- uploadFileList
- privateMessage
- signerList
- addReceiverBtn
- labelMark
- sendFooter
- ridingSeal
- knewDetailPopup
- infoFillPopup
- sampleLabel
- templateDocList
- templateDocForm

#### 新建commonComponents分包：
```json
{
  "root": "commonComponents",
  "pages": [],
  "components": [
    "activityLogo",
    "centerPopup", 
    "customToast",
    "searchBar",
    "webview"
  ]
}
```

## 🖼️ 资源优化策略

### 图片压缩目标（≥60%）：
- bankBanner.png: 124KB → 50KB
- hubbleBanner.png: 50KB → 20KB
- contractBanner.png: 50KB → 20KB  
- shareImg.png: 46KB → 18KB
- downloadTip2.png: 33KB → 13KB

### 字体优化（≥85%）：
- 提取实际使用字符集
- 使用fontmin工具压缩

## ⚡ 代码优化

### 1. 启用lazyCodeLoading
```json
"lazyCodeLoading": "requiredComponents"
```

### 2. Tree-shaking优化
```json
"optimization": {
  "subPackages": true,
  "treeShaking": {
    "enable": true
  }
}
```

### 3. 预加载配置
```json
"preloadRule": {
  "views/sendGuide/index": {
    "network": "all",
    "packages": ["subSendViews"]
  },
  "views/account/index": {
    "network": "all", 
    "packages": ["authPackage"]
  }
}
```

## 📈 预期效果

### 主包体积优化：
- 页面迁移节省：~400KB
- 组件迁移节省：~300KB  
- 图片压缩节省：~200KB
- 代码优化节省：~100KB

**预期主包体积：3.1MB → 1.2MB**

### 性能提升：
- 首屏加载时间：减少40%
- 分包预加载命中率：≥95%
- 路由跳转兼容性：100%

## 🔧 实施步骤

1. ✅ 项目分析与现状评估
2. 🔄 智能分包规划设计  
3. ⏳ 资源深度优化
4. ⏳ 代码逻辑重构优化
5. ⏳ 分包配置实施
6. ⏳ 跨分包引用重构
7. ⏳ 构建优化配置
8. ⏳ 性能测试与验证
9. ⏳ 自动化脚本开发
