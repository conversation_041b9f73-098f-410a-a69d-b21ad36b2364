#!/usr/bin/env node

/**
 * 小程序包体积分析脚本
 * 分析主包和分包的体积分布，生成优化建议
 */

const fs = require('fs');
const path = require('path');

// 构建输出目录
const BUILD_DIR = 'dist/build/mp-weixin';

// 体积目标
const TARGET_MAIN_PACKAGE_SIZE = 1.5 * 1024; // 1.5MB in KB
const TARGET_SUBPACKAGE_SIZE = 2 * 1024; // 2MB in KB

/**
 * 获取目录大小（KB）
 */
function getDirSize(dirPath) {
    if (!fs.existsSync(dirPath)) {
        return 0;
    }
    
    let totalSize = 0;
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
        const filePath = path.join(dirPath, file.name);
        if (file.isDirectory()) {
            totalSize += getDirSize(filePath);
        } else {
            totalSize += fs.statSync(filePath).size;
        }
    }
    
    return Math.round(totalSize / 1024); // Convert to KB
}

/**
 * 获取文件大小（KB）
 */
function getFileSize(filePath) {
    if (!fs.existsSync(filePath)) {
        return 0;
    }
    return Math.round(fs.statSync(filePath).size / 1024);
}

/**
 * 分析主包体积
 */
function analyzeMainPackage() {
    const mainPackageItems = [
        { name: 'components', path: path.join(BUILD_DIR, 'components') },
        { name: 'common', path: path.join(BUILD_DIR, 'common') },
        { name: 'node-modules', path: path.join(BUILD_DIR, 'node-modules') },
        { name: 'assets', path: path.join(BUILD_DIR, 'assets') },
        { name: 'views', path: path.join(BUILD_DIR, 'views') },
        { name: 'static', path: path.join(BUILD_DIR, 'static') },
        { name: 'vendor.js', path: path.join(BUILD_DIR, 'vendor.js') },
        { name: 'common.js', path: path.join(BUILD_DIR, 'common.js') },
        { name: 'app.js', path: path.join(BUILD_DIR, 'app.js') },
        { name: 'app.json', path: path.join(BUILD_DIR, 'app.json') },
        { name: 'app.wxss', path: path.join(BUILD_DIR, 'app.wxss') },
        { name: 'project.config.json', path: path.join(BUILD_DIR, 'project.config.json') },
    ];
    
    let totalSize = 0;
    const analysis = [];
    
    for (const item of mainPackageItems) {
        const size = fs.statSync(item.path).isDirectory() 
            ? getDirSize(item.path) 
            : getFileSize(item.path);
        
        totalSize += size;
        analysis.push({
            name: item.name,
            size: size,
            percentage: 0 // Will be calculated after total is known
        });
    }
    
    // Calculate percentages
    analysis.forEach(item => {
        item.percentage = ((item.size / totalSize) * 100).toFixed(1);
    });
    
    return {
        totalSize,
        items: analysis.sort((a, b) => b.size - a.size),
        isOverTarget: totalSize > TARGET_MAIN_PACKAGE_SIZE,
        targetSize: TARGET_MAIN_PACKAGE_SIZE
    };
}

/**
 * 分析分包体积
 */
function analyzeSubPackages() {
    const subPackages = [
        'subSendViews',
        'authPackage', 
        'videoPackage',
        'businessPackage',
        'utilityPackage',
        'subViews'
    ];
    
    const analysis = [];
    
    for (const packageName of subPackages) {
        const packagePath = path.join(BUILD_DIR, packageName);
        if (fs.existsSync(packagePath)) {
            const size = getDirSize(packagePath);
            analysis.push({
                name: packageName,
                size: size,
                isOverTarget: size > TARGET_SUBPACKAGE_SIZE,
                targetSize: TARGET_SUBPACKAGE_SIZE
            });
        }
    }
    
    return analysis.sort((a, b) => b.size - a.size);
}

/**
 * 分析组件分布
 */
function analyzeComponents() {
    const componentsDir = path.join(BUILD_DIR, 'components');
    if (!fs.existsSync(componentsDir)) {
        return [];
    }
    
    const components = fs.readdirSync(componentsDir, { withFileTypes: true })
        .filter(item => item.isDirectory())
        .map(item => {
            const componentPath = path.join(componentsDir, item.name);
            return {
                name: item.name,
                size: getDirSize(componentPath)
            };
        })
        .sort((a, b) => b.size - a.size);
    
    return components;
}

/**
 * 生成优化建议
 */
function generateOptimizationSuggestions(mainPackage, subPackages, components) {
    const suggestions = [];
    
    // 主包体积建议
    if (mainPackage.isOverTarget) {
        const excessSize = mainPackage.totalSize - mainPackage.targetSize;
        suggestions.push({
            type: 'critical',
            title: '主包体积超标',
            description: `当前主包体积 ${(mainPackage.totalSize/1024).toFixed(2)}MB，超出目标 ${(excessSize/1024).toFixed(2)}MB`,
            actions: [
                '将更多组件移动到分包',
                '压缩图片资源',
                '移除未使用的代码',
                '优化第三方库引用'
            ]
        });
    }
    
    // 组件优化建议
    const largeComponents = components.filter(c => c.size > 50);
    if (largeComponents.length > 0) {
        suggestions.push({
            type: 'warning',
            title: '大体积组件优化',
            description: `发现 ${largeComponents.length} 个大体积组件（>50KB）`,
            actions: largeComponents.map(c => `优化组件 ${c.name} (${c.size}KB)`)
        });
    }
    
    // 分包建议
    const oversizedSubPackages = subPackages.filter(p => p.isOverTarget);
    if (oversizedSubPackages.length > 0) {
        suggestions.push({
            type: 'warning',
            title: '分包体积过大',
            description: `${oversizedSubPackages.length} 个分包超过2MB限制`,
            actions: oversizedSubPackages.map(p => `拆分分包 ${p.name} (${(p.size/1024).toFixed(2)}MB)`)
        });
    }
    
    return suggestions;
}

/**
 * 生成报告
 */
function generateReport() {
    console.log('🔍 开始分析小程序包体积...\n');
    
    const mainPackage = analyzeMainPackage();
    const subPackages = analyzeSubPackages();
    const components = analyzeComponents();
    const suggestions = generateOptimizationSuggestions(mainPackage, subPackages, components);
    
    // 主包分析报告
    console.log('📦 主包体积分析');
    console.log('='.repeat(50));
    console.log(`总体积: ${(mainPackage.totalSize/1024).toFixed(2)}MB`);
    console.log(`目标体积: ${(mainPackage.targetSize/1024).toFixed(2)}MB`);
    console.log(`状态: ${mainPackage.isOverTarget ? '❌ 超标' : '✅ 达标'}`);
    console.log('\n详细分布:');
    
    mainPackage.items.forEach(item => {
        const sizeStr = item.size > 1024 
            ? `${(item.size/1024).toFixed(2)}MB` 
            : `${item.size}KB`;
        console.log(`  ${item.name.padEnd(20)} ${sizeStr.padStart(10)} (${item.percentage}%)`);
    });
    
    // 分包分析报告
    console.log('\n📦 分包体积分析');
    console.log('='.repeat(50));
    subPackages.forEach(pkg => {
        const sizeStr = pkg.size > 1024 
            ? `${(pkg.size/1024).toFixed(2)}MB` 
            : `${pkg.size}KB`;
        const status = pkg.isOverTarget ? '❌' : '✅';
        console.log(`  ${pkg.name.padEnd(20)} ${sizeStr.padStart(10)} ${status}`);
    });
    
    // 组件分析报告
    console.log('\n🧩 大体积组件分析 (Top 10)');
    console.log('='.repeat(50));
    components.slice(0, 10).forEach(comp => {
        console.log(`  ${comp.name.padEnd(30)} ${comp.size}KB`);
    });
    
    // 优化建议
    console.log('\n💡 优化建议');
    console.log('='.repeat(50));
    if (suggestions.length === 0) {
        console.log('✅ 当前配置已达到最佳状态！');
    } else {
        suggestions.forEach((suggestion, index) => {
            const icon = suggestion.type === 'critical' ? '🚨' : '⚠️';
            console.log(`${icon} ${suggestion.title}`);
            console.log(`   ${suggestion.description}`);
            suggestion.actions.forEach(action => {
                console.log(`   • ${action}`);
            });
            console.log('');
        });
    }
    
    // 总结
    const totalAppSize = mainPackage.totalSize + subPackages.reduce((sum, pkg) => sum + pkg.size, 0);
    console.log('\n📊 总体统计');
    console.log('='.repeat(50));
    console.log(`应用总体积: ${(totalAppSize/1024).toFixed(2)}MB`);
    console.log(`主包占比: ${((mainPackage.totalSize/totalAppSize)*100).toFixed(1)}%`);
    console.log(`分包数量: ${subPackages.length}`);
    console.log(`组件数量: ${components.length}`);
    
    return {
        mainPackage,
        subPackages,
        components,
        suggestions,
        totalSize: totalAppSize
    };
}

// 运行分析
if (require.main === module) {
    try {
        const report = generateReport();
        
        // 保存详细报告到文件
        const reportData = {
            timestamp: new Date().toISOString(),
            ...report
        };
        
        fs.writeFileSync('bundle-analysis-report.json', JSON.stringify(reportData, null, 2));
        console.log('\n📄 详细报告已保存到 bundle-analysis-report.json');
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
        process.exit(1);
    }
}

module.exports = { generateReport, analyzeMainPackage, analyzeSubPackages };
