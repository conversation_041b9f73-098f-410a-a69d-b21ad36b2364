#!/usr/bin/env node

/**
 * 修复错误码 6000100 的快速脚本
 * 主要解决 "unbind download url" 相关问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复错误码 6000100...\n');

// 1. 修复 project.config.json 配置
function fixProjectConfig() {
    const configPath = 'dist/build/mp-weixin/project.config.json';
    
    if (!fs.existsSync(configPath)) {
        console.log('❌ 未找到 project.config.json，请先执行构建');
        return false;
    }
    
    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        // 临时关闭 URL 检查（开发阶段）
        config.setting.urlCheck = false;
        
        // 确保其他设置正确
        config.setting.es6 = true;
        config.setting.enhance = true;
        config.setting.postcss = true;
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        console.log('✅ 已修复 project.config.json 配置');
        return true;
    } catch (error) {
        console.log('❌ 修复 project.config.json 失败:', error.message);
        return false;
    }
}

// 2. 检查必要文件
function checkRequiredFiles() {
    const requiredFiles = [
        'dist/build/mp-weixin/app.js',
        'dist/build/mp-weixin/app.json',
        'dist/build/mp-weixin/app.wxss'
    ];
    
    let allExists = true;
    
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file} 存在`);
        } else {
            console.log(`❌ ${file} 缺失`);
            allExists = false;
        }
    });
    
    return allExists;
}

// 3. 检查静态资源
function checkStaticResources() {
    const staticDir = 'dist/build/mp-weixin/static';
    
    if (!fs.existsSync(staticDir)) {
        console.log('❌ static 目录不存在');
        return false;
    }
    
    // 检查 TabBar 图标
    const tabBarIcons = [
        'basic.png',
        'basic_active.png',
        'home.png', 
        'home_active.png',
        'send.png',
        'send_active.png',
        'risk.png',
        'risk_active.png',
        'account.png',
        'account_active.png'
    ];
    
    let allIconsExist = true;
    
    tabBarIcons.forEach(icon => {
        const iconPath = path.join(staticDir, icon);
        if (fs.existsSync(iconPath)) {
            console.log(`✅ TabBar 图标 ${icon} 存在`);
        } else {
            console.log(`❌ TabBar 图标 ${icon} 缺失`);
            allIconsExist = false;
        }
    });
    
    return allIconsExist;
}

// 4. 生成开发者工具导入指南
function generateImportGuide() {
    const guide = `
# 🚀 微信开发者工具导入指南

## 解决错误码 6000100 的步骤

### 1. 更新开发者工具
- 确保使用最新版本的微信开发者工具
- 建议版本: >= 1.06.2307260

### 2. 导入项目
1. 打开微信开发者工具
2. 选择 "导入项目"
3. 项目目录选择: \`${path.resolve('dist/build/mp-weixin')}\`
4. AppID: wx211c5f8feae8d939 (测试环境)

### 3. 开发设置
- ✅ 已关闭 URL 校验 (urlCheck: false)
- ✅ 已启用 ES6 转 ES5
- ✅ 已启用样式补全

### 4. 如果仍有问题
1. 清除缓存: 工具 → 构建npm → 清除缓存
2. 重新编译: 项目 → 重新编译
3. 重启开发者工具

### 5. 网络问题排查
- 检查网络连接
- 关闭代理软件
- 尝试切换网络环境

## 📞 技术支持
如问题仍然存在，请查看 TROUBLESHOOTING-6000100.md 文档
`;
    
    fs.writeFileSync('IMPORT-GUIDE.md', guide);
    console.log('✅ 已生成导入指南: IMPORT-GUIDE.md');
}

// 5. 主修复流程
function main() {
    console.log('📋 检查项目状态...\n');
    
    // 检查构建产物
    if (!fs.existsSync('dist/build/mp-weixin')) {
        console.log('❌ 构建目录不存在，请先执行构建:');
        console.log('   npm run build:mp-weixin\n');
        return;
    }
    
    // 执行修复步骤
    const steps = [
        { name: '修复项目配置', func: fixProjectConfig },
        { name: '检查必要文件', func: checkRequiredFiles },
        { name: '检查静态资源', func: checkStaticResources }
    ];
    
    let allSuccess = true;
    
    steps.forEach((step, index) => {
        console.log(`\n[${index + 1}/${steps.length}] ${step.name}...`);
        const success = step.func();
        if (!success) {
            allSuccess = false;
        }
    });
    
    // 生成指南
    generateImportGuide();
    
    // 输出结果
    console.log('\n' + '='.repeat(50));
    if (allSuccess) {
        console.log('🎉 修复完成！');
        console.log('\n📋 下一步操作:');
        console.log('1. 打开微信开发者工具');
        console.log('2. 导入项目目录: dist/build/mp-weixin');
        console.log('3. 如果仍有问题，查看 TROUBLESHOOTING-6000100.md');
    } else {
        console.log('⚠️  修复过程中发现问题，请检查上述错误信息');
        console.log('💡 建议重新执行构建: npm run build:mp-weixin');
    }
    console.log('='.repeat(50));
}

// 运行修复
if (require.main === module) {
    main();
}

module.exports = { fixProjectConfig, checkRequiredFiles, checkStaticResources };
