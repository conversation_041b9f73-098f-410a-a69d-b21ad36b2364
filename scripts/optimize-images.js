#!/usr/bin/env node

/**
 * 图片压缩优化脚本
 * 使用TinyPNG API进行图片压缩，目标压缩率≥60%
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// TinyPNG API配置
const TINYPNG_API_KEY = 'YOUR_API_KEY'; // 需要替换为实际的API Key
const TINYPNG_API_URL = 'https://api.tinify.com/shrink';

// 需要压缩的图片目录
const IMAGE_DIRS = [
    'src/assets/images',
    'src/static'
];

// 支持的图片格式
const SUPPORTED_FORMATS = ['.png', '.jpg', '.jpeg'];

// 压缩统计
let stats = {
    total: 0,
    compressed: 0,
    originalSize: 0,
    compressedSize: 0,
    errors: 0
};

/**
 * 获取所有需要压缩的图片文件
 */
function getAllImageFiles() {
    const imageFiles = [];
    
    IMAGE_DIRS.forEach(dir => {
        if (fs.existsSync(dir)) {
            const files = fs.readdirSync(dir, { recursive: true });
            files.forEach(file => {
                const filePath = path.join(dir, file);
                const ext = path.extname(file).toLowerCase();
                
                if (SUPPORTED_FORMATS.includes(ext) && fs.statSync(filePath).isFile()) {
                    imageFiles.push(filePath);
                }
            });
        }
    });
    
    return imageFiles;
}

/**
 * 使用TinyPNG API压缩图片
 */
function compressImage(filePath) {
    return new Promise((resolve, reject) => {
        const originalSize = fs.statSync(filePath).size;
        const imageData = fs.readFileSync(filePath);
        
        const options = {
            method: 'POST',
            hostname: 'api.tinify.com',
            path: '/shrink',
            auth: `api:${TINYPNG_API_KEY}`,
            headers: {
                'Content-Type': 'application/octet-stream',
                'Content-Length': imageData.length
            }
        };
        
        const req = https.request(options, (res) => {
            if (res.statusCode === 201) {
                // 获取压缩后的图片URL
                const compressedUrl = res.headers.location;
                
                // 下载压缩后的图片
                https.get(compressedUrl, (downloadRes) => {
                    const chunks = [];
                    
                    downloadRes.on('data', (chunk) => {
                        chunks.push(chunk);
                    });
                    
                    downloadRes.on('end', () => {
                        const compressedData = Buffer.concat(chunks);
                        const compressedSize = compressedData.length;
                        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
                        
                        // 只有压缩率≥60%才保存
                        if (compressionRatio >= 60) {
                            fs.writeFileSync(filePath, compressedData);
                            
                            stats.compressed++;
                            stats.originalSize += originalSize;
                            stats.compressedSize += compressedSize;
                            
                            console.log(`✅ ${filePath}: ${(originalSize/1024).toFixed(2)}KB → ${(compressedSize/1024).toFixed(2)}KB (${compressionRatio}%)`);
                            resolve({ success: true, originalSize, compressedSize, compressionRatio });
                        } else {
                            console.log(`⚠️  ${filePath}: 压缩率${compressionRatio}%不足60%，跳过`);
                            resolve({ success: false, reason: 'insufficient_compression' });
                        }
                    });
                }).on('error', reject);
            } else {
                reject(new Error(`TinyPNG API错误: ${res.statusCode}`));
            }
        });
        
        req.on('error', reject);
        req.write(imageData);
        req.end();
    });
}

/**
 * 本地图片压缩（备用方案）
 * 使用简单的质量调整
 */
function compressImageLocally(filePath) {
    // 这里可以集成其他本地压缩库
    // 如sharp、jimp等
    console.log(`📦 本地压缩: ${filePath}`);
    return Promise.resolve({ success: true, originalSize: 0, compressedSize: 0 });
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始图片压缩优化...\n');
    
    const imageFiles = getAllImageFiles();
    stats.total = imageFiles.length;
    
    console.log(`📊 找到 ${stats.total} 个图片文件\n`);
    
    // 检查API Key
    if (!TINYPNG_API_KEY || TINYPNG_API_KEY === 'YOUR_API_KEY') {
        console.log('⚠️  未配置TinyPNG API Key，使用本地压缩方案\n');
        
        for (const filePath of imageFiles) {
            try {
                await compressImageLocally(filePath);
            } catch (error) {
                console.error(`❌ ${filePath}: ${error.message}`);
                stats.errors++;
            }
        }
    } else {
        // 使用TinyPNG API
        for (const filePath of imageFiles) {
            try {
                await compressImage(filePath);
                // 添加延迟避免API限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ ${filePath}: ${error.message}`);
                stats.errors++;
                
                // API失败时使用本地压缩
                try {
                    await compressImageLocally(filePath);
                } catch (localError) {
                    console.error(`❌ 本地压缩也失败: ${localError.message}`);
                }
            }
        }
    }
    
    // 输出统计结果
    console.log('\n📈 压缩统计:');
    console.log(`总文件数: ${stats.total}`);
    console.log(`成功压缩: ${stats.compressed}`);
    console.log(`错误数量: ${stats.errors}`);
    
    if (stats.compressed > 0) {
        const totalCompressionRatio = ((stats.originalSize - stats.compressedSize) / stats.originalSize * 100).toFixed(2);
        const sizeSaved = ((stats.originalSize - stats.compressedSize) / 1024).toFixed(2);
        
        console.log(`原始大小: ${(stats.originalSize / 1024).toFixed(2)}KB`);
        console.log(`压缩后大小: ${(stats.compressedSize / 1024).toFixed(2)}KB`);
        console.log(`总压缩率: ${totalCompressionRatio}%`);
        console.log(`节省空间: ${sizeSaved}KB`);
    }
    
    console.log('\n✨ 图片压缩完成！');
}

// 运行脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { compressImage, getAllImageFiles };
