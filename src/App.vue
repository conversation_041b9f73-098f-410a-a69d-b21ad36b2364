<script>
import { mapState, mapActions, mapMutations } from 'vuex';

export default {
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
    },
    onLaunch: function(data) {
        console.log('App launch', data);
        this.checkWXUpdate();
    },
    onShow: function(data) {
        console.log('App Show', data);
        // #ifdef MP-WEIXIN
        const { utmSource = '', fromPage = '' } = data.query;
        this.$point('enter_applet', {
            utm_source: utmSource,
            biz_id: '',
        });
        if (fromPage) {
            this.$point('from_page_event', {
                form_page: fromPage,
            });
        }
        // #endif
    },
    onHide: function() {
        console.log('App Hide');
    },
    methods: {
        ...mapActions('user', ['getHeadInfo']),
        ...mapMutations('send', ['setCurEntId']),
        checkWXUpdate() {
            if (uni.canIUse('getUpdateManager')) {
                const updateManager = uni.getUpdateManager();
                updateManager.onCheckForUpdate(function(res) {
                    // 请求完新版本信息的回调
                    console.log('has new version:' + res.hasUpdate);
                    if (res.hasUpdate) {
                        updateManager.onUpdateReady(function() {
                            uni.showModal({
                                title: '更新提示',
                                content: '新版本已经准备好，是否重启应用？',
                                success(res) {
                                    if (res.confirm) {
                                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                                        updateManager.applyUpdate();
                                    }
                                },
                            });
                        });

                        updateManager.onUpdateFailed(function() {
                            // 新的版本下载失败
                            uni.showModal({
                                title: '已经有新版本了哟~',
                                content: '新版本已经上线啦~，请您删除当前小程序，重新扫码小程序码打开哟~',
                            });
                        });
                    }
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: '当前客户端版本过低，无法使用该功能，请升级到最新客户端版本后重试。',
                });
            }
        },
    },
};
</script>

<style lang="scss">
	/*每个页面公共css */
    page {
        height: 100%
    }
</style>
