import Vue from 'vue';
import CryptoJS from 'crypto-js';
import regRules from 'utils/regs.js';
import store from 'store/index.js';

/**
 * @description: 获取图形验证码
 */
export function getImageVerifyImg() {
    return Vue.$http.get('/users/ignore/captcha/base64-image');
}
/**
 * @description: 获取验证码
 */
export function getVerifyCode(account, imageVerifyCode, imageKey, type = 1, code = 'B001', bizTargetKey = '') {
    const captchaVerifyParam = store.state.captchaVerifyParam;
    const pathMap = {
        1: '/users/ignore/v2/captcha/notice',
        2: '/users/captcha/notice',
    };
    // 使用AES对'{code}:{target}'数据进行加密，目的增加中间人修改手机号频繁调接口的难度
    // mod,和padding和后端约定好
    const AES_ENCRYPT_KEY = '44eb2e88a84656e756ec397bd2f18a7d';
    const key = CryptoJS.enc.Utf8.parse(AES_ENCRYPT_KEY);
    const timestamp = Date.parse(new Date());
    const encryptStr = `${code}:${account}:${bizTargetKey || ''}:${timestamp}`;
    const encryptToken = CryptoJS.AES.encrypt(encryptStr, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    }).toString();
    const encodeToken = encodeURIComponent(encryptToken);
    // 需要输入图形验证码，在header里面添加对应参数
    let headersObj = {};
    if (imageVerifyCode !== '' && imageKey !== '') {
        headersObj = {
            'additionalImgVerCode': JSON.stringify({
                'imageCode': imageVerifyCode,
                'imageKey': imageKey,
            }),
        };
    }
    if (captchaVerifyParam) {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalAfs: captchaVerifyParam,
        };
    }
    return Vue.$http.request(`${pathMap[type]}?encryptToken=${encodeToken}`, {
        target: account,
        code,
        bizTargetKey: '',
        sendType: regRules.userPhone.test(account) ? 'S' : 'E',
    }, {
        method: 'POST',
        headers: headersObj,
    });
}
/**
 * @description: 获取绑定状态
 */
export function getBindingStatus() {
    return Vue.$http.request('/users/applets/login-status2', {}, { noToast: true });
}
/**
 * @description: 获取登录状态
 */
export function getLoginStatus() {
    return Vue.$http.request('/users/login-status', {}, { noToast: true });
}
/**
 * @description: 根据微信登录code登录上上签
 */
export function wxCodeLogin(code, thirdCorpId = '') {
    return Vue.$http.post(`/auth-center/user/applets/get-inner-binding-user-token?code=${code}&sourceType=${store.getters.mpSourceType}&thirdCorpId=${thirdCorpId}`);
}
/**
 * @description: 根据企业微信登录code登录上上签
 */
export function weWorkCodeLogin(code) {
    return Vue.$http.post(`/auth-center/user/applets/get-binding-user-token?code=${code}&sourceType=${store.getters.mpSourceType}`);
}
/**
 * @description: 根据微信登录code登录上上签
 */
export function ssqLogin(data) {
    return Vue.$http.post('/auth-center/user/applets/third-inner-binding-user', data);
}
/**
 * @description: 根据企业微信登录code登录上上签
 */
export function weWorkSsqLogin(data) {
    console.log(data);
    return Vue.$http.post('/auth-center/user/applets/third-binding-user', data);
}
/**
 * @description: 获取账号信息
 */
export function getAccountInfo() {
    return Vue.$http.get('/users/detail');
}
/**
 * @description: 解绑登出
 */
export function logout() {
    return Vue.$http.post('/users/applets/untied-third-user-binding', {
        sourceType: store.getters.mpSourceType,
    });
}
/**
 * @description: 获取微信手机号
 */
export function wxQuickLogin(data) {
    return Vue.$http.post('/users/ignore/applets/quick-login', data);
}

/**
 * @description: 获取微信手机号
 */
export function getSSQAccountByWxId(data) {
    return Vue.$http.post('/ents/employees/receivers-third', data);
}

/**
 * @description: 给没有绑定的用户发微信小程序消息
 */
export function postWxMsg(data) {
    return Vue.$http.post('/contract-api/qywx/push-invitation', data);
}

/**
 * @description: 获取个人实名认证证书信息
 */
export function getPersonCaCertInfo() {
    return Vue.$http.get('/users/auth/cacert');
}

/**
 * @description: 获取企业实名认证证书信息
 */
export function getEntCaCertInfo() {
    return Vue.$http.get('/ents/auth/cacert');
}

/**
 * @description: 存储user相关信息
 */
export function postUserConfig(key, value) {
    return Vue.$http.post(`/users/configs/${key}`, {
        name: key,
        value,
    });
}

/**
 * @description: 查询user相关信息
 */
export function getUserConfigByKey(key) {
    return Vue.$http.get(`/users/configs/${key}`);
}

/**
 * 获取用户是否存在签约密码
 */
export function getSignPwdExist() {
    return Vue.$http.get(`/users/sign-pwd/exist`);
}

/**
 * 获取用户是否存在签约密码
 */
export function getAlipayUserInfo(code, thirdCorpId = '') {
    return Vue.$http.get(`/auth-center/user/applets/auth-summary?code=${code}&sourceType=${store.getters.mpSourceType}&thirdCorpId=${thirdCorpId}`);
}

/**
 * 获取用户是否可以参加周年庆活动
 */
export function getAnniversaryInfo() {
    return Vue.$http.get(`/ents/query-anniversary-ent`);
}

