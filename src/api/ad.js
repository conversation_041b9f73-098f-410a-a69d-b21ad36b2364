import Vue from 'vue';
/**
 * 判断当前用户是否是黑名单用户，黑名单用户不展示广告位
 */
export function checkBlackAd() {
    return Vue.$http.post('/ad-api/black/check');
}
/**
 * 增加定制广告位点击记录
 */
export function addAdRecord({ adId = 0, adPageAddress = '', click = false }) {
    return Vue.$http.post('/ad-api/record/add', {
        adId,
        adPageAddress,
        click,
    });
}

/**
 * 获取广告信息
 */
export function getAdInfo({ scene = '', deliveryChannel = 0 }) {
    return Vue.$http.get(`/ad-api/plan/query?scene=${scene}&deliveryChannel=${deliveryChannel}`);
}
