import Vue from 'vue';
/**
 * @description: 获取该账号所在的企业列表
 */
export function getAccountEntList({ userAccount })  {
    return Vue.$http.get(`ents/employees/receiver?account=${userAccount}`);
}
/**
 * @description: 获取该账号列表
 */
export function getAccountPersonList({ userAccount })  {
    return Vue.$http.get(`/users/receiver?account=${userAccount}`);
}

/**
 * @description: 获取企业自定义的组织架构
 */
export function getEntDept()  {
    return Vue.$http.get(`ents/depts`);
}

/**
 * @description: 获取组织架构下某一级的所有成员(包含该级的下级)（包含不可用的成员）
 */

export function getEmployees({ value }) {
    return Vue.$http.get(`ents/depts/${value}/out-employees`);
}

/**
 * @description: 获取所有的联系人分类
 */

export function getGroup() {
    return Vue.$http.get(`ents/contacts/out-groups`);
}

/**
 * @description: 获取当前分类的联系人
 */

export function getGroupMember({ value, sharedGroup }) {
    return Vue.$http.get(`ents/contacts/out-groups/${value}?isSharedGroup=${sharedGroup}`);
}
