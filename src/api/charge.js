import Vue from 'vue';
/**
 * @description: 获取计费数据
 */
export const getChargingData = (url) => {
    return Vue.$http.get(url);
};

/**
 * @description: 获取合同可用份数
 */
export const getWallet = ({ isNewGroup }) => {
    return Vue.$http.get(`/ents/charging/wallet?isNewGroup=${isNewGroup}`, {}, { noToast: true });
};
/**
 * @description: 获取充值套餐详情
 */
export const checkEntChannel = () => {
    return Vue.$http.get(`/ents/channel/check-ent-channel`);
};

/**
 * @description: 获取充值套餐详情
 */
export const getChargePackage = ({ entChannel, registerChannel, contractId }) => {
    return Vue.$http.get(`/ents/ignore/charging/package?entChannel=${entChannel}&registerChannel=${registerChannel}&contractId=${contractId}`);
};

/**
 * @description: 获取限购套餐购买记录
 */
export const getLimitPackageChargeInfo = () => {
    return Vue.$http.get(`/ents/charging/check-first-purchase-package`);
};

/**
 * @description: 获取充值套餐详情
 */
export const getPackageChannel = ({ entChannel, registerChannel, contractId }) => {
    return Vue.$http.get(`/ents/charging/package/channel?entChannel=${entChannel}&registerChannel=${registerChannel}&contractId=${contractId}`);
};

/**
 * @description: 获取订单编号
 */
export const getOrderId = ({ id, contractId, contractNum }) => {
    return Vue.$http.post(`/ents/charging/package/${id}/ordering?contractId=${contractId}${contractNum ? `&amount=${contractNum}` : ''}`);
};

/**
 * @description: 获取微信支付参数
 */
export const getWeChatPayParam = ({ orderId, sourceType }) => {
    return Vue.$http.post(`/ents/wechat/applet-payment/${orderId}?sourceType=${sourceType}`);
};
/**
 * @description: 生成合同订单
 */
export const getAlipayPayParam = ({ orderId, sourceType }) => {
    return Vue.$http.post(`/ents/alipay/applet-payment/${orderId}?sourceType=${sourceType}`);
};
/**
 * @description: 获取可充值高级功能列表
 */
export const getAdvancedFeatures = () => {
    return Vue.$http.get('/ents/charging/advancedFeatures');
};
/**
 * @description: 生成高级功能订单
 */
export const getFeaturesOrderId = (id, isNewGroup) => {
    return Vue.$http.post('/ents/charging/advancedFeatures/ordering', { ids: [id], isNewGroup });
};

/**
 * @description: 获取可充值ai功能列表
 */
export const getAiFeatures = () => {
    return Vue.$http.get('/ents/ignore/charging/ai-combination-package');
};

/**
 * @description: 生成ai功能订单
 */
export const getAiOrderId = (id) => {
    return Vue.$http.post(`/ents/charging/aiCombinationPackage/${id}/ordering`);
};
