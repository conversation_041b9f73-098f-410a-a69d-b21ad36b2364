import Vue from 'vue';
/**
 * @description: 获取合同文件夹
 */
export function getShortcuts() {
    return Vue.$http.get('/contract-center-bearing/shortcuts?queryCount=true');
}
/**
 * @description: 获取合同份数
 */
export function getContractCount() {
    return Vue.$http.get('/contract-api/contracts/count');
}
/**
 * @description: 获取所有合同
 */
export function getAllContractList({ searchEntryParams, currentPage = 1, pageSize = 10,
    extraParams = {} }) {
    return Vue.$http.post('/contract-search/web/search', {
        pageRequest: {
            currentPage,
            pageSize,
        },
        searchEntryParams,
        extraParams,
    });
}
/**
 * @desc:获取列表配置
 * */
export function getTableListConfigs(pageIndex = 1, pageSize = 200) {
    return Vue.$http.get(`/contract-center-bearing/search-contracts/all-fields-list?pageIndex=${pageIndex}&pageSize=${pageSize}`);
}
/**
 * @description: 根据菜单类型获取合同列表
 */
export function getContractList(searchParams, pageIndex = 1, pageSize = 10) {
    return Vue.$http.post('/contract-api/contracts/search', {
        ...searchParams,
        pageIndex,
        pageSize,
    });
}

/**
 * @description: 根据合同id list获取合同的tag信息
 */
export function getContractTags(contractIds, searchEntryParams) {
    return Vue.$http.post('/contract-center-bearing/tags-operations', {
        platform: 'WX_APPLET',
        contractIds,
        contractSourceSubject: [],
        searchEntryParams,
    });
}

/**
 * @desc 批量签署需要实名
 * @param { Object } params 所需参数
 * @param { String } params.batchSignId 签署id
 */
export function getNeedAuthenticationListAjax(params) {
    return Vue.$http.post('/contract-api/contracts/query-unsatisfied-contracts', params);
}

/**
 * @desc 批量签署存储合同id列表
 * @param { String } params.batchSignId 批量签署id
 * @param { String[] } params.contractIds 批量签署的合同id
 */
export function saveBatchSignContractIds(params) {
    return Vue.$http.post('/contract-api/contracts/save-batch-sign-contractIds', params);
}

/**
 * @desc 批量签署
 * @param { String[] } contractIds 批量签署的合同id
 */
export function batchSignAjax(contractIds) {
    return Vue.$http.post('/contract-api/contracts/batch-sign-filter-v2', contractIds);
}

/**
 * @desc 批量认领
 * @param { String[] } contractIds 批量认领的合同id
 */
export function claimBatchAjax(contractIds) {
    return Vue.$http.post('/contract-api/contracts-claim/batch-in-web', {
        contractIds,
    });
}
