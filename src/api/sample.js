import Vue from 'vue';

/**
 * @desc 获取所有的范本数据
 * @return Array [{}]
 * */
export function getAllSampleList() {
    return Vue.$http.get('/web/contract-sample/sample/basic/query-samples-preview');
}

/**
 * @desc 获取范本的详情数据
 * @param {string} sampleId 范本id
 * @return Object {}
 * */
export function getSampleDetailById(sampleId) {
    return Vue.$http.get(`/web/contract-sample/sample/${sampleId}/detail`);
}

/**
 * @desc 发送范本合同
 * @param {string} sampleId 范本id
 * @param {Object} sampleInfo 字段数据
 * */
export function postSampleInfoAndSend(sampleId, sampleInfo) {
    return Vue.$http.post(`/web/contract-sample/sample/send-contract`, { ...sampleInfo }, { noToast: true });
}
