import Vue from 'vue';
/**
 * @description: 切换主体
 */
export function switchEnt(entId)  {
    return Vue.$http.post('/authenticated/switch-ent', {
        entId: entId,
        refreshToken: uni.getStorageSync('refreshToken'),
    });
}

/**
 * @description: 获取所有企业
 */
export function getAllEntList() {
    return Vue.$http.get('/users/chosen-enterprises');
}
/**
 * @description: 生成合同id
 */
export function createContractId()  {
    return Vue.$http.post('/contract-api/contracts', {}, { noToast: true });
}

/**
 * @description: 生成合同文档
 */

export function genDocument({ contractId, fileList }) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}/gen-document`, fileList);
}

/**
 * @description: 删除当前个人签署方
 */
export function deleteReceiver(contractId, receiverId)  {
    return Vue.$http.delete(`/contract-api/contracts/${contractId}/receivers/${receiverId}`);
}
/**
 * @description: 更新label信息
 */

export function updateLabel({ contractId, newMark }) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}/labels/create-and-modify/`, [newMark]);
}

/**
 * @description: 删除label信息
 */

export function deleteLabel({ contractId, labelId }) {
    return Vue.$http.delete(`/contract-api/contracts/${contractId}/labels/${labelId}`);
}

/**
 * @description: 获取合同类型
 */

export function getContractType() {
    return Vue.$http.get(`/contract-api/contract-types`);
}

/**
 * @description: 获取合同信息
 */

export function getContractInfo({ contractId }) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}`);
}

/**
 * @description: 更新合同信息
 */

export function postContractInfo({ contractId, param }) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}`, param);
}

/**
 * @description: 发送合同
 */
export const postSendContract = (url, data) => {
    return Vue.$http.post(url, data);
};

/**
 * @description: 获取本地发送时receiver信息
 */
export function getReceiverList({ contractId }) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/receivers?isOnlyNeedSigner=1&displayRuleName=true`);
}
/**
 * @description: 获取本地发送时文档信息
 */
export function getDocList({ contractId }) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/documents?isGenerateDocumentImages=1&showLabels=1`);
}

/**
 * @desc 删除label信息
 * */
export function deleteMark({ draftId = '', labelId = '' }) {
    return Vue.$http.delete(`/contract-api/contracts/${draftId}/labels/${labelId}`);
}

/**
 * @desc 无模板时直接生成draftId
 * */
export function createDraftId() {
    return Vue.$http.post('/template-api/v2/draft/V2/init-single-with-blank-template');
}

/**
 * @desc 获取是否可以创建模板的权限
 * */
export function getCreateTemplatePermission(ifHybridServer) {
    return Vue.$http.post(`/template-api/v2/templates/?systemType=${ifHybridServer ? 'HYBRID_CLOUD' : 'ALIYUN_FINANCE'}`, { ofdTemplate: false }, { noToast: true });
}
