import Vue from 'vue';
/**
 * @description: 获取模板列表
 */
export function getTemplateList({ filterType, pageIndex, pageSize, templateCategory, permissionType, createEntId }) {
    return Vue.$http.post('/template-api/v2/templates/list-filter', {
        filterType,
        pageIndex,
        pageSize,
        templateCategory,
        permissionType,
        createEntId,
    });
}

/**
 * @description: 获取模板对应的文档列表
 */
export function getTemplateDocList({ templateId }) {
    return Vue.$http.get(`/template-api/v2/draft/find-template/${templateId}`);
}

/**
 * @description: 获得草稿对应的文档列表
 */
export function getDraftDocumentsInfo({ draftId }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/document`);
}

/**
 * @description: 获取模板使用阶段文档链接
 */
export function getTemplatePreviewInfo({ templateId, documentId, documentFederationId = '' }) {
    return Vue.$http.post(`/template-api/v2/templates/${templateId}/document-preview-urls`, {
        templateId: templateId,
        documentId,
        documentFederationId,
    });
}
/**
 * @description: 获取空白模板使用阶段文档链接
 */
export function getDraftPreviewInfo({ draftId = '', documentId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/document/${documentId}/document-preview-urls`);
}

/**
 * @description: 生成draftId
 */
export function getDraftId({ templateId = '', editDocumentIds = [], batchSend = false, bizType = '', documentFederationId = '', relationContractIds = [] }) {
    return Vue.$http.post('/template-api/v2/draft/V2/init-single', {
        editDocumentIds,
        templateId,
        batchSend,
        bizType,
        documentFederationId,
        relationContractIds,
    });
}

/**
 * @description: 获取合同类型
 */
export function getContractTypes() {
    return Vue.$http.get(`/contract-api/contract-types/`);
}

/**
 * 使用模板时，查询及保存模板文档及字段信息
 * @param draftId String 使用阶段生成的id
 * @param documentBasicInfos [Object]Array 文档信息
 */
export function postDraftDocumentsInfo({ draftId = '', documentBasicInfos = [] }) {
    return Vue.$http.post(`/template-api/v2/draft/${draftId}/save-document`, {
        documentBasicInfos,
    });
}

/**
 * @description: 删除草稿文档
 */
export function deleteDraftDoc({ draftId = '',  documentId = '' }) {
    return Vue.$http.delete(`/template-api/v2/draft/${draftId}/document/${documentId}`);
}

/**
 * @desc 获取发件方信息，内部、外部可选择发件方
 * @param {String} draftId 草稿id
 * @return {Object} data:{ existProxySendAuth: Boolean, senders: Array, outSenders: Array}
 * */
export function getDraftSenders({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/senders`);
}

/**
 * @desc 获取签署方默认配置
 * */
export function getReceiverDefaultConfig() {
    return Vue.$http.get(`/template-api/v2/templates/receiver-default-config`);
}

/**
 * @desc 获取草稿通知用合同标题
 * @param {String} draftId 草稿id
 * @return {Object} data:{ templateName: String, singleDocumentFlag: Boolean }
 * */
export function getDraftNoticeContractName({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/V2/${draftId}/template`);
}

/**
 * @desc 获取是否开启了阅读完毕再签署
 * */
export function getReadBeforeSignConfig() {
    return Vue.$http.get(`/ents/configs/read-before-sign-config`);
}
/**
 * @desc 获取草稿签署人信息
 * @param {String} draftId 草稿id
 * @return {Object} data:{ signOrderly: Boolean, senderInfo: Object, receiverInfos: Array}
 * */
export function getDraftReceivers({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/receiver`);
}

/**
 * @desc 保存草稿签署人信息
 */
export function saveDraftReceiverInfo({ draftId, senderInfo = {}, contractName, signOrderly, receiverInfos, proxySend }) {
    return Vue.$http.post(`/template-api/v2/draft/${draftId}/save-receiver`, {
        senderInfo,
        contractName,
        signOrderly,
        receiverInfos,
        proxySend,
    }, {
        noToast: 1,
    });
}

/**
 * @desc 获取模板发送文档列表和label信息
 * */
export function getDocListAndLabels({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/label-view`);
}

/**
 * @desc 获取模板发送文档列表和label信息
 * */
export function saveLabel({ draftId = '', label = {} }) {
    return Vue.$http.post(`/template-api/v2/draft/${draftId}/save-label`, label);
}

/**
 * @desc 删除label信息
 * */
export function deleteLabel({ draftId = '', labelId = '' }) {
    return Vue.$http.delete(`/template-api/v2/draft/${draftId}/label/${labelId}`);
}

/**
 * @desc 删除label信息
 * */
export function submitWorkflow({ draftId = '', workflows = [] }) {
    return Vue.$http.post(`/template-api/v2/draft/${draftId}/submit-workflow`, { workflows: workflows });
}

/**
 * @desc 获取模板是否开启FDA功能
 * */
export function getFdaIfOpened({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/custom-scene/fda-opened?type=DRAFT&id=${draftId}`);
}

/**
 * @desc 获取模板是否开启对内文件签字功能
 * */
export function getInternalSignIfOpened({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/query-internal-sign-config`);
}

// 发送前校验
export function validDraft({ draftId = '' }) {
    return Vue.$http.get(`/template-api/v2/draft/${draftId}/valid`, {
        params: {
            skipped: false,
        },
    });
}

export function getIsLimitFaceConfig() {
    return Vue.$http.request('/template-api/v2/draft/sender/config');
}
