<template>
    <view class="auth-cert">
        <img :src="certBgImg" alt="" class="cert-bg">
        <div class="content">
            <img class="logo" src="~img/logo.png" alt="">
            <div class="cert-name">{{ isPerson ? '个人实名认证证书' : '企业实名证书' }}</div>
        </div>
        <div class="auth-info">
            <div class="name">{{ isPerson ? '姓名' : '企业名称' }}：</div>
            <div class="value">{{ isPerson ? caInfo.realName : caInfo.entName }}</div>
            <div class="name">{{ isPerson ? '身份证号码' : '统一社会信用代码' }}：</div>
            <div class="value">{{ isPerson ? caInfo.idNumber : caInfo.businessLicenceNumber }}</div>
            <div class="name">证书颁发机构：</div>
            <div class="value">{{ caInfo.caOrgCN }}</div>
        </div>
        <div class="cert-bestsign">
            <img class="verify" src="~img/auth_verify.png" alt="">
            <div class="hint">上上签电子签约云平台</div>
            <div class="hint">{{ caInfo.startTime }}</div>
        </div>
        <div class="cert-info">
            <div class="hint">证书序列号：{{ isPerson ? caInfo.serialNumber : '' }}</div>
            <div v-if="!isPerson" class="hint">{{ caInfo.serialNumber }}</div>
            <div class="hint">有效期：{{ `${caInfo.startTime}～${caInfo.stopTime}` }}</div>
        </div>
    </view>
</template>

<script>
import {  mapState } from 'vuex';
import { getPersonCaCertInfo, getEntCaCertInfo } from 'api/account.js';
export default {
    name: 'AuthCert',
    data() {
        return {
            personCertBgImg: 'https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/8f9bf2fde557aacc8e8415c28b68afe6076d4a41.png',
            entCertBgImg: 'https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/7b72e660eedbd213f5f1fa85fe58cf9e0c323b39.png',
            caInfo: {},
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo', 'entList']),
        ...mapState('send', ['curEntIndex', 'sendType']),
        isPerson() {
            return this.entList[this.curEntIndex].entId === '0';
        },
        certBgImg() {
            return this.isPerson ? this.personCertBgImg : this.entCertBgImg;
        },
    },
    methods: {
        initCaInfo() {
            if (this.isPerson) {
                getPersonCaCertInfo().then(({ data }) => {
                    this.caInfo = data;
                    this.caInfo.startTime = this.getTransferDate(this.caInfo.startTime);
                    this.caInfo.stopTime = this.getTransferDate(this.caInfo.stopTime);
                });
            } else {
                getEntCaCertInfo().then(({ data }) => {
                    this.caInfo = data;
                    this.caInfo.startTime = this.getTransferDate(this.caInfo.startTime);
                    this.caInfo.stopTime = this.getTransferDate(this.caInfo.stopTime);
                });
            }
        },
        getTransferDate(date) {
            let newDate = '';
            if (!date) {
                newDate = '未发放';
            } else {
                const t = date.split('-');
                newDate = `${Number(t[0])}年${Number(t[1])}月${Number(t[2])}日`;
            }
            return newDate;
        },
    },
    onShow() {
        this.initCaInfo();
    },
};
</script>

<style lang="scss">
.auth-cert {
    position: relative;
    .cert-bg{
        width: calc(100% - 60rpx);
        height: 1100rpx;
        padding: 80rpx 30rpx;
    }
    .content{
        position: absolute;
        top: 166rpx;
        width: 100%;
        display: flex;
        flex-direction:column;
        justify-content:flex-start ;
        align-items:center;
        .logo{
            width:80px;
            height: 34px;
            text-align: center;
        }
        .cert-name{
            font-size: 35rpx;
            color: #333333;
            margin-top: 60rpx;
            font-weight: 500;
        }
    }
    .auth-info{
        position: absolute;
        top: 400rpx;
        left: 164rpx;
        width: calc(100% - 164rpx - 30rpx);
        font-size: 28rpx;
        .name{
            color: $--color-text-primary;
            margin-top: 35rpx;
            font-weight: 500;
        }
        .value{
            color: $--color-text-secondary;
            margin: 10rpx 0;
        }
    }
    .cert-bestsign{
        position: absolute;
        right: 120rpx;
        bottom: 330rpx;
        text-align: right;
        .verify{
            width: 160rpx;
            height: 160rpx;
            margin-bottom: -30rpx;
        }
        font-size: 20rpx;
        color: $--color-text-primary;
        .hint{
            padding: 5rpx 0;
        }
    }
    .cert-info{
        width: 100%;
        position: absolute;
        bottom: 160rpx;
        text-align: center;
        font-size: 20rpx;
        color: $--color-text-secondary;
        .hint{
            padding: 5rpx 0;
        }
    }
}
</style>
