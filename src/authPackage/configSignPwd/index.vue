<template>
    <xd-page>
        <view class="config-sign-password">
            <view v-if="isReset" class="config-sign-password__reset-steps">
                <view class="step-item"
                    v-for="(step, index) in steps"
                    :key="index"
                    :class="activeStep >= index ? 'active' : ''"
                >
                    <view class="step-item__icon">
                        <text v-if="activeStep > index" class="icon-ic_singleoption_selected"></text>
                        <view class="step-item__icon-order" v-else>{{ index + 1 }}</view>
                    </view>
                    <view class="step-item__title">{{ step }}</view>
                    <view class="step-item__dots" v-if="index < steps.length - 1">······</view>
                </view>
            </view>
            <view class="config-sign-password__input-module">
                <view class="account-verify" v-show="!isReset || (!noNeedPhoneVerify && activeStep === 0)">
                    <view class="input-row">
                        <view class="input-row__title">帐号</view>
                        <view class="input-row__fill verify-get">
                            <input
                                class="input-row__fill account"
                                disabled
                                :value="account"
                            />
                            <view
                                v-if="ifShowChangeBtn"
                                class="input-row__btn"
                                @click="handleChangeVerifyMethod"
                            >{{ changeBtnText }}</view>
                        </view>
                    </view>
                    <view class="input-row" v-if="!noNeedPhoneVerify">
                        <view class="input-row__title">验证码</view>
                        <view class="input-row__fill verify-get">
                            <input
                                class="input-row__fill account"
                                type="number"
                                maxlength="6"
                                v-model="verifyCode"
                                placeholder="请输入验证码"
                            />
                            <view class="count-down" :class="{ 'disabled': time > 0 }" @click="getVerifyCode">{{ codeText }}</view>
                        </view>
                    </view>
                </view>
                <view class="name-verify" v-show="(authStatus === '2' && isReset && (noNeedPhoneVerify && activeStep === 0 ) || (!noNeedPhoneVerify && activeStep === 1))">
                    <view class="input-row">
                        <view class="input-row__title">姓名</view>
                        <input
                            class="input-row__fill"
                            disabled
                            :value="fullName"
                        />
                    </view>
                    <view class="input-row">
                        <view class="input-row__title">证件号</view>
                        <input class="input-row__fill"
                            type="idcard"
                            v-model="idNumber"
                            placeholder="填写身份证号码"
                        />
                    </view>
                </view>
                <view v-if="!isReset || isFinalStep" class="input-row">
                    <view class="input-row__title">{{ isReset ? '新密码' : '签约密码' }}</view>
                    <input class="input-row__fill"
                        type="number"
                        v-model="password"
                        maxlength="6"
                        placeholder="设置6位数签约密码"
                    />
                </view>
            </view>
            <view class="config-sign-password__footer">
                <button v-if="activeStep > 0"
                    type="default"
                    class="back-btn opt-btn"
                    @click="handlePreviousStep"
                >
                    上一步
                </button>
                <button v-if="isReset && !isFinalStep"
                    class="opt-btn"
                    type="primary"
                    @click="handleNextStep"
                >
                    下一步
                </button>
                <button v-if="!isReset || isFinalStep"
                    class="opt-btn"
                    type="primary"
                    @click="handleSave"
                >
                    保存
                </button>
            </view>
        </view>
    </xd-page>
</template>
<script>
import xdPage from 'components/xdPage/index.vue';
import { getVerifyCode, getAccountInfo } from 'api/account';
import regRules from 'utils/regs';

export default {
    components: { xdPage },
    data() {
        return {
            isReset: false,
            activeStep: 0,
            time: 0,
            fullName: '',
            account: '',
            authStatus: '',
            verifyCode: '',
            password: '',
            verifyKey: '',
            accountValidSuccessKey: '',
            idNumberValidSuccessKey: '',
            idNumber: '',
            currentAccountType: '',
            showChangePhoneVerify: false,
            notifications: [],
            canReUsePhoneVerify: false,
        };
    },
    computed: {
        steps() {
            if (this.noNeedPhoneVerify) {
                return this.authStatus === '2' ? ['验证实名', '重置密码'] : ['重置密码'];
            } else {
                return this.authStatus === '2' ? ['验证账号', '验证实名', '重置密码'] : ['验证账号', '重置密码'];
            }
        },
        codeText() {
            return this.time > 0 ? `重新发送(${this.time}s)` : '获取验证码';
        },
        isFinalStep() {
            return this.activeStep === this.steps.length - 1;
        },
        changeBtnText() {
            return this.currentAccountType === 'S' ? '切换邮箱验证' : '切换手机号验证';
        },
        ifShowChangeBtn() {
            // 当前账号是邮箱，不展示切换按钮，当前是手机，展示切换按钮，切换为邮箱后还能切换回来手机号验证
            return (this.currentAccountType === 'S' && this.notifications.length === 2) || this.showChangePhoneVerify;
        },
        noNeedPhoneVerify() {
            return this.currentAccountType === 'S' && this.canReUsePhoneVerify;
        },
    },
    methods: {
        backToSetPage() {
            uni.navigateBack({ delta: 1 });
        },
        validateVerifyCode() {
            if (this.noNeedPhoneVerify) {
                return '';
            }
            let errorMsg = '';
            if (!this.verifyKey) {
                errorMsg = '请先获取验证码';
            } else if (!this.verifyCode) {
                errorMsg = '验证码不能为空';
            } else if (!regRules.signpass.test(this.verifyCode)) {
                errorMsg = '验证码错误';
            }
            return errorMsg;
        },
        confirmSet() {
            let errorMsg = this.validateVerifyCode();
            if (!this.password) {
                errorMsg = '签约密码不能为空';
            } else if (!regRules.signpass.test(this.password)) {
                errorMsg = '密码错误，请输入6位数字';
            }
            if (errorMsg) {
                this.$toast.error(errorMsg);
                return;
            }
            this.$http.put(`/users/sign-pwd/setting`, {
                signPwd: this.password,
                verifyKey: this.verifyKey,
                verifyCode: this.verifyCode,
                account: this.account,
            }).then(() => {
                this.$toast.success('签约密码设置成功');
                setTimeout(() => {
                    this.backToSetPage();
                }, 1500);
            });
        },
        confirmReset() {
            if (!this.password) {
                this.$toast.error('新密码不能为空');
                return;
            } else if (!regRules.signpass.test(this.password)) {
                this.$toast.error('密码错误，请输入6位数字');
                return;
            }
            this.$http.put(`/users/sign-pwd/resetting`, {
                authPassKey: this.idNumberValidSuccessKey,
                captchaPassKey: this.accountValidSuccessKey,
                newPwd: this.password,
                account: this.account,
            }).then(() => {
                this.$toast.success('重置密码成功');
                setTimeout(() => {
                    this.backToSetPage();
                }, 1500);
            });
        },
        handleSave() {
            if (!this.isReset) {
                this.confirmSet();
                return;
            }
            this.confirmReset();
        },
        handleNextStep() {
            if (!this.noNeedPhoneVerify && this.activeStep === 0) {
                this.validateAccount();
            } else {
                this.validateIdNumber();
            }
        },
        handlePreviousStep() {
            this.activeStep -= 1;
        },
        validateIdNumber() {
            if (!this.idNumber) {
                this.$toast.error('证件号不能为空');
                return;
            }

            if (!regRules.weakIdCardReg.test(this.idNumber)) {
                this.$toast.error('证件号格式错误');
                return;
            }
            this.$http.get(`/users/auth/same-auth?idNumber=${this.idNumber}`)
                .then((res) => {
                    this.idNumberValidSuccessKey = res.data.value;
                    // 校验成功，下一步
                    this.activeStep += 1;
                });
        },
        validateAccount() {
            const errorInfo = this.validateVerifyCode();
            if (errorInfo) {
                this.$toast.error(errorInfo);
                return;
            }

            this.$http.post(`/users/captcha/verify`, {
                code: `B003`,
                target: this.account,
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
            }).then(res => {
                this.accountValidSuccessKey = res.data.value;
                this.time = 0;
                this.activeStep += 1;
            });
        },
        timer() {
            this.time = 60;
            const inTimer = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    clearInterval(inTimer);
                }
            }, 1000);
        },
        getVerifyCode() {
            if (this.time > 0) {
                return;
            }
            getVerifyCode(this.account, '', '', 2, 'B003').then((res) => {
                this.verifyKey = res.data.value;
                this.timer();
                if (res.data.msg) {
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        confirmText: '我知道了',
                        success: () => {
                        },
                    });
                }
            });
        },
        judgeAccountType(account) {
            const phoneRule = regRules.userPhone;
            const emailRule = regRules.userEmail;
            if (phoneRule.test(account)) {
                return 'S'; // 手机
            } else if (emailRule.test(account)) {
                return 'E';  // 邮箱
            }
            return '';
        },
        handleChangeVerifyMethod() {
            const isPhone = this.currentAccountType === 'S';
            this.account = this.notifications.find((item) => {
                return item.type === (isPhone ? 1 : 2);  // 1:邮箱 2:手机
            })?.code || '';
            this.currentAccountType = this.judgeAccountType(this.account);
            this.showChangePhoneVerify = true;
        },
        getNotifications() {
            getAccountInfo().then((res) => {
                this.notifications = res.data.notifications;
            });
        },
        getCurrentAccountType() {
            this.currentAccountType = this.judgeAccountType(this.account) || '';
        },
        getIfCanReUsePhoneVerify() {
            // 判断是否在10分钟内有短信验证码登录过，如果有则可以复用，不需要再次验证
            this.$http.get(`/users/captcha/exist-verify-code`)
                .then((res) => {
                    this.canReUsePhoneVerify = res.data;
                });
        },
    },
    onLoad(query) {
        const { reset, account, fullName = '', authStatus } = query;
        this.isReset = reset === '1';
        this.account = decodeURIComponent(account);
        this.fullName = decodeURIComponent(fullName);
        this.authStatus = authStatus;
        this.getNotifications();
        this.getCurrentAccountType();
        this.getIfCanReUsePhoneVerify();
    },
};
</script>
<style lang="scss">
.config-sign-password {
	background-color: $--background-color-regular;
    &__reset-steps {
        display: flex;
        justify-content: center;
        height: 45px;
        line-height: 45px;
        color: $--color-text-secondary;
        background-color: $--color-white;
        box-shadow: $--box-shadow-light;
        border-top: 1px solid $--border-color-lighter;
        .step-item {
            display: flex;
            &__icon {
				padding: 0 4px;
                &-order {
                    width: 14px;
                    height: 14px;
                    line-height: 14px;
                    margin-top: 7px;
                    transform: translateY(50%);
					border: 1px solid $--color-text-secondary;
                    border-radius: 7px;
                    font-size: 12px;
                    text-align: center;
                }
            }
            &__dots {
                font-size: 10px;
                padding: 0 6px;
                letter-spacing: -2px;
            }
            &.active {
                color: $--color-primary;
                .step-item__icon-order {
                    border-color: $--color-primary;
                }
            }
        }
    }
    &__input-module {
        margin-top: 20px;
        background-color: $--color-white;
		.input-row {
            display: flex;
            padding: 0 15px;
            height: 50px;
            line-height: 50px;
            font-size: 15px;
			border-bottom: 1px solid $--border-color-lighter;
            color: $--color-text-primary;
            &__title {
                width: fit-content;
                min-width: 45px;
            }
            &__btn {
                width: 200rpx;
                height: 50px;
                line-height: 50px;
                font-size: 28rpx;
                color: $--color-primary;
                text-align: left;
            }
            &__fill {
                flex-grow: 1;
                height: 50px;
                line-height: 50px;
                text-align: right;
            }
            .verify-get {
				display: flex;
                .account {
                    padding-left: 10px;
                    padding-right: 15px;
                    flex-grow: 1;
                    height: 50px;
                    line-height: 50px;
                }
                .count-down {
                    width: 200rpx;
                    height: 50px;
                    line-height: 50px;
                    font-size: 28rpx;
                    color: $--color-primary;
                    text-align: left;
                    &.disabled {
                        color: $--color-text-secondary;
                    }
                }
            }
        }
    }
    &__footer {
        position: fixed;
        bottom: 34px;
        display: flex;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        justify-content: center;
        .back-btn {
            width: 45%;
            margin-right: 20px;
        }
    }
}
</style>
