<!--h5 web-view窗口， 实名-->
<template>
    <web-view :src="webviewSrc" @message="handleMessage"></web-view>
</template>

<script>
import { mapState } from 'vuex';

const h5PageConfig = {
    personRealName: {
        url: '/foundation/authentication',
        title: '个人实名认证',
    },
    personRealNameFace: {
        url: '/personal/certification/noIdCard',
        title: '个人实名认证',
    },
};

export default {
    data() {
        return {
            facePageUrl: '',
        };
    },
    onLoad(options) {
        const optionData = options.query || options;
        optionData.facePageUrl && (this.facePageUrl = decodeURIComponent(optionData.facePageUrl));
    },
    computed: {
        ...mapState('send', ['h5Params']),
        ...mapState(['isWeWork']),
        webviewSrc() {
            // 如果有facePageUrl参数，说明是刷脸小程序的逻辑，需要重新加载刷脸页面
            if (this.facePageUrl) {
                return this.facePageUrl;
            } else {
                const page = `${h5PageConfig[this.h5Params.type].url}`;
                const params = `accessToken=${uni.getStorageSync('accessToken')}&refreshToken=${uni.getStorageSync('refreshToken')}&qywx=1&signPlatform=${this.isWeWork ? 'QWX' : 'WX'}`;
                const url = `${this.$http.baseURL}/mpLogin?${params}&url=${encodeURIComponent(page)}`;
                return url;
            }
        },
    },
    onShow() {
        uni.setNavigationBarTitle({
            title: h5PageConfig[this.h5Params.type].title,
        });
    },
    methods: {
        handleMessage(megData) {
            const message = megData.target.data[0];
            // 刷脸小程序需要的参数
            const ssqFaceQuery = message.ssqFaceQuery;
            // 上上签页面对应的webview路径，需要主动跳回
            const ssqPageUrl = message.ssqPageUrl;
            // 消息类型 face： 需要跳转刷脸小程序
            const messageType = message.messageType;

            if (messageType === 'face') {
                uni.navigateTo({
                    url: `/views/personAuth/index?facePageUrl=${encodeURIComponent(ssqPageUrl)}`,
                });
                uni.navigateToMiniProgram({
                    appId: 'wx6dd34865858963a5',
                    path: `index/index?ssqFaceQuery=${encodeURIComponent(ssqFaceQuery)}`,
                    envVersion: 'release', // develop 开发板；trial 体验版；release 正式版
                    success: () => {
                        uni.showModal({
                            title: '提示',
                            content: '是否已经完成刷脸',
                        });
                    },
                    fail: (err) => {
                        console.log(err);
                    },
                });
            }
        },
    },
};
</script>

