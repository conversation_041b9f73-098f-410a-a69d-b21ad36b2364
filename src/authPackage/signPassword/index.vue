<template>
    <xd-page>
        <view class="sign-password-page">
            <view class="row-item">
                <view class="row-item__title">签约密码</view>
                <view class="row-item__opt" @click="handleOptSignPassword">{{ hasSetSignPwd ? '重置' : '添加' }}</view>
            </view>
            <view class="row-item">
                <view class="row-item__title">优先签约密码校验</view>
                <view class="row-item__opt">
                    <switch color="#127FD2"
                        class="row-item__opt-switch"
                        :checked="priority"
                    />
                    <view class="shadow-switch" @click="handleSwitch('priority')"></view>
                </view>
            </view>
            <view class="row-tip">
                开启后，未指定特殊校验方式（如必须刷脸签署等）的合同均优先推荐签约密码校验，但允许切换至验证码校验。开启后会自动关闭双重校验
            </view>
            <view class="row-item">
                <view class="row-item__title">双重校验</view>
                <view class="row-item__opt">
                    <switch color="#127FD2"
                        class="row-item__opt-switch"
                        :checked="doubleCheck"
                    />
                    <view class="shadow-switch" @click="handleSwitch('doubleCheck')"></view>
                </view>
            </view>
            <view class="row-tip">
                开启后，每次签署、拒签、撤销时，除了需要校验短信验证码以外还需要校验签约密码。开启后会自动关闭优先签约密码校验
            </view>
        </view>
    </xd-page>
</template>
<script>
import xdPage from 'components/xdPage/index.vue';
import { getUserConfigByKey, getSignPwdExist, postUserConfig } from 'api/account';
import { mapActions, mapState } from 'vuex';

export default {
    components: { xdPage },
    data() {
        return {
            priority: false,
            doubleCheck: false,
            hasSetSignPwd: false,
            hasLoaded: false,
            configKeyMap: {
                priority: 'PRIORITY_SIGNING_PASSWORD',
                doubleCheck: 'SECOND_CHECK',
            },
            SUBMMIT_VALUE: {
                ON: '1', // 配置打开
                OFF: '0', // 配置关闭
            },
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
    },
    methods: {
        ...mapActions('user', ['getHeadInfo']),
        ...mapActions(['loginPromise', 'switchEntId']),
        handleOptSignPassword() {
            const { account, authStatus, fullName } = this.commonHeaderInfo.platformUser || {};
            const query =
                `reset=${this.hasSetSignPwd ? '1' : '0'}&account=${account}&authStatus=${authStatus}&fullName=${fullName}`;
            uni.navigateTo({ url: `/views/configSignPwd/index?${query}` });
        },
        async handleSwitch(key) {
            // 必须先开启签约密码校验，才能开启二次校验或者优先签约密码校验
            if (!this.hasSetSignPwd) {
                this.$toast.error('请先添加签约密码');
                return;
            }
            try {
                // 保存当前配置
                await this.saveUserConfig(this.configKeyMap[key], !this[key] ? this.SUBMMIT_VALUE.ON
                    : this.SUBMMIT_VALUE.OFF);
                this[key] = !this[key];
                // 二次校验、优先签约密码校验不能同时开启
                const conflictKey = key === 'priority' ? 'doubleCheck' : 'priority';
                if (this[key] && this[conflictKey]) {
                    this.saveUserConfig(this.configKeyMap[conflictKey], this.SUBMMIT_VALUE.OFF);
                    this[conflictKey] = false;
                }
            } catch (e) {
                this.$toast.error('操作失败');
            }
        },
        saveUserConfig(key, value) {
            return postUserConfig(key, value);
        },
        async initConfig() {
            uni.showLoading({
                title: '用户信息获取中...',
                mask: true,
            });
            try {
                const { data } = await getUserConfigByKey('PRIORITY_SIGNING_PASSWORD');
                const { data: pwdData } = await getSignPwdExist();
                this.priority = data?.value === this.SUBMMIT_VALUE.ON;
                this.hasSetSignPwd = pwdData?.value;
                this.doubleCheck = this.commonHeaderInfo.SECOND_CHECK === this.SUBMMIT_VALUE.ON;
            } catch (e) {
                this.$toast.error('获取用户签约密码配置失败');
            } finally {
                uni.hideLoading();
            }
        },
    },
    onHide() {
        this.hasLoaded = true;
    },
    async onShow() {
        // 重新进入页面的时候刷新配置数据
        if (this.hasLoaded) {
            await this.initConfig();
        }
    },
    async onLoad(query) {
        await this.loginPromise();
        if (query.chosenEntId) {
            await this.switchEntId(query.chosenEntId);
        }
        await this.getHeadInfo();
        await this.initConfig();
    },
};
</script>
<style lang="scss">
.sign-password-page {
    background-color: $--background-color-regular;
    padding-top: 20rpx;
    .row-item {
        display: flex;
        margin-bottom: 20rpx;
        padding: 0 30rpx;
        background-color: $--color-white;
        height: 50px;
        line-height: 50px;
        &__title {
            width: fit-content;
        }
        &__opt {
            width: auto;
            text-align: right;
            flex-grow: 1;
            color: $--color-primary;
            position: relative;
            .shadow-switch {
                position: absolute;
                right: 2px;
                top: 0;
                line-height: 50px;
                vertical-align: middle;
                width: 47px;
                height: 50px;
            }
            .wx-switch-input{
                width: 42px !important;
                height: 20px !important;
            }
            /*白色样式（false的样式）*/
            .wx-switch-input::before{
                width: 22px !important;
                height: 18px !important;
                background-color: $--border-color-light;
            }
            /*绿色样式（true的样式）*/
            .wx-switch-input::after{
                width: 20px !important;
                height: 18px !important;
            }
        }
    }
    .row-tip {
        margin-bottom: 20rpx;
        padding: 0 30rpx;
        color: $--color-info;
    }
}
</style>
