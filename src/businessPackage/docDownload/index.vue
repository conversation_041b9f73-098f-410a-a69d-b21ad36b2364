<template>
    <view class="content" v-if="init">
        <scroll-view
            class="scroll-container"
            scroll-y
        >
            <view>
                <view class="header_view">
                    <image
                        :src="imageUrl"
                        mode="widthFix"
                        class="header_image"
                    />
                </view>
                <text>{{ decodeURIComponent(fileName) ? decodeURIComponent(fileName) : fileName }}</text>
                <progress
                    class="progress"
                    :percent="percent"
                    show-info
                    border-radius="6"
                    activeColor="#127FD2"
                    backgroundColor="#CCCCCC"
                />
                <button
                    v-if="isShowButton"
                    class="button"
                    @click="openDocument"
                >
                    {{ buttonText }}
                </button>
                <view class="tip">
                    <text>下载提示</text>
                    <text>第一步：</text>
                    <text>点击上方"预览文档"后，在预览页点击右上角"..."按钮</text>
                    <image
                        :src="downLoadTipImg1"
                        mode="widthFix"
                    />
                    <text>第二步：</text>
                    <text>在弹出抽屉页里面，选择相应操作完成下载</text>
                    <image
                        :src="downLoadTipImg2"
                        mode="widthFix"
                    />
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import pdfImg from 'src/static/pdf.png';
import zipImg from 'src/static/zip.png';
import downLoadImg1 from 'src/static/downloadTip1.png';
import downLoadImg2 from 'src/static/downloadTip2.png';
import { mapState, mapActions } from 'vuex';

export default {
    data() {
        return {
            contractId: '',
            fileType: '',
            documentIds: [],
            fileName: '',
            imageUrl: '',
            percent: 0,
            buttonText: '',
            isShowButton: false,
            savedFilePath: '',
            downLoadTipImg1: downLoadImg1,
            downLoadTipImg2: downLoadImg2,
            fileListKeys: [],
            fileListValues: [],
            fileNameList: [],
            lastFileList: [],
            init: false,
        };
    },
    async onLoad(options) {
        if (options.userAccount) {
            this.setLoading(true);
            try {
                await this.loginPromise();
                await this.getHeadInfo(); // 已登录时需要获取当前账号数据进行account对比
                if (options.userAccount !== this.userAccount) {
                    return this.goLogin(options);
                }
            } catch (error) {
                return this.goLogin(options);
            }
            this.setLoading(false);
        }
        this.initPageData(options);
    },
    computed: {
        ...mapState('user', ['userAccount']),
        downLoadUrl() {
            const documentIdsStr = this.documentIds.toString();
            const query = `&needAttachment=false&documentIds=${documentIdsStr}`;
            return`${this.$http.baseURL}/contract-api/contracts/${this.contractId}/documents-download?needMergeLabel=true&access_token=${uni.getStorageSync('accessToken')}${query}`;
        },
    },
    methods: {
        ...mapActions('user', ['getHeadInfo']),
        ...mapActions(['loginPromise', 'setLoading']),
        goLogin(options) {
            const { contractId, userAccount, fileType, documentIds, fileName } = options;
            uni.setStorageSync('loginSuccessPage', `views/docDownload/index?contractId=${contractId}&fileType=${fileType}&documentIds=${documentIds}&fileName=${fileName}`);
            uni.reLaunch({
                url: `/subViews/login/index?account=${userAccount}`,
            });
        },
        // 初始化页面数据
        initPageData(options) {
            this.init = true;
            uni.showModal({
                title: '提示',
                showCancel: false,
                content: '合同原件为PDF，下载成功后建议使用PDF阅读软件查看。使用手机自带的PDF预览功能，可能会看不到合同上的签名、盖章。',
            });
            const { contractId, fileType, documentIds, fileName } = options;
            this.contractId = contractId || '';
            this.fileType = fileType || 'singlePart';
            this.documentIds = documentIds || [];
            this.fileName = decodeURIComponent(fileName) || '';
            this.imageUrl = this.fileType === 'singlePart' ? zipImg : pdfImg;
            this.downloadFile();
        },
        // 下载文件
        downloadFile() {
            const downloadTask = uni.downloadFile({
                url: this.downLoadUrl,
                success: (result) => {
                    if (result.statusCode === 200) {
                        this.saveFile(result);
                    } else {
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none',
                        });
                    }
                },
                fail: () => {
                    uni.showToast({
                        title: '下载失败',
                        icon: 'none',
                    });
                },
            });
            downloadTask.onProgressUpdate((res) => {
                this.percent = res.progress;
            });
        },
        // 保存文件
        saveFile(file) {
            this.percent = 100;
            let saveFilePath = '';
            let fileNameStr = this.fileName;
            // 处理一下合同名称中带斜杠的，不然下载会出错
            if (fileNameStr) {
                fileNameStr = fileNameStr.replaceAll('/', '_');
                fileNameStr = fileNameStr.replaceAll('\\', '_');
            }
            if (this.fileType === 'singlePart') {
                saveFilePath = `${wx.env.USER_DATA_PATH}/合同文件_${new Date().getTime()}.zip`;
                this.buttonText = '解压zip文件';
            } else {
                saveFilePath = `${wx.env.USER_DATA_PATH}/${fileNameStr}.pdf`;
                this.buttonText = '预览文档';
            }
            wx.getFileSystemManager().saveFile({
                tempFilePath: file.tempFilePath,
                filePath: saveFilePath,
                success: (res) => {
                    this.isShowButton = true;
                    this.savedFilePath = res.savedFilePath;
                },
                fail: ()  => {
                    uni.showToast({
                        title: '保存失败',
                        icon: 'none',
                    });
                },
            });
        },
        // 打开文档
        openDocument() {
            // 压缩文件
            if (this.fileType === 'singlePart') {
                // ios 无法下载到本地 因此只能预览
                if (uni.getSystemInfoSync().platform === 'ios') {
                    const fm = uni.getFileSystemManager();
                    fm.readZipEntry({
                        filePath: this.savedFilePath,
                        entries: 'all',
                        success: (res) => {
                            this.fileListKeys = Object.keys(res.entries);
                            this.fileListValues = Object.values(res.entries);
                            if (this.fileListKeys[0]) {
                                this.checkNameType();
                                this.writeFiles();
                            } else {
                                uni.showToast({
                                    title: '文件为空，请重新下载',
                                    icon: 'none',
                                });
                            }
                        },
                        fail: (err) => {
                            console.error(err);
                        },
                    });
                } else {
                    uni.openDocument({
                        fileType: 'zip',
                        filePath: this.savedFilePath,
                        showMenu: true,
                    });
                }
            } else {
                uni.openDocument({
                    fileType: 'pdf',
                    filePath: this.savedFilePath,
                    showMenu: true,
                });
            }
        },
        // 处理文件名
        checkNameType() {
            this.fileNameList = [];
            if (this.fileListKeys && this.fileListKeys.length) {
                for (let i = 0; i < this.fileListKeys.length; i++) {
                    const file = this.fileListKeys[i];
                    const lastIndex = file.lastIndexOf('.');
                    const type = file.substring(lastIndex + 1, file.length);
                    const name = `${this.fileName}_${i + 1}.${type}`;
                    this.fileNameList.push(name);
                }
            }
        },
        writeFiles() {
            this.lastFileList = [];
            if (this.fileListValues && this.fileListValues.length) {
                const fm = uni.getFileSystemManager();
                for (let i = 0; i < this.fileListValues.length; i++) {
                    const filePath = `${wx.env.USER_DATA_PATH}/${this.fileNameList[i]}`;
                    const fileData = this.fileListValues[i].data;
                    const encoding = 'base64';
                    try {
                        fm.writeFileSync(filePath, fileData, encoding);
                        this.lastFileList.push(filePath);
                    } catch (e) {
                        console.error('err', e);
                    }
                }
                if (this.lastFileList.length === this.fileNameList.length) {
                    uni.navigateTo({
                        url: `/views/fileList/index?fileList=${this.fileNameList}`,
                    });
                } else {
                    uni.showToast({
                        title: '解压失败',
                        icon: 'none',
                    });
                }
            }
        },
    },

};

</script>

<style lang="scss">
page{
    height: 100%;
    display: flex;
}
.content{
    .scroll-container{
        height: 100%;
    }
    width: 100%;
    overflow: hidden;
    flex-direction: column;
    display: flex;
    text-align: center;
    padding: 20px 20px;
    .button{
        height: 40px;
        background-color: #127FD2;
        border-radius: 4px;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        width: 200px;
        color: #FFFFFF;
        margin-top: 30px;
        line-height: 40px;
    }
    .header_view{
        width: 100%;
        text-align: center;
        margin-top: 30px;
        .header_image{
            width: 50px;
            height: 50px;
        }
    }
    text{
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 18px;
    }
    .tip{
        flex-direction: column;
        display: flex;
        text-align: left;
        margin-top: 10px;
        margin-bottom: 90px;
        text{
            font-size: 13px;
            color: #666666;
        }
        image{
            width:100%;
        }
    }
}
</style>
