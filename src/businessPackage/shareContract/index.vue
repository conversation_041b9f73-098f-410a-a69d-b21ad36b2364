<template>
    <div class="send-contract">
        <img class="send-success" src="~img/success.png" alt="">
        <p>合同发送成功</p>
        <button class="send-btn" open-type="share">转发给对方</button>
    </div>
</template>

<script>
export default {
    data() {
        return {
            emptySrc: require('img/shareImg.png'),
        };
    },
    onShareAppMessage() {
        const { contractId, sender, contractName } = this.$Route.query;
        this.$point('click_sample_contact_foward', {
            biz_id: contractId,
        });
        return {
            title: `${decodeURIComponent(sender)}给您发来一份《${decodeURIComponent(contractName)}》，请查看`,
            path: `/views/webviewRedirect/index?contractId=${contractId}&fromShare=true`,
            imageUrl: this.emptySrc,
        };
    },
};
</script>

<style lang="scss">
.send-contract {
    position: relative;
    height: 100vh;
    background-color: $uni-bg-color;
    .send-success {
        display: block;
        width: 54px;
        height: 54px;
        margin: 52px auto 42px;
    }
    p {
        text-align: center;
        font-size: 22px;
        color: $uni-text-color;
    }
    .send-btn {
        position: absolute;
        bottom: 130px;
        left: 50%;
        transform: translateX(-50%);
        width: 184px;
        height: 40px;
        line-height: 40px;
        background: $main-color;
        border-radius: 4px;
        color: $uni-bg-color;
        font-size: 16px;
    }
}
</style>
