<template>
    <scroll-view class="approve-list" :scroll-y="true" @scrolltolower="loadMore">
        <uni-list>
            <uni-list-item 
                v-for="(item, index) in list"
                :key="index"
                :title="item.title" 
                :note="item.content"
                clickable
                :link="needJump(item.gotoType)"
                @click="handleJump(item)"
            ></uni-list-item>
            <view v-if="!list.length && !loading" class="approve-list-empty">
                <img :src="emptySrc" alt="">
                <p>暂无内容</p>
            </view>
        </uni-list>
    </scroll-view>
</template>
<script>
import { switchEnt } from 'api/send';
import { mapActions, mapState } from 'vuex';
export default {
    data() {
        return {
            emptySrc: require('img/empty.png'),
            list: [],
            onShowNum: 0,
            pageNum: 1,
            pageSize: 20,
            total: 0,
        }
    },
    computed: {
        ...mapState(['loading']),
    },
    methods: {
        ...mapActions(['setLoading']),
        loadMore() {
            try {
            if (this.list.length < this.total && !this.loading) {
                this.pageNum++;
                this.getList();
            }
            } catch (error) {
                console.log(error);
                
            }
        },
        needJump(gotoType) {
            return !!gotoType;
        },
        async handleJump(item) {
            if (item.gotoType === 2) {
                uni.switchTab({
                    url: 'views/account/index',
                });
                return;
            }
            if ([10, 11].includes(item.gotoType)) {
                uni.navigateTo({
                    url: 'views/charge/index',
                });
                return;
            }
            const { data } = await switchEnt(item.entId);
            uni.setStorageSync('accessToken', data.access_token);
            uni.setStorageSync('refreshToken', data.refresh_token);
            const url = this.getUrl(item);
            url && uni.navigateTo({
                url: `/views/webviewRedirect/index?url=${encodeURIComponent(`${url}${url.includes('?') ? '&' : '?'}access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`)}`,
            });
        },
        async getList(type) {
            if (type === 'init') {
                this.pageNum = 1;
                this.list = [];
            }
            this.setLoading(true);
            this.$http.get(`/ents/message/centre/list?shouldReadAllSubject=true&noticeType=NOTICE,APPROVAL&pageNum=${this.pageNum}&pageSize=${this.pageSize}`).then(res => {
                this.list = this.list.concat(res.data.list);
                this.total = res.data.total;
            }).finally(() => {
                this.setLoading(false);
            });
        },
        getUrl(notice) {
            const { gotoAddr, gotoType, attachment } = notice;
            const { applyId, contractSenderName, contractTitle, targetEntId, recordId, type, archiveType, archiveToken, entId } = attachment;
            let url = '';
            switch (gotoType) {
                case 1:
                    url = `/mobile/doc/detail?contractId=${gotoAddr}`;
                    break;
                // case 2: url = '/usercenter/account'; break;
                // case 4:
                // case 5: url = gotoAddr; break;
                case 7:
                case 8: url = '/mp/auth-m/individual/auth'; break;
                case 9: url = '/enterprise/authentication'; break;
                // case 10: url = '/console/enterprise/account/recharge'; break;
                // case 11: url = '/usercenter/recharge'; break;
                // case 3:
                // case 6: break;
                // case 12: url = '/console/enterprise/seal/manage'; break;
                // case 13: url = '/console/enterprise/account/members'; break;
                case 14:
                    url = `/sign/seal-distribute?applyId=${applyId}&entId=${entId}`;
                break; // 申请印章分配
                case 15:
                    url = `/mobile/approve/authority?applyId=${applyId}&contractSenderName=${encodeURIComponent(contractSenderName)}&contractTitle=${encodeURIComponent(contractTitle)}&targetEntId=${targetEntId || ''}`;
                    break; // 申请合同权限
                case 16: url = `/account-center/reception/examination?applyId=${applyId}`; break;
                case 17: url = `/mp/auth-m/sealApproval?recordId=${recordId}&type=${type}`; break;
                case 18:
                    if (+archiveType === 0) {
                        url = `/damp/pages/guide/guide?token=${archiveToken}`;
                    } else {
                        url = `/box/h5/entGuide?token=${archiveToken}`;
                    }
                    break;
            }
            if (!url) {
                return '';
            }
            url = `${this.$http.baseURL}${url}`;
            return url;
        },
        getNote(notice) {
            const { type, applyUserName, operatedEntName, targetUserName } = notice;
            return {
                1: `${applyUserName}发起交接申请，点击进行审批`,
                11: `${applyUserName}希望将${operatedEntName}的印章交接于${targetUserName}，点击进行审批`,
            }[type] || `${applyUserName}向你发起印章或相关权限申请，点击进行审批`;
        }
    },
    onShow() {
        if (this.onShowNum) {
            this.getList('init');
        }
        this.onShowNum = 1;
    },
    created() {
        this.getList();
    },
}
</script>
<style lang="scss">
.approve-list {
    position: relative;
    height: 100vh;
}
.approve-list-empty {
    position: absolute;
    width: 100%;
    top: 50%;
    img{
        width: 100%;
    }
    p{
        text-align: center;
        color: $--color-info;
    }
}
</style>