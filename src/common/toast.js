const toast = (options, type) => {
    if (type === 'loading') {
        uni.showLoading(options);
    } else if (type === 'hideLoading') {
        uni.hideLoading();
    } else {
        options.icon = (type === 'error') ? 'none' : type;
        uni.showToast(options);
    }
};

// 'none'：不加图标可以展示两行文字，否则只能最多7个字
['error', 'success', 'loading', 'hideLoading', 'none'].forEach(type => {
    toast[type] = options => {
        if (typeof options === 'string') {
            options = {
                title: options,
                duration: 2000,
            };
        }

        return toast(options, type);
    };
});

export default toast;
