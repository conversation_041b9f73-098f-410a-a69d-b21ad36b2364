<template>
    <view class="activity-logo">
        <img
            v-if="isDuringActivity"
            @click="jumpToActivityDetail"
            class="activity-logo__icon"
            src="~img/activity.png"
            alt="活动挂件"
        >
        <!--        <view class="activity-logo__close">-->
        <!--            <text class="icon-ic_close" @click="visible=false"></text>-->
        <!--        </view>-->
    </view>
</template>

<script>

export default {
    name: 'ActivityLogo',
    computed: {
        isDuringActivity() {
            // const isPreEnv = this.$http.config.baseURL.includes('wx502.bestsign.info');
            // if (isPreEnv) {
            //     return true;
            // }
            const today = new Date();
            const startDate = new Date('2024-08-28T00:00:00');
            const endDate = new Date('2024-09-30T23:59:59');

            return today >= startDate && today <= endDate;
        },
    },
    methods: {
        async jumpToActivityDetail() {
            this.$sensors.track({
                eventName: 'Mp_Common_BtnClick',
                eventProperty: {
                    activity_id: '10th',
                    activity_name: '10周年活动',
                    first_category: '悬浮框',
                    icon_name: '活动入口',
                },
            });
            uni.navigateTo({
                url: 'views/charge/index?isFromH5Home=1',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.activity-logo {
    position: absolute;
    right: 5px;
    top: 220px;
    width: 90px;
    height: 40px;
    &__icon {
        width: 100%;
        height: 100%;
    }
    //&__close {
    //    text-align: center;
    //    line-height: 10px;
    //    .icon-ic_close {
    //        padding: 4px;
    //        font-size: 8px;
    //        border: 1px solid #ddd;
    //        border-radius: 8px;
    //    }
    //}
}
</style>
