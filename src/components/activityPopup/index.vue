<template>
    <CenterPopup class="activity-popup">
        <view class="activity-popup__wrapper">
            <view class="activity-popup__content">
                <img src="~img/activityBanner.png" alt="">
            </view>
            <view class="activity-popup__jump">
                <view class="activity-popup__jump-btn" @click="jumpToActicityDetail">
                    立即参与
                </view>
            </view>
            <view class="activity-popup__close">
                <text class="icon-ic_close" @click="handleClose('关闭')"></text>
            </view>
        </view>
    </CenterPopup>
</template>

<script>
import dayjs from 'dayjs';
import CenterPopup from 'components/centerPopup';
import { postUserConfig } from 'api/account';
export default {
    name: 'ActivityPopup',
    components: { CenterPopup },
    data() {
        return {
            avalidDays: 1,
        };
    },
    computed: {
        hasLogin() {
            const isLogined = Boolean(uni.getStorageSync('accessToken'));
            return isLogined;
        },
    },
    methods: {
        async jumpToActicityDetail() {
            this.handleSensors('Mp_HomeWindow_BtnClick', '立即参与');
            this.hasLogin && await this.saveActivityState();
            uni.navigateTo({
                url: 'views/charge/index?isFromH5Home=1',
            });
        },
        handleSensors(eventName, iconName) {
            this.$sensors.track({
                eventName,
                eventProperty: {
                    activity_id: '10th',
                    activity_name: '10周年活动',
                    window_name: '倒计时弹窗',
                    icon_name: iconName || '',
                },
            });
        },
        saveActivityState() {
            return postUserConfig('YEAR_END_COUNT_DOWN', 'true');
        },
        async handleClose(iconName) {
            this.handleSensors('Mp_HomeWindow_BtnClick', iconName);
            this.hasLogin && await this.saveActivityState();
            this.$emit('close');
        },
    },
    mounted() {
        this.handleSensors('Mp_HomeWindow_PopUp');
        this.avalidDays = dayjs('2024-10-1').diff(dayjs(new Date()), 'day') + 1;
    },
};
</script>

<style lang="scss">
.activity-popup {
    &__wrapper {
        position: relative;
    }
    &__content {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 227px;
        height: 242px;
        border-radius: 32px;
        background-size: cover;
        .tip-text {
            display: flex;
            align-items: center;
            margin-top: -20px;
            font-size: 16px;
            font-weight: 400;
            color: #6D3B12;
            .day {
                padding: 0 36px 0 20px;
                font-size: 150px;
            }
        }
    }
    &__jump {
        display: flex;
        justify-content: center;
        padding: 10px 0;
        &-btn {
            width: 120px;
            line-height: 32px;
            text-align: center;
            color: #fff;
            border-radius: 25px;
            background: #000;
        }
    }
    &__next {
        width: 100%;
        color: $--color-white;
        display: flex;
        justify-content: center;
        .next-btn {
            width: 230px;
            padding: 8px 15px;
            font-size: 14px;
            box-sizing: border-box;
            border-radius: 21px;
            border: 1px solid $--color-white;
            background: rgba(61, 61, 61, 0.5);
        }
    }
    &__close {
        text-align: center;
        .icon-ic_close {
            padding: 6px;
            font-size: 16px;
            color: $--color-white;
            border: 1px solid $--color-white;
            border-radius: 18px;
        }
    }
}
</style>
