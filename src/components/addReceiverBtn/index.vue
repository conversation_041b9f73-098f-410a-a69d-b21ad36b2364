<template>
    <view class="signer-info__btn">
        <view @click="addReceiver('ENTERPRISE')">{{ showCCBtn ? '+ 抄送给企业成员' : '+ 添加签约企业' }}</view>
        <view class="signer-info__line">|</view>
        <view @click="addReceiver('PERSON')">{{ showCCBtn ? '+ 抄送给个人用户' : '+ 添加签约个人' }}</view>
    </view>
</template>

<script>
import { mapMutations, mapState, mapActions } from 'vuex';
import { getSSQAccountByWxId, postWxMsg } from 'api/account';
import { getLocalSendReceiverInfo, getTemplateSendReceiverInfo } from 'const/receiver';
export default {
    props: {
        hasAddCCReceiverRight: {
            type: Boolean,
            default: true,
        },
        hasModifyReceiverRight: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            selectEnterpriseContactData: [], // 选择微信联系人信息
        };
    },
    computed: {
        ...mapState('send', ['receiverList', 'sendType']),
        ...mapState('user', ['featureIds', 'qwxUserName']),
        ...mapState(['isWeWork']),
        // 是否有使用企业微信通讯录权限
        hasUseWeWorkAddressRight() {
            return this.featureIds.indexOf('195') > -1;
        },
        showCCBtn() {
            return !this.hasModifyReceiverRight && this.hasAddCCReceiverRight;
        },
    },
    methods: {
        ...mapActions(['loginPromise']),
        ...mapMutations('send', ['addReceiverList']),
        /* 过滤没有绑定过上上签的微信账号*/
        getThirdUserIds(userList, data) {
            const leftThirdUserIds = [];
            userList.forEach((item) => {
                const isExistId = data.some((it) => {
                    return it.thirdUserId === item.id;
                });
                if (!isExistId) {
                    leftThirdUserIds.push(item.id);
                }
            });
            return leftThirdUserIds;
        },
        // 判断session是否过期再执行对应操作
        qyWxCheckSession(callback) {
            const _self = this;
            (this.isWeWork ? uni.qy.checkSession : uni.checkSession)({
                success: function() {
                    callback & callback();
                },
                fail: function() {
                    _self.loginPromise().then(() => {
                        callback && callback();
                    });
                },
            });
        },
        getSaasAccountByWxId(thirdCorpId, userList) {
            const thirdUserIds = [];
            userList.forEach((item) => {
                thirdUserIds.push(item.id);
            });
            const params = {
                thirdCorpId,
                thirdUserIds,
            };
            return getSSQAccountByWxId(params);
        },
        // 筛选出微信选择人的头像
        filterAvatar(thirdUserId) {
            const temp = this.selectEnterpriseContactData.filter(item => item.id === thirdUserId);
            return temp[0] ? temp[0].avatar : null;
        },
        /* 通讯录添加完之后获取这批人的相关信息，个人和企业都可以用这个接口*/
        getReceiversInfo(accounts, userType) {
            const self = this;

            accounts.forEach((item) => {
                let addReceiverInfo;
                if (self.sendType === 'localSend') {
                    const initInfo = {
                        routeOrder: self.receiverList.length + 1, // 顺序签-顺序
                        userType: userType,
                        fullName: item.fullName,
                        userName: userType === 'ENTERPRISE' && item.userName ? item.userName : item.fullName, // 考虑到企业经办人是这个字段
                        userId: item.userId,
                        enterpriseName: userType === 'ENTERPRISE' ? item.enterprises[0].entName : '',
                        enterpriseId: userType === 'ENTERPRISE' ? item.enterprises[0].entId : '',
                        enterprises: userType === 'ENTERPRISE' ? item.enterprises : [],
                        userAccount: item.account,
                        requireIdentityAssurance: true, // 默认不实名，中原地产要求wx2730a10487f9df56  SAAS-5162要求改回默认实名
                        userAvatar: self.filterAvatar(item.thirdUserId), // 头像
                        isBlank: false,
                    };
                    addReceiverInfo = getLocalSendReceiverInfo(initInfo);
                } else {
                    const initInfo = {
                        routeOrder: self.receiverList.length + 1, // 顺序签-顺序
                        userType: userType,
                        userInfo: {
                            userName: userType === 'ENTERPRISE' && item.userName ? item.userName : item.fullName, // 考虑到企业经办人是这个字段
                            userId: item.userId,
                            userAccount: item.account,
                            enterpriseName: '',
                            enterpriseId: '',
                        },
                        isBlank: false,
                        mustCCUser: this.showCCBtn,
                    };
                    addReceiverInfo = getTemplateSendReceiverInfo(initInfo);
                }

                self.addReceiverList(addReceiverInfo);
            });
        },
        addReceiver(userType) {
            if (!this.isWeWork || (this.isWeWork && !this.hasUseWeWorkAddressRight)) {
                uni.navigateTo({
                    url: `/subSendViews/addReceiver/index?createUserType=${userType}&showCCBtn=${this.showCCBtn}`,
                });
                return;
            }
            const self = this;
            this.qyWxCheckSession(() => { // 获取头像前判断session是否过期
                uni.qy.selectEnterpriseContact({
                    fromDepartmentId: 0, // 必填，-1表示打开的通讯录从自己所在部门开始展示, 0表示从最上层开始
                    mode: self.sendType === 'localSend' && userType === 'PERSON' ? 'multi' : 'single', // 必填，选择模式，single表示单选，multi表示多选
                    type: ['user'], // 必填，选择限制类型，指定department、user中的一个或者多个
                    success: function(res) {
                        const userList = res.result.userList;
                        self.selectEnterpriseContactData = userList;
                        /* thirdCorpId本人所在的企业微信的企业id*/
                        /* 根据微信id获取上上签账号*/
                        if (userList.length === 0) {
                            return self.$toast.error('请至少选择一个签约方');
                        }
                        const thirdCorpId = uni.getStorageSync('thirdCorpId');
                        self.getSaasAccountByWxId(thirdCorpId, userList).then(({ data }) => {
                            if (data.length > 0 && (userType === 'PERSON' || (userType === 'ENTERPRISE' && !!data[0].enterprises && data[0].enterprises.length))) {
                                self.getReceiversInfo(data, userType);
                            }
                            /* 使用模板的时候签约方也有可能是个人或者企业，这个userType是由设置模板的时候指定的*/
                            if (userList.length - data.length > 0) {
                                uni.showModal({
                                    content: `您所选的签约方中有${userList.length - data.length}人还未绑定上上签小程序，不能向对方发送合同。是否发送绑定通知？`,
                                    confirmText: '发送',
                                    confirmColor: '#127fd2',
                                    success: async function(res) {
                                        if (res.confirm) {
                                            const params = {
                                                corpId: thirdCorpId,
                                                sendUserName: self.qwxUserName,
                                                userIdList: self.getThirdUserIds(userList, data),
                                            };
                                            const { data: result } = await postWxMsg(params);
                                            result.code === '140001' && self.$toast.success('发送成功');
                                        }
                                    },
                                });
                            } else if (userType === 'ENTERPRISE' && !data[0]?.enterprises) {
                                /* 针对添加企业签约方，如果只有个人账号并没有注册企业的话，则告知客户不能添加*/
                                self.$toast.error(`${data[0]?.fullName || '该账号'}还没有添加企业，请重新选择`);
                            }
                        });
                    },
                    fail: () => {
                        uni.navigateTo({
                            url: `/subSendViews/addReceiver/index?createUserType=${userType}&showCCBtn=${this.showCCBtn}`,
                        });
                    },
                });
            });
        },
    },
};
</script>
<style lang="scss">
.signer-info {
    &__btn {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40rpx;
        height: 100rpx;
        line-height: 100rpx;
        color: $--color-primary;
        background-color: $--color-white;
        border-bottom: 1rpx solid $--border-color-lighter;
    }
    &__line {
        color: $--border-color-lighter;
        transform:scaleY(1.2);
    }
}
</style>
