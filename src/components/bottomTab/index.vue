<template>
    <view class="bottom-tab">
        <view class="menu-list">
            <view
                v-for="(item, index) in tabList"
                class="list-item"
                :class="{'center-tab': index === 2, 'active': currentIndex === index}"
                @click="handlePush(item, index)"
                :key="index"
            >
                <view class="list-item__img">
                    <img :src=" currentIndex === index ? item.selectedIconPath : item.iconPath" />
                </view>
                <view class="list-item__text">
                    {{ item.text }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import homeImg from 'src/static/home.png';
import homeActiveImg from 'src/static/home_active.png';
import sendImg from 'src/static/send.png';
import sendActiveImg from 'src/static/send_active.png';
import basicImg from 'src/static/basic_home.png';
import basicActiveImg from 'src/static/basic_home_active.png';
import riskImg from 'src/static/risk.png';
import riskActiveImg from 'src/static/risk_active.png';
import accountImg from 'src/static/account.png';
import accountActiveImg from 'src/static/account_active.png';
export default {
    props: {
        currentIndex: { // 当前选中的tab项
            type: Number,
            default: 2,
        },
    },
    data() {
        return {
            tabList: [
                {
                    'text': '合同列表',
                    'pagePath': 'views/home/<USER>',
                    'iconPath': homeImg,
                    'selectedIconPath': homeActiveImg,
                },
                {
                    'text': '发送合同',
                    'pagePath': 'views/sendGuide/index',
                    'iconPath': sendImg,
                    'selectedIconPath': sendActiveImg,
                },
                {
                    'text': '首页',
                    'pagePath': 'views/basic/index',
                    'iconPath': basicImg,
                    'selectedIconPath': basicActiveImg,
                },
                // #ifdef MP-WEIXIN
                {
                    'text': '风险判断',
                    'pagePath': 'views/riskJudge/index',
                    'iconPath': riskImg,
                    'selectedIconPath': riskActiveImg,
                },
                // #endif
                {
                    'text': '账号管理',
                    'pagePath': 'views/account/index',
                    'iconPath': accountImg,
                    'selectedIconPath': accountActiveImg,
                },
            ],
            showTabBar: false,
        };
    },
    methods: {
        handlePush(item, index) {
            if (index === this.currentIndex) {
                return;
            }
            uni.switchTab({
                url: this.tabList[index].pagePath,
            });
        },
    },
};
</script>

<style lang="scss">
.bottom-tab {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 70px;
    z-index: 9;
    background: $--color-white;
    .menu-list {
        display: flex;
        justify-content: space-between;
        height: 70px;
        //border-top: 1px solid $--border-color-light;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        .list-item {
            width: 20vw;
            padding-top: 10px;
            &__img {
                width: 100%;
                height: 28px;
                text-align: center;
                img {
                    width: 24px;
                    height: 24px;
                }
            }
            &__text {
                height: 18px;
                line-height: 18px;
                font-size: 12px;
                text-align: center;
            }
            &.active {
                color: $--color-primary;
                &.center-tab .list-item__img{
                    background: $--color-primary;
                }
            }
            &.center-tab {
                width: 60px;
                height: 60px;
                margin-top: -20px;
                padding-top: 0;
                border-radius: 30px;
                border-top: 1px solid $--border-color-light;
                background: $--color-white;
                .list-item__img {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-left: 8px;
                    margin-top: 8px;
                    width: 44px;
                    height: 44px;
                    border-radius: 22px;
                    background: #F0F1F5;
                }
                .list-item__text {
                    margin-top: 6px;
                }

            }
        }
    }
}
</style>
