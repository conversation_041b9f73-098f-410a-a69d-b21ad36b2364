<template>
    <CenterPopup class="cancel-pay-popup">
        <view class="cancel-pay-popup__content">
            <view class="top-text">限时优惠</view>
            <view class="discount-info">
                <img src="~img/pay_dicount.png" class="discount-img" alt="">
            </view>
            <view class="pay-count">
                <view class="desc">在2分钟内完成支付</view>
                <view class="timer-comp">
                    <view class="number-item">
                        <view class="num"><text class="num-text">0{{ Math.floor(timer / 60) }}</text></view>
                        <view class="unit"><text class="num-text">min</text></view>
                    </view>
                    <view class="seperator">:</view>
                    <view class="number-item">
                        <view class="num"><text class="num-text">{{ secText }}</text></view>
                        <view class="unit"><text class="num-text">sec</text></view>
                    </view>
                </view>
            </view>
            <view class="gap">
                <view class="gap-left"></view>
                <view class="gap-line"></view>
                <view class="gap-right"></view>
            </view>
            <view class="jump-row">
                <view class="jump-row__btn" @click="handleContinue">
                    继续支付
                </view>
            </view>

        </view>
        <view class="cancel-pay-popup__close">
            <text class="icon-ic_close" @click="handleClose"></text>
        </view>
    </CenterPopup>
</template>

<script>
import CenterPopup from 'components/centerPopup';
export default {
    name: 'ActivityPopup',
    components: { CenterPopup },
    props: {
        pageName: {
            type: String,
            default: '',
        },
        orderInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            timer: 119,
            interval: null,
        };
    },
    computed: {
        secText() {
            const secVal = this.timer > 60 ? this.timer - 60 : this.timer;
            return `${secVal < 10 ? '0' : ''}${secVal}`;
        },
    },
    methods: {
        handleContinue() {
            this.handleSensors('Mp_CommodityListWindow_BtnClick', '继续');
            this.$emit('continue');
        },
        handleSensors(eventName, iconName) {
            this.$sensors.track({
                eventName,
                eventProperty: {
                    page_name: this.pageName,
                    activity_id: this.pageName === '活动页' ? '10th' : '',
                    activity_name: this.pageName === '活动页' ? '10周年活动' : '',
                    window_name: '支付二次确认弹窗',
                    order_id: this.orderInfo.orderId,
                    commodity_id: this.orderInfo.productPackageId,
                    icon_name: iconName,
                },
            });
        },
        handleClose() {
            this.handleSensors('Mp_CommodityListWindow_BtnClick', '关闭');
            this.$emit('close');
        },
    },
    mounted() {
        uni.setStorageSync('cancelPopupDate', new Date().getTime());
        this.handleSensors('Mp_CommodityListWindow_PopUp');
        this.interval = setInterval(() => {
            if (this.timer > 0) {
                this.timer--;
            } else {
                clearInterval(this.interval);
            }
        }, 1000);
    },
    beforeDestroy() {
        clearInterval(this.interval);
    },
};
</script>

<style lang="scss" scoped>
$active-color: #DD9240;
.cancel-pay-popup {
    &__content {
        width: 180px;
        padding: 10px 0;
        background: $--color-white;
        background: #FFFFFF;
        border-radius: 6px;
        text-align: center;
        color: $active-color;
        .top-text {
            font-size: 14px;
    }
        .discount-info {
            .discount-img{
                width: 111px;
                height: 42px;
                margin: 12px 0;
            }
        }
        .pay-count {
            //margin-top: 5px;
            .desc {
                font-size: 12px;
                color: $--color-text-regular;
            }
            .timer-comp {
                display: flex;
                justify-content: center;
                padding: 5px 0;
                text-align: center;
                .num {
                    display: flex;
                    justify-content: center;
                    width: 26px;
                    padding: 1px;
                    background: url("@/assets/images/timer-bg.png") no-repeat;
                    background-size: cover;
                }
                .unit {
                    font-size: 8px;
                }
                .seperator {
                    width: 20px;
                }
            }
        }
        .gap{
            height: 12px;
            display: flex;
            align-items: center;
            &-left, &-right {
                width: 7px;
                height: 100%;
                background: rgba(0, 0, 0, .5);
            }
            &-left {
                border-radius: 0 7px 7px 0;
            }
            &-right {
                border-radius: 7px 0 0 7px;
            }
            &-line {
                flex: 1;
                border-top: 1px dashed $active-color;
                height: 1px;
            }

        }
        .jump-row {
            display: flex;
            justify-content: center;
            padding: 10px 0 5px;
            &__btn {
                width: 120px;
                line-height: 32px;
                color: $--color-white;
                border-radius: 25px;
                background: $active-color
            }
        }
    }
    &__close {
        margin-top: 12px;
        text-align: center;
        line-height: 10px;
        .icon-ic_close {
            padding: 5px;
            font-size: 8px;
            color: $--color-white;
            border: 1px solid $--color-white;
            border-radius: 11px;
        }
    }
}
</style>
