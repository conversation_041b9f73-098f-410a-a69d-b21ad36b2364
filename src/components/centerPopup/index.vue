<template>
    <view class="center-popup">
        <view class="center-popup__body">
            <view class="center-popup__body-content">
                <slot></slot>
            </view>
        </view>
        <view class="center-popup__shadow"></view>
    </view>
</template>

<script>
export default {
    name: 'CenterPopup',
};
</script>

<style lang="scss" scoped>
.center-popup {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    &__body {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 99;
        //&-content {
        //    text-align: center;
        //}
    }
    &__shadow {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        background: rgba(0, 0, 0, 0.5);
    }
}
</style>
