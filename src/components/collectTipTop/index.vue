<template>
    <div class="collect-tip" v-if="!hasAdd && showCollectTip && !collectTipHasShow">
        <view class="collect-tip__arrow-icon"></view>
        <view class="collect-tip__text-context">
            {{ collectTipText }}
            <text class="close icon-ic_close" @click="handleCloseCollectTip"></text>
        </view>
    </div>
</template>

<script>
export default {
    name: 'CollectTipTop',
    props: {
        storageKey: {
            type: String,
            default: 'collectTipHasShow',
        },
        guideWordWX: {
            type: String,
            default: '添加到"我的小程序"，合同签发轻松便捷',
        },
        guideWordAlipay: {
            type: String,
            default: '点击收藏，合同签发轻松便捷',
        },
    },
    data() {
        return {
            hasAdd: false,
            showCollectTip: true,
        };
    },
    computed: {
        collectTipHasShow() {
            return uni.getStorageSync(this.storageKey) === '1';
        },
        collectTipText() {
            let text = this.guideWordWX;
            // #ifdef MP-ALIPAY
            text = this.guideWordAlipay;
            // #endif
            return text;
        },
    },
    methods: {
        handleCloseCollectTip() {
            uni.setStorageSync(this.storageKey, '1');
            this.showCollectTip = false;
        },
    },
    mounted() {
        // #ifdef MP-WEIXIN
        uni.checkIsAddedToMyMiniProgram({
            success: res => {
                this.hasAdd = res.added;
            },
            fail: res => {
                console.log('checkIsAdded fail = ', res);
            },
        });
        // #endif
        // #ifdef MP-ALIPAY
        uni.isCollected({
            success: res => {
                this.hasAdd = res.isCollected;
            },
            fail: res => {
                console.log('checkIsCollect fail = ', res);
            },
        });
        // #endif
    },
};
</script>

<style lang="scss" scoped>
.collect-tip {
    position: fixed;
    top: 0;
    right: 10px;
    &__arrow-icon {
        position: relative;
        left: 197px;
        margin-top: -8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom: 8px solid $--color-black;
    }
    &__text-context {
        padding: 0 10px;
        height: 30px;
        line-height: 28px;
        font-size: 12px;
        color: $--color-white;
        border-radius: 4px;
        background: $--color-black;
        .close {
            font-size: 12px;
            padding-left: 10px;
        }
    }
}
</style>
