<template>
    <uni-popup
        class="cant-sign-tip"
        ref="cantBatchSignPopUp"
        type="center"
        @maskClick="popupCancel"
    >
        <div class="cant-sign-tip__content">
            <div class="cant-sign-tip__head">
                无法批量签署
            </div>
            <div class="cant-sign-tip__main">
                <p class="tip-1 tip">
                    由于您尚未获取必要权限签署这批合同（或其他原因），无法执行批量签署的操作。
                </p>
                <p class="tip-2 tip">
                    建议你先试着单独签署一份合同，找到不能批量签署的原因。
                </p>
                <div class="reason-tip">
                    <h2>常见原因：</h2>
                    <p>你尚未加入签约方企业，无法签署它的合同</p>
                    <p>你还没有印章，无法签署合同</p>
                    <p>你的企业规定某些发件方企业所发送的合同，需要额外签署授权才能签署，但你缺少该授权</p>
                </div>
                <div class="to-sign-tip">
                    在待我签署页，点击列表右侧的“前往签署”操作，逐个完成合同签署。在单独签署时，系统将引导您顺利获取权限，完成合同签署。
                </div>
            </div>
            <div class="cant-sign-tip__btns">
                <p
                    class="cant-sign-tip__btn"
                    @click="popupCancel"
                >
                    我知道了
                </p>
            </div>
        </div>
    </uni-popup>
</template>

<script>
export default {
    props: {
        dialogVisible: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {};
    },
    watch: {
        dialogVisible: {
            handler(val) {
                if (val) {
                    this.popUpOpen();
                } else {
                    this.popupCancel();
                }
            },
        },
    },
    methods: {
        popUpOpen() {
            this.$refs.cantBatchSignPopUp.open();
        },
        popupCancel() {
            this.$refs.cantBatchSignPopUp.close();
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss">
.cant-sign-tip__content {
    background: #fff;
    margin: 0 50rpx;
    border-radius: 20rpx;
    padding: 30rpx;
}
.cant-sign-tip__head {
    text-align: center;
    color: #333;
    font-size: 32rpx;
}
.cant-sign-tip__main {
    padding: 20rpx 0;
    .tip {
        color: #333;
        font-size: 30rpx;
        &.tip-1 {
            text-indent: 80rpx;
        }
        &.tip-2 {
            margin-top: 20rpx;
        }
    }
    .reason-tip {
        padding-top: 20rpx;
        color: #666;
        font-size: 26rpx;
        h2 {
            font-size: 26rpx;
            font-weight: normal;
        }
        p {
            text-indent: 50rpx;
        }
    }
    .to-sign-tip {
        font-size: 26rpx;
        margin-top: 20rpx;
        padding: 20rpx;
        color: #666;
        background: #f6f6f6;
    }
}
.cant-sign-tip__btn {
    text-align: center;
    color: #127FD2;
    height: 60rpx;
    line-height: 60rpx;
}
</style>
