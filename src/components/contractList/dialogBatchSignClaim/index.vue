<template>
    <uni-popup
        class="batch-sign-claim"
        ref="batchSignClaimPopUp"
        type="bottom"
        @maskClick="popupCancel"
    >
        <view class="batch-sign-claim__wrapper">
            <view class="batch-sign-claim__info">
                批量签署的合同中包含了待认领合同，请确认这些合同是否应该由您来签署！
            </view>
            <div class="batch-sign-claim__main">
                <p class="batch-sign-claim__title batch-sign-claim__line">
                    需要认领签署的合同
                    <span class="batch-sign-claim__title_add">
                        {{ checkedClaimContractList.length }}份
                    </span>
                </p>
                <div class="batch-sign-claim__checkbox-group">
                    <div
                        v-for="item in claimContracts"
                        :key="item.contractId"
                        class="batch-sign-claim__checkbox batch-sign-claim__line"
                    >
                        <radio
                            class="checkbox"
                            @click.native="handleSelectItem(item.contractId)"
                            :value="item.contractId"
                            :checked="checkedClaimContractList.includes(item.contractId)"
                            color="#127FD2"
                        />
                        {{ item.contractName }}
                    </div>
                </div>
                <p class="batch-sign-claim__title">
                    其他需要签署的合同
                    <span class="batch-sign-claim__title_add">
                        {{ signContracts.length }}份
                    </span>
                </p>
            </div>
            <div class="batch-sign-claim__btns">
                <p
                    class="batch-sign-claim__btn batch-sign-claim__btn_cancle"
                    @click="popupCancel"
                >
                    取消
                </p>
                <p
                    class="batch-sign-claim__btn batch-sign-claim__btn_sign"
                    @click="handleCheckDone"
                >
                    去签署{{ totalNum }}份
                </p>
            </div>
        </view>
    </uni-popup>
</template>

<script>
import { claimBatchAjax } from 'src/api/doc.js';
import { mapActions } from 'vuex';
export default {
    props: {
        dialogBatchSignClaimShow: {
            default: false,
            type: Boolean,
        },
        claimContracts: {
            type: Array,
            default: function() {
                return [];
            },
        },
        signContracts: {
            type: Array,
            default: function() {
                return [];
            },
        },
    },
    data() {
        return {
            show: false,
            checkedClaimContractList: [],
        };
    },
    computed: {
        totalNum() {
            return this.checkedClaimContractList.length + this.signContracts.length;
        },
        contractList() {
            return this.claimContracts.map(a => ({
                text: a.contractName,
                value: a.contractId,
            }));
        },
    },
    watch: {
        dialogBatchSignClaimShow: {
            handler(val) {
                if (val) {
                    this.popUpOpen();
                } else {
                    this.popupCancel();
                }
            },
        },
        claimContracts: {
            handler(val) {
                if (val && val.length > 0) {
                    this.checkedClaimContractList = val.map(item => item.contractId);
                }
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions(['setLoading']),
        popUpOpen() {
            this.$refs.batchSignClaimPopUp.open();
        },
        popupCancel() {
            this.$refs.batchSignClaimPopUp.close();
            this.$emit('close');
        },
        handleSelectItem(contractId) {
            if (this.checkedClaimContractList.includes(contractId)) {
                this.checkedClaimContractList.splice(this.checkedClaimContractList.indexOf(contractId), 1);
            } else {
                this.checkedClaimContractList.push(contractId);
            }
        },
        // 保存选中标签
        handleCheckDone() {
            if (this.checkedClaimContractList.length > 0) {
                this.setLoading(true);
                claimBatchAjax(this.checkedClaimContractList)
                    .then(() => {
                        this.handleToSign();
                    }).finally(() => {
                        this.setLoading(false);
                    });
            } else {
                this.handleToSign();
            }
        },
        handleToSign() {
            this.$emit(
                'toSign',
                [...this.checkedClaimContractList, ...this.signContracts.map(item => item.contractId)],
            );
        },
    },
};
</script>

<style lang="scss">
.batch-sign-claim {
    height: 380px;
    &__wrapper {
        background-color: #fff;
    }
    &__info {
        box-sizing: border-box;
        padding: 9px 10px;
        width: 100%;
        height: 50px;
        line-height: 16px;
        background: #FFF3F3;
        font-size: 12px;
        color: #FF7B7B;
        text-align: left;
    }
    &__main {
        height: 265px;
        overflow-y: scroll;
    }
    &__line {
        border-bottom: 1px solid #E0E1E3;
    }
    &__title {
        position: relative;
        height: 45px;
        padding-left: 20px;
        padding-right: 20px;
        line-height: 45px;
        font-size: 15px;
        font-weight: bold;
        color: #333;
    }
    &__title_add {
        position: absolute;
        right: 20px;
        font-weight: normal;
        color: #999;
    }
    &__checkbox {
        height: 45px;
        padding-left: 20px;
        padding-right: 20px;
        line-height: 45px;
        font-size: 14px;
        .van-checkbox__label {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .checkbox {
            transform: scale(0.7);
        }
    }
    &__btns {
        display: flex;
        padding: 10rpx 20rpx;
        gap: 30rpx;
    }
    &__btn {
        height: 45px;
        border-radius: 8px;
        line-height: 45px;
        text-align: center;
        font-size: 16px;
    }
    &__btn_sign {
        flex: 1 1 230px;
        background: #127FD2;
        color: #fff;
    }
    &__btn_cancle {
        flex: 1 1 100px;
        margin-right: 5px;
        background: #E8F5FF;
        color: #127FD2;
    }
}
</style>
