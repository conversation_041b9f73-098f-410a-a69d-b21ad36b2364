<template>
    <scroll-view class="contract-list" :scroll-y="true" @scrolltolower="loadMore">
        <div class="contact-content" :class="isSignTab ? 'has-batch-options' : ''">
            <div class="contract-list__item" v-for="contract in contractList" :key="contract.contractId">
                <div class="contract-list__item-check"
                    v-if="isSignTab"
                    @click.capture="handleSelectItem(contract.contractId)"
                >
                    <label class="checkbox-content">
                        <radio
                            class="checkbox"
                            :key="contract.contractId"
                            :value="contract.contractId"
                            :checked="selectedContractIds.includes(contract.contractId)"
                            :disabled="!selectedContractIds.includes(contract.contractId) &&
                                selectedContractIds.length === MAX_OPTION_NUM"
                            activeBackgroundColor="#127FD2"
                            color="#127FD2"
                        />
                    </label>
                </div>
                <div class="contract-list__item-detail" @click="toContractDetail(contract)">
                    <div class="contract-list__item-name ellipsis">{{ contract.contractName }}</div>
                    <button class="contract-list__item-operate-btn opt-btn" type="primary" v-if="showResendBtn(contract)" @click.stop="handleResend(contract)">重新发起</button>
                    <div class="contract-list__item-sender ellipsis">
                        <span>发件方：</span>
                        {{ contract.sender }}
                    </div>
                    <div class="contract-list__item-signer ellipsis">
                        <span>签约方：</span>
                        {{ contract.signer }}
                    </div>
                    <div class="contract-list__item-operate">
                        {{ operateBtnText }}
                    </div>
                </div>
            </div>
            <div class="contract-list__empty" v-if="!contractList.length && !loading">
                <img :src="emptySrc" alt="">
                <p>暂无内容</p>
            </div>
        </div>
        <div class="contract-list__options" v-if="isSignTab">
            <button class="opt-btn" type="primary" @click="handleSelectAll">{{ selectedAllText }}</button>
            <button class="opt-btn" type="primary" @click="handleBatchSign">批量签署</button>
        </div>
        <DialogBatchSignClaim
            :dialog-batch-sign-claim-show="dialogBatchSignClaimShow"
            :claim-contracts="claimContracts"
            :sign-contracts="signContracts"
            @close="dialogBatchSignClaimShow = false"
            @toSign="handleBatchClaimDoneToSign"
        >
        </DialogBatchSignClaim>
        <CantBatchSignTipDialog
            :dialog-visible="showDialogCantBatchSign"
            @close="showDialogCantBatchSign=false"
        />
    </scroll-view>
</template>

<script>
import DialogBatchSignClaim from './dialogBatchSignClaim/index.vue';
import CantBatchSignTipDialog from './cantBatchSignTip/index.vue';
import _get from 'lodash/get';
import { mapState, mapActions, mapMutations } from 'vuex';
import {
    batchSignAjax,
    getAllContractList,
    getContractList,
    getContractTags,
    getNeedAuthenticationListAjax, saveBatchSignContractIds,
} from 'api/doc.js';
const pageName = {
    1: '待我签署',
    2: '待我审批',
    3: '他人签署',
    4: '签约完成',
};
export default {
    components: {
        DialogBatchSignClaim,
        CantBatchSignTipDialog,
    },
    props: {
        isAllContract: {
            type: Boolean,
            default: false,
        },
        isCustomSearch: {
            type: Boolean,
            default: false,
        },
        searchParams: {
            type: Object,
            default: () => ({}),
        },
        extraParams: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            onShowNum: 0,
            totalPage: 0,
            currentPage: 1,
            contractList: [],
            emptySrc: require('img/empty.png'),
            selectedContractIds: [],
            signContracts: [],
            claimContracts: [],
            checkedContractData: [],
            allowBatchSignContracts: [],
            needSatisfyRequirementContracts: [],
            batchSignId: '',
            dialogBatchSignClaimShow: false,
            showDialogCantBatchSign: false,
            MAX_OPTION_NUM: 50,
        };
    },
    computed: {
        ...mapState(['isWeWork', 'loading']),
        ...mapState('user', ['commonHeaderInfo']),
        operateBtnText() {
            if (this.isAllContract) {
                return '';
            } else {
                return ['', '前往签署>', '前往审批>'][this.searchParams.tabType] || '';
            }
        },
        isSignTab() {
            return this.searchParams.tabType === '1';
        },
        selectedAllText() {
            return '全选' + (this.selectedContractIds.length ? `（${this.selectedContractIds.length}）` : '');
        },
        showResendBtn() {
            return (contract) => {
                return this.isAllContract && contract.operations.includes('RESEND') && ![1, 2, 3].includes(contract.contractSendType);
            };
        },
    },
    methods: {
        ...mapActions(['setLoading', 'loginPromise', 'switchEntId']),
        ...mapMutations('send', ['setSendType', 'setIfDynamicTemplate']),
        ...mapActions('user', ['getHeadInfo']),
        async handleResend(contract) {
            // const { contractSendType, contractId } = contract;
            const { contractId } = contract;
            // contractSendType 1-动态模板、2-静态模板、3-普通合同发送、4-开通新模版合同  异常情况，后端返回null
            // if ([1, 2, 3].includes(contractSendType)) {
            //     const toastMap = {
            //         1: '动态模板合同',
            //         2: '老模板合同',
            //         3: '本地合同',
            //     };
            //     return this.$toast.error(`小程序不支持重新发起${toastMap[contractSendType]}`);
            // }
            uni.showLoading();
            try {
                await this.handleSender(contractId);
                await this.doResend(contractId);
            } catch (e) {
                console.log(e);
            }
        },
        async handleSender(contractId) {
            const res = await this.$http.get(
                `/contract-api/contracts/${contractId}/sender`,
            ); // 考虑代理发送场景，单独去查发件人信息
            const senderEntId = res.data.senderEntId;
            if (senderEntId !== this.commonHeaderInfo?.currentEntId) {
                // CFD-10965 主体不一致 切换
                await this.switchEntId(senderEntId);
                await this.getHeadInfo();
            }
        },
        async doResend(contractId) {
            const res = await this.$http.post(`/template-api/v2/draft/init-from-contract-resend`, {
                contractId,
            });
            const newContractId = res.data && res.data.draftId;
            this.setSendType('templateSend');
            this.setIfDynamicTemplate(false);
            uni.navigateTo({
                url: `/subSendViews/settingTemplateDoc/index?draftId=${newContractId}`,
            });
        },
        handleSelectItem(contractId) {
            if (this.selectedContractIds.length === this.MAX_OPTION_NUM &&
                !this.selectedContractIds.includes(contractId)) {
                this.$toast.error(`已选择${this.MAX_OPTION_NUM}份合同，达到单次操作上限`);
                return;
            }
            if (this.selectedContractIds.includes(contractId)) {
                this.selectedContractIds.splice(this.selectedContractIds.indexOf(contractId), 1);
            } else {
                this.selectedContractIds.push(contractId);
            }
        },
        handleBatchClaimDoneToSign(contractIds) {
            this.dialogBatchSignClaimShow = false;
            this.handleMultProxy(contractIds);
        },
        handleSelectAll() {
            if (this.loading) {
                return;
            }
            // 如果已经全选则清空，单次最多操作50项
            if (this.selectedContractIds.length === this.contractList.length || this.selectedContractIds.length ===
                this.MAX_OPTION_NUM) {
                this.selectedContractIds = [];
                return;
            }
            this.selectedContractIds = this.contractList.filter((c, i) => i < this.MAX_OPTION_NUM).map(a => a.contractId);
        },
        handleBatchSign() {
            if (this.selectedContractIds.length === 0) {
                this.$toast.error('请至少选择一份合同');
                return;
            }

            if (this.selectedContractIds.length === 1) {
                this.toContractDetail(this.contractList.find(a => a.contractId === this.selectedContractIds[0]));
                return;
            }

            // 整理合同数据
            this.checkedContractData = this.contractList.filter(a => this.selectedContractIds.includes(a.contractId))
                .map(({ contractId, contractName, operations, claimRecipient }) => {
                    return {
                        contractId,
                        contractName,
                        operations,
                        claimRecipient,
                    };
                });

            // 需要我签署的合同
            this.signContracts = this.checkedContractData
                .filter(({ operations }) => {
                    return operations.includes('SIGN') || operations.includes('PROXY_SIGN');
                });
            // 可以批量认领的合同
            this.claimContracts = this.checkedContractData
                .filter(({ operations, claimRecipient }) => {
                    // 是否是多业务线认领的合同，多业务线不支持批量认领
                    const isMultiLine = ((claimRecipient || {}).entList || []).length > 1;
                    return !operations.includes('SIGN') && operations.includes('CLAIM') && !isMultiLine;
                });
            if (this.claimContracts.length > 0) {
                this.dialogBatchSignClaimShow = true;
            } else {
                this.handleMultProxy();
            }
        },
        // 批量代签署
        handleMultProxy(contractIds) {
            const proxyContractIds =
                this.checkedContractData.filter(item => item.operations.includes('PROXY_SIGN')).map(item =>
                    item.contractId);
            uni.showLoading({
                title: '批量签署中...',
                mask: true,
            });
            if (!proxyContractIds.length) {
                return this.submitBatchSign();
            }
            this.$http.post('/contract-api/contracts/proxy-sign/batch-ready-to-do-proxy-sign',
                            contractIds || proxyContractIds, { noToast: true })
                .finally(() => {
                    this.submitBatchSign();
                });
        },
        submitBatchSign() {
            batchSignAjax(this.selectedContractIds)
                .then(async(res) => {
                    const resData = res.data.result;
                    const { forbiddenBatchSignContracts, allowBatchSignContracts, batchSignId } = resData;
                    this.allowBatchSignContracts = allowBatchSignContracts;
                    this.batchSignId = batchSignId;
                    const contractIds = (allowBatchSignContracts || []).map(item =>  {
                        return item.contractId;
                    });
                    await saveBatchSignContractIds({
                        batchSignId,
                        contractIds: contractIds,
                    });
                    await getNeedAuthenticationListAjax({
                        batchSignId,
                        contractIds: this.selectedContractIds,
                    }).then(resList => {
                        const listData = resList.data.result;
                        this.needSatisfyRequirementContracts = listData.needSatisfyRequirementContracts;
                    });
                    uni.hideLoading();
                    if (forbiddenBatchSignContracts.length > 0) {
                        const titleStr = forbiddenBatchSignContracts.reduce((prev, curr) => {
                            return `${prev}《${curr.contractTitle}》，`;
                        }, '');
                        this.showDialogForbbidenBatchSign(titleStr);
                    } else {
                        this.batchSignJump();
                    }
                }).catch(() => {
                    uni.hideLoading();
                });
        },
        // 不能批量操作的合同弹框
        showDialogForbbidenBatchSign(titleStr) {
            uni.showModal({
                title: '批量签署',
                content: `${titleStr}无法进行批量签署，请单独签署`,
                confirmText: '我知道了',
                confirmColor: '#127fd2',
                showCancel: false,
                success: (res) =>  {
                    if (!res.cancel) {
                        this.batchSignJump();
                    }
                },
            });
        },
        // 批量签署跳转
        batchSignJump() {
            // 有可以批量签署的合同
            if (this.allowBatchSignContracts.length > 0) {
                // 有需要实名的合同，跳到实名页面
                let path = '';
                const query =
                    `?batchSignId=${this.batchSignId}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&isBestSignApplet=true`;

                if (this.needSatisfyRequirementContracts.length > 0) {
                    // 有需要实名的合同，跳到实名页面
                    path = '/doc/need-auth-contracts';
                } else {
                    // 无需要实名的合同，跳到批量签署页面
                    path = '/batch-sign';
                }
                uni.navigateTo({
                    url:
                        `/views/webviewRedirect/index?url=${encodeURIComponent(`${this.$http.baseURL}/mobile${path}${query}`)}`,
                });
            } else {
                // 无可批量签署的合同
                this.showDialogCantBatchSign = true;
            }
        },
        loadMore() {
            if (this.currentPage < this.totalPage && !this.loading) {
                this.currentPage++;
                this.init({ type: 'loadMore' });
            }
        },
        init({ type } = { type: 'init' }) {
            this.setLoading(true);
            if (this.isAllContract) {
                this.initAllContractList({ type });
            } else {
                // 检查是否登录，没有登录就不请求接口
                this.loginPromise()
                    .then(() => {
                        this.initContractList();
                    })
                    .catch(() => {
                        this.setLoading(false);
                    });
            }
        },
        async initContractList() {
            try {
                const contractResponse = await getContractList(this.searchParams, this.currentPage);
                this.totalPage = _get(contractResponse, 'data.result.totalPages', 0);
                const contractRecords = _get(contractResponse, 'data.result.results', []);
                const contractTagResponse = await getContractTags(contractRecords.map(item => item.contractId), this.searchParams);
                const contractTagList = _get(contractTagResponse, 'data.data', []);
                const contractStatusMap = { // 为了和web上报的数据保持一致
                    2: 'CREATED',
                    3: 'SENT',
                    4: 'REJECT',
                    5: 'COMPLETE',
                    7: 'REVOKE_CANCEL',
                    9: 'OVERDUE',
                    10: 'IN_SEND_APPROVAL',
                    11: 'SEND_APPROVAL_NOT_PASSED',
                    12: 'INVALID',
                };
                const contractList = contractRecords.map(el => {
                    const { tags = [], operations = [], reSendParams } = contractTagList.find(item => item.contractId                        ===
                        el.contractId);
                    return {
                        contractId: el.contractId,
                        contractName: el.contractTitle,
                        sender: el.senderName,
                        signer: el.entityNameOrAccount,
                        contractStatus: contractStatusMap[el.contractStatus] || '',
                        contractTagList: tags || [],
                        userType: this.searchParams.tabType === '1' ? '签署人' : (this.searchParams.tabType === '2' ? '审批人' : ''),
                        contractType: '',
                        companyId: '', // 接口数据未返回企业id
                        companyName: el.senderName, // 接口数据没办法知道是个人发件方还是企业发件方
                        operations,
                        claimRecipient: reSendParams?.claimRecipient,
                    };
                });
                this.contractList = this.contractList.concat(contractList);
                this.setLoading(false);
            } catch (err) {
                this.setLoading(false);
            }
        },
        async initAllContractList({ type }) {
            // 如果用户是主动搜索，并且不是加载更多，那么清空内容
            if (this.isCustomSearch && type !== 'loadMore') {
                this.contractList = [];
            }
            const contractResponse = await getAllContractList({
                searchEntryParams: this.searchParams,
                currentPage: this.currentPage,
                extraParams: {
                    ...this.extraParams,
                },
            });
            this.totalPage = _get(contractResponse, 'data.data.contractPage.pages', 0);
            const contractRecords = _get(contractResponse, 'data.data.contractPage.records', []);
            const contractTagResponse = await getContractTags(contractRecords.map(item => item.contractId), this.searchParams);
            const contractTagList = _get(contractTagResponse, 'data.data', []);
            const { account } = this.commonHeaderInfo.platformUser || {};

            const contractList = contractRecords.map(el => {
                const { tags = [], operations = [], reSendParam } = contractTagList.find(item => item.contractId  === el.contractId);
                return {
                    contractId: el.contractId,
                    contractName: this.getContractValue(el.fields, 'contract_title'),
                    sender: this.getContractValue(el.fields, 'sender'),
                    signer: this.getSigner(this.getContractValue(el.fields, 'signer')),
                    contractType: this.getContractValue(el.fields, 'contract_type'),
                    contractStatus: el.contractStatus || '',
                    contractTagList: tags.map(item => item.name),
                    userType: (el.senderEntId !== '0' && (el.senderEntId === this.commonHeaderInfo?.currentEntId)) || (el.senderEntId === '0' && (el.senderEntName === account)) ? '发件人' : null,
                    companyId: el.senderEntId,
                    companyName: el.senderEntId !== '0' ? el.senderEntName : '个人发件人',
                    operations: operations,
                    ...reSendParam,
                };
            });
            this.contractList = this.contractList.concat(contractList);
            this.setLoading(false);
        },
        getContractValue(fields, name) {
            const existItem = fields.filter(item => item.columnName === name);
            return existItem.length ? existItem[0].value : '';
        },
        getSigner(value) {
            try {
                const data = JSON.parse(value);
                return data.map(el => el.name).join('、');
            } catch (err) {
                return '';
            }
        },
        toContractDetail(contract) {
            const { contractId, contractName, companyId, companyName, userType, contractType, contractStatus, contractTagList } = contract;
            this.$sensors.track({
                eventName: 'Mp_ContractManageList_BtnClick',
                eventProperty: {
                    page_name: pageName[this.searchParams.tabType] || '所有合同',
                    first_category: '列表',
                    icon_name: contractName,
                    type: userType,
                    company_id: companyId,
                    company_name: companyName,
                    contract_id: contractId,
                    contract_name: contractName,
                    contract_type: contractType,
                    contract_status: contractStatus,
                    contract_tag_list: contractTagList,
                },
            });
            const contractUrl = encodeURIComponent(`${this.$http.baseURL}/mobile/doc/detail?isBestSignApplet=true&contractId=${contractId}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`);
            uni.navigateTo({
                url: `/views/webviewRedirect/index?url=${contractUrl}`,
            });
        },
    },
    created() {
        this.init();
    },
    onShow() {
        if (this.onShowNum) {
            this.init();
        }
        this.onShowNum = 1;
    },
};
</script>

<style lang="scss">
.contract-list{
    height: 100vh;
    padding: 20rpx 20rpx 0;
    box-sizing: border-box;
    background: $--background-color-base;
    &__item{
        background: $--color-white;
        padding: 30rpx 10rpx 20rpx;
        margin-bottom: 20rpx;
        position: relative;
        display: flex;
        align-items: center;
        &-check .checkbox {
            transform: scale(0.8);
        }
        &-detail{
            width: 100%;
            overflow: hidden;
            padding-left: 10rpx;
            position: relative;
        }
        &-name{
            font-size: 30rpx;
            font-weight: 500;
            margin-bottom: 16rpx;
            padding-right: 150rpx;
        }
        &-operate-btn{
            position: absolute;
            right: 0;
            width: 140rpx;
            margin: 0;
            top: 0;
            height: 60rpx;
            line-height: 60rpx;
            font-size: 24rpx;
            padding: 0;
        }
        &-sender, &-signer{
            width: 535rpx;
            font-size: 24rpx;
            color: $--color-info;
        }
        &-operate{
            position: absolute;
            font-size: 24rpx;
            color: $--color-text-regular;
            bottom: 20rpx;
            right: 36rpx;
        }
    }
    &__empty{
        position: absolute;
        width: 100%;
        top: 50%;
        margin-top: -300rpx;
        img{
            width: 100%;
        }
        p{
            text-align: center;
            margin-top: -120rpx;
            color: $--color-info;
        }
    }
    &__options {
        position: fixed;
        bottom: 0px;
        display: flex;
        gap: 60rpx;
        width: calc(100% - 40rpx);
        padding: 10rpx 20rpx; ;
        box-sizing: border-box;
        justify-content: space-between;
        .opt-btn {
            width: 300rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 32rpx;
            margin: 15rpx 0
        }
    }
    .has-batch-options {
        padding-bottom: 110rpx;
    }
}
</style>
