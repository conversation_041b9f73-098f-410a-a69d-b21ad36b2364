<template>
    <view
        class="custom-bottom-popup"
        :style="{
            'height': `${popContentHeight}px`,
        }"
    >
        <view class="custom-bottom-popup__resize">
            <text class="icon-zhixiangshang adjust-icon"
                v-if="popupLevel === 0"
                @click="changePopupStatus(false)"
            ></text>
            <text class="icon-quanping adjust-icon"
                v-if="popupLevel === 1"
                @click="changePopupStatus(false)"
            ></text>
            <text class="icon-zhixiangxia adjust-icon"
                v-if="popupLevel !== 0"
                @click="changePopupStatus(true)"
            ></text>
        </view>
        <scroll-view
            class="custom-bottom-popup__body"
            :style="{
                'height': `${popContentHeight}px`,
            }"
        >
            <slot name="content"></slot>
        </scroll-view>
    </view>
</template>
<script>
export default {
    name: 'CustomBottomPopup',
    props: {
        nextText: {
            type: String,
            default: '下一步',
        },
    },
    data() {
        return {
            windowHeight: 0,
            popupLevel: 1,
        };
    },
    computed: {
        popContentHeight() {
            const HEIGHT_ARR = [0, this.windowHeight / 2, this.windowHeight - 200];
            return HEIGHT_ARR[this.popupLevel] + 150;
        },
    },
    methods: {
        // Popup status 改变
        changePopupStatus(isDecrease = false) {
            this.popupLevel = isDecrease ? this.popupLevel - 1 : this.popupLevel + 1;
            this.$emit('popLevelChange');
        },
    },
    onReady() {
        uni.getSystemInfo({
            success: res => {
                this.windowHeight = res.windowHeight; // 获取屏幕高度供后续做popup高度半屏、全屏处理
            },
        });
    },
};
</script>
<style lang="scss">
.custom-bottom-popup {
    width: 100%;
    position: fixed;
    bottom: 0;
    height: 0;
    transition: height 200ms ease-in-out;
    &__resize {
        background: $--color-white;
        display: inline-block;
        float: right;
        margin-top: -40px;
        border-top-left-radius: 8rpx;
        padding: 15rpx;
        box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
        .adjust-icon {
            padding: 0 15rpx 10rpx;
            color: $--color-text-primary;
            font-size: 24px;
        }
    }
    &__body {
        width: 100%;
        background-color: $--color-white;
        box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
        transition: height 200ms ease-in-out;
    }
}
</style>
