<template>
    <view
        v-if="toastParam.show"
        class="toast"
    >{{ toastParam.message }}
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    computed: {
        ...mapState(['toastParam']),
    },
};
  </script>

<style>
.toast {
    background-color: #555;
    color: #FFFFFF;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    border-radius: 14rpx;
    max-width: 500rpx;
    padding: 20rpx 30rpx;
    line-height: 40rpx;
    font-size: 24rpx;
    text-align: center;
}
</style>
