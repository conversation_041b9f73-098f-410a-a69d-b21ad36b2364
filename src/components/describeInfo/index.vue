<template>
    <view class="describe-info">
        <view class="describe-info__title">合同描述信息</view>
        <view class="describe-info__content">
            <view class="describe-info__item describe-info__name">
                <view><text class="describe-info__necessary">*</text>合同名称</view>
                <view>
                    <input
                        placeholder="必填"
                        v-model="contractName"
                        class="describe-info__input"
                        placeholder-class="describe-info__input_placeholder"
                        @input="contractNameChange"
                    />
                </view>
            </view>
            <view class="describe-info__item">
                <view><text class="describe-info__unnecessary">*</text>合同类型</view>
                <view>
                    <picker @change="bindPickerChange" :value="curIndex" range-key="folderName" :range="contractTypes">
                        {{ contractTypes[curIndex].folderName }}
                    </picker>
                </view>
            </view>
            <view class="describe-info__item">
                <view><text class="describe-info__unnecessary">*</text>签约截止时间</view>
                <view>
                    <picker mode="date" :value="signDate" :start="startDate" :end="endDate" @change="signDateChange">
                        {{ signDate }}
                    </picker>
                </view>
            </view>
            <view class="describe-info__item" v-if="isShowContractDate">
                <view><text class="describe-info__unnecessary">*</text>合同到期时间</view>
                <view>
                    <picker mode="date" :value="contractDate" :start="startDate" :end="endDate" @change="contractDateChange">
                        {{ contractDate || '请选择' }}
                    </picker>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { getContractType } from 'api/send';
import { mapMutations, mapState } from 'vuex';
export default {
    props: {

    },
    data() {
        return {
            curIndex: 0,
            contractTypes: [{
                folderName: '未分类',
                folderId: '10000',
            }],
            signDate: '', // 签约截止时间
            startDate: '',
            endDate: '',
            contractDate: '', // 合同到期时间
            contractName: '',
        };
    },
    computed: {
        ...mapState('user', ['featureIds', 'commonHeaderInfo']),
        ...mapState('send', ['contractDescribeInfo']),
        isShowContractDate() {
            return this.featureIds.indexOf('18') > -1 && this.commonHeaderInfo.userType === 'Enterprise';
        },
    },
    methods: {
        ...mapMutations('send', ['setContractDescribeInfo']),
        getContractTypes() {
            const _self = this;
            getContractType().then((res) => {
                _self.contractTypes = res.data;
                this.setContractDescribeInfo({
                    ...this.contractDescribeInfo,
                    contractTypes: _self.contractTypes,
                });
            });
        },
        bindPickerChange(e) {
            this.curIndex = e.target.value;
            this.setContractDescribeInfo({
                ...this.contractDescribeInfo,
                curContractType: this.contractTypes[this.curIndex],
            });
        },
        // 获取时间，1) type = start/end为指定时间，2) type = time 时间，获取指定时间的XXXX-XX-XX格式
        getDate(type) {
            const isTime = type !== 'start' && type !== 'end';
            const date = isTime ? new Date(type) : new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();

            if (type === 'end') {
                year = year + 2;
            }
            month = month > 9 ? month : '0' + month;
            day = day > 9 ? day : '0' + day;
            return `${year}-${month}-${day}`;
        },
        computeDateAddCount(dateStart, count) {
            const date = new Date(Date.parse(dateStart) + count * 1000 * 60 * 60 * 24);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            month = month > 9 ? month : '0' + month;
            day = day > 9 ? day : '0' + day;
            return `${year}-${month}-${day}`;
        },
        initSignDate() {
            this.startDate = this.getDate('start');
            this.endDate = this.getDate('end');
            this.signDate = this.computeDateAddCount(this.startDate, 30);
            this.setContractDescribeInfo({
                ...this.contractDescribeInfo,
                signDate: this.signDate,
            });
        },
        contractNameChange() {
            this.setContractDescribeInfo({
                ...this.contractDescribeInfo,
                contractName: this.contractName,
            });
        },
        signDateChange(e) {
            this.signDate = e.target.value;
            this.setContractDescribeInfo({
                ...this.contractDescribeInfo,
                signDate: this.signDate,
            });
        },
        contractDateChange(e) {
            this.contractDate = e.target.value;
            this.setContractDescribeInfo({
                ...this.contractDescribeInfo,
                contractDate: this.contractDate,
            });
        },
    },
    async onReady() {
        this.getContractTypes();
        this.initSignDate();
    },
};
</script>

<style lang="scss">
.describe-info {
    &__necessary {
        color: red;
        margin-right: 6rpx;
    }
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
    &__title {
        height: 90rpx;
        line-height: 90rpx;
        color: $--color-text-secondary;
        padding: 0 30rpx;
    }
    &__content {
        background-color: $--color-white;
    }
    &__item {
        padding: 0 30rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 100rpx;
        line-height: 100rpx;
        border-top: 1rpx solid $--border-color-lighter;
    }
    &__name {
        border:none;
    }
    &__input {
        height: 100rpx;
        line-height: 100rpx;
        text-align: right;
    }
    &__input_placeholder{
        color: $--color-text-placeholder;
    }

}
</style>
