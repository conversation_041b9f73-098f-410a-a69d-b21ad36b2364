<template>
    <view class="upload-file-list">
        <view
            :key="index"
            v-for="(item, index) in fileList"
           
            class="upload-file-list__item"
        >
            <view v-if="item.fileId" class="upload-file-list__name" :class="{'disabled':!item.canPreview}"  @click="handleViewFile(item)">{{ item.fileName|| imgShowName(item.previewUrl) }} </view>
            <view class="upload-file-list__delete" @click.stop="removeFileList(index)" v-if="!disabled">
                <text class="icon-shanchu"></text>
            </view>
        </view>
        <view class="upload-file-list__add" @click="add" v-if="!disabled">+ {{btnText}}</view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    data(){
        return {
            accessToken: uni.getStorageSync('accessToken'),
            host: this.$http.baseURL,
        }
    },
    props:{
        fileList:{
            type:Array,
            default:[],
        },
        disabled:{
            type:Boolean,
            default: false,
        },
        btnText:{
            type: String,
            default: '',
        }
    },
    computed: {
        ...mapState(['isWeWork']),
    },
    methods: {
        imgShowName(path = '') {
            return path.split('//')[1];
        },
        handleViewFile(file) {
            if (this.isWeWork || !file.canPreview) {
                return;
            }
            const imgList =  (file.previewUrls || []).map(item => `${this.host}${item}?access_token=${this.accessToken}`);
            uni.previewImage({
                urls: imgList,
            });
        },
        removeFileList(index){
            this.$emit('remove',index);
        },
        add(){
            if (!this.disabled){
                this.$emit('add');
            }
        }
    },
};
</script>

<style lang="scss">
.upload-file-list {
    background-color: $--color-white;
    padding: 10rpx 30rpx 30rpx;
    font-size: 24rpx;
    &::after{
        clear: both;
        display: block;
        content:'';
    }
    &__item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        color: $--color-primary;
        width: 325rpx;
        border-radius: 5px;
        background: $--background-color-regular;
        float: left;
        margin-right: 20rpx;
        line-height: 60rpx;
        margin-bottom: 20rpx;
        border: 1rpx solid $--color-info-light;
    }
    &__name {
        margin: 0 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &__delete {
        .icon-shanchu {
            color: $--color-text-secondary;
            font-size: 24rpx;
            padding-right: 20rpx;
        }

    }
    &__add{
        width: 325rpx;
        border-radius: 5px;
        float: left;
        margin-right: 20rpx;
        background: #F1F9FF;
        text-align: center;
        line-height: 60rpx;
        margin-bottom: 20rpx;
        color: $--color-primary-light-1;
        border: 1rpx dashed $--color-primary-light-5;
    }
}
</style>
