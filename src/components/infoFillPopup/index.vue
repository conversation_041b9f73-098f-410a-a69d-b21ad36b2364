<template>
    <CustomBottomPopup ref="customPopup" @popLevelChange="handlePopLevelChange">
        <view class="info-fill-popup" slot="content">
            <view class="info-fill-popup__header">
                <view class="step-item" :class="{'active': activeStep === 'labelFill'}">
                    <view class="step-item__text">① 填写文件内容</view>
                </view>
                <view class="step-item mid-icon">······</view>
                <view class="step-item" :class="{'active': activeStep === 'receiverFill'}">
                    <view class="step-item__text">② 填写签署方信息并发送</view>
                </view>
            </view>
            <view class="info-fill-popup__body" :style="{'overflow': isMultiplePopShow ? 'visible' : 'auto'}">
                <!--            <view class="info-fill-popup__body-input-tip">注意："<span>*</span>"标记项为必填项</view>-->
                <view style="height: 100%" v-show="activeStep === 'labelFill'">
                    <scroll-view
                        @scroll="scroll"
                        :scroll-top="scrollTop"
                        style="height: 100%"
                        scroll-y="true"
                        class="label-fill-content"
                    >
                        <RowInfoInput
                            v-for="(item, index) in labels"
                            :key="item.labelId"
                            :name="item.labelName"
                            :required="item.labelExtends.required"
                            :options="item.labelExtends.options"
                            :type="item.labelType"
                            :input-type="getInputType(item)"
                            :error="errorInfo[item.labelId]"
                            :suffix="item.labelExtends.unit"
                            :disabled="item.disabled"
                            :defaultValue="item.defaultValue"
                            @labelClick="handleLabelClick(item, index)"
                            @valueChange="handleInputValueChange($event, item)"
                            @innerPopupVisibleChange="handlePopupVisibleChange"
                        >
                        </RowInfoInput>
                    </scroll-view>
                </view>
                <view v-show="activeStep === 'receiverFill'" class="receiver-fill-content">
                    <view class="receiver-item" v-for="receiver in receivers" :key="receiver.roleId">
                        <view class="receiver-item__header">
                            <view class="receiver-item__header-icon">{{ receiver.roleName[0] }}</view>
                            <view class="receiver-item__header-name">{{ receiver.roleName }}信息</view>
                        </view>
                        <template v-if="receiver.userType === 'PERSON'">
                            <RowInfoInput
                                name="姓名"
                                :required="receiver.signConfig && !receiver.signConfig.optionalUserName"
                                type="TEXT"
                                :error="errorInfo[`${receiver.roleId}_userName`]"
                                @valueChange="handleReceiverChange($event, receiver, 'userName')"
                            >
                            </RowInfoInput>
                            <RowInfoInput
                                v-if="receiver.signConfig && receiver.signConfig.requiredIdNumber"
                                name="身份证号"
                                :required="receiver.signConfig && !receiver.signConfig.optionalIDNumber"
                                type="TEXT"
                                input-type="idcard"
                                placeholder="请输入身份证号"
                                :error="errorInfo[`${receiver.roleId}_idNumber`]"
                                @valueChange="handleReceiverIdNumberChange($event, receiver)"
                            >
                            </RowInfoInput>
                            <RowInfoInput
                                name="手机号"
                                :required="true"
                                type="NUMERIC_VALUE"
                                input-type="number"
                                placeholder="请输入手机号"
                                :error="errorInfo[`${receiver.roleId}_userAccount`]"
                                @valueChange="handleReceiverChange($event, receiver, 'userAccount')"
                            >
                            </RowInfoInput>
                            <!-- <view class="label-fill-item" v-if="signConfigItemList(receiver.signConfig)">
                                <view class="label-fill-item__name">
                                    <span class="label-fill-item__name-required">*</span>
                                    签署要求</view>
                                <view class="label-fill-item__filler">
                                    {{ signConfigItemList(receiver.signConfig) }}
                                </view>
                            </view> -->
                        </template>
                        <template v-else>
                            <RowInfoInput
                                :defaultValue="receiver.enterpriseName"
                                name="企业名称"
                                :required="true"
                                type="TEXT"
                                :error="errorInfo[`${receiver.roleId}_enterpriseName`]"
                                @valueChange="handleReceiverChange($event, receiver, 'enterpriseName')"
                            >
                            </RowInfoInput>
                            <RowInfoInput
                                :defaultValue="receiver.userAccount"
                                name="经办人手机号"
                                :required="true"
                                type="NUMERIC_VALUE"
                                input-type="number"
                                :error="errorInfo[`${receiver.roleId}_userAccount`]"
                                @valueChange="handleReceiverChange($event, receiver, 'userAccount')"
                            >
                            </RowInfoInput>
                        </template>
                    </view>
                    <view class="receiver-fill-content__sender-tip">小提醒：当前发件方为{{ userInfo }}</view>
                </view>
            </view>
            <view class="info-fill-popup__footer">
                <button v-if="activeStep === 'receiverFill'"
                    type="primary"
                    class="back-btn opt-btn"
                    @click="handleBackToFillLabel"
                >
                    上一步
                </button>
                <button class="opt-btn" type="primary" @click="handleLabelFillNext">
                    {{ activeStep === 'labelFill' ? '下一步' : '发送' }}
                </button>
            </view>
        </view>
    </CustomBottomPopup>
</template>
<script>
import { mapState } from 'vuex';
import { getSampleLabelValidateResult } from 'utils/sample';
import RowInfoInput from 'components/inputComp/rowInfoInput.vue';
import CustomBottomPopup from 'components/customBottomPopup/index.vue';

export default {
    name: 'InfoFillPopup',
    components: { CustomBottomPopup, RowInfoInput },
    props: {
        labels: {
            type: Array,
            default: () => ([]),
        },
        receivers: {
            type: Array,
            default: () => ([]),
        },
        sampleId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            activeStep: 'labelFill',
            errorInfo: {},
            popupLevel: 1,
            date: new Date(),
            isMultiplePopShow: false,
            scrollTop: 0,
            old: {
                scrollTop: 0,
            },
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
        signConfigItemList() { // 根据配置展示对应的文案
            return (signConfig) => {
                const configTexts = [];
                if (signConfig.forceHandWrite) {
                    configTexts.push('手写签名');
                }
                if (signConfig.requireIdentityAssurance) {
                    configTexts.push('实名');
                }
                return configTexts.join(',');
            };
        },
        userInfo() {
            const { currentEntId, enterprises, platformUser } = this.commonHeaderInfo;
            if (currentEntId === '0') {
                return `${platformUser.fullName}（${platformUser.account}）`;
            }
            const currentEnt = enterprises.find(a => a.entId === currentEntId);
            return `${currentEnt.entName}（${platformUser.account}）`;
        },
    },
    methods: {
        getInputType(item) {
            if (item.labelName.includes('身份证号')) {
                return 'idcard';
            }
            if (item.labelType === 'NUMERIC_VALUE') {
                return 'digit';
            }
            return 'text';
        },
        handlePopupVisibleChange(visible) { // 多选弹出时，ios手机上pop布局会有问题，修正布局
            this.isMultiplePopShow = visible;
            // this.$emit('multiPopShow', {
            //     visible,
            //     markI,
            // });
        },
        handleLabelClick(item, index) {
            this.scrollTop = this.old.scrollTop;
            this.$nextTick(() => {
                this.scrollTop = (index - 1) * 45;
            });
            this.$emit('labelClick', item.markI);
        },
        scroll(e) {
            this.old.scrollTop = e.detail.scrollTop;
        },
        handleBackToFillLabel() {
            this.activeStep = 'labelFill';
            this.date = new Date();
        },
        async handleLabelFillNext() {
            // 第一步：校验输入字段
            if (this.activeStep === 'labelFill') {
                this.$point('click_sample_fill_fields_page_next', {
                    biz_id: this.sampleId,
                });
                this.sendPoint(true);
                await this.labelFillValidate();
                if (Object.values(this.errorInfo).some(a => a)) {
                    this.$toast.error('请输入正确的文件内容信息');
                    this.$forceUpdate();
                    return;
                }
                this.activeStep = 'receiverFill';
            } else {
                this.$point('click_sample_send', {
                    biz_id: this.sampleId,
                });
                // 第二步：校验输入签署人信息
                await this.receiverFillValidate();
                if (Object.values(this.errorInfo).some(a => a)) {
                    this.$toast.error('签署人信息有误，请修正后发送');
                    return;
                }
                this.$emit('sendSample', this.receivers);
            }
        },
        receiverFillValidate() {
            return new Promise((resolve) => {
                this.receivers.forEach(receiver => {
                    const { userName, userAccount, userType, enterpriseName, signConfig } = receiver;
                    if (userType === 'PERSON') {
                        const { isFailed: nameError, errorMessage: nameErrorMsg } = getSampleLabelValidateResult({
                            name: '姓名',
                            required: !signConfig.optionalUserName,
                            value: userName,
                        });
                        if (nameError) {
                            this.addErrorInfo(`${receiver.roleId}_userName`, nameErrorMsg);
                        }

                        const { isFailed: idNumberError, errorMessage: idNumberErrorMsg } =
                            getSampleLabelValidateResult({
                                name: '身份证号',
                                required: !signConfig.optionalIDNumber,
                                value: signConfig.idNumber,
                            });
                        if (idNumberError) {
                            this.addErrorInfo(`${receiver.roleId}_idNumber`, idNumberErrorMsg);
                        }

                        const { isFailed: accountError, errorMessage: accountErrorMsg } =
                            getSampleLabelValidateResult({
                                name: '手机号',
                                required: true,
                                value: userAccount,
                            });
                        if (accountError) {
                            this.addErrorInfo(`${receiver.roleId}_userAccount`, accountErrorMsg);
                        }
                    } else {
                        const { isFailed: entNameError, errorMessage: entNameErrorMsg } =
                            getSampleLabelValidateResult({
                                name: '企业名称',
                                required: true,
                                value: enterpriseName,
                            });
                        if (entNameError) {
                            this.addErrorInfo(`${receiver.roleId}_enterpriseName`, entNameErrorMsg);
                        }
                        const { isFailed: accountError, errorMessage: accountErrorMsg } =
                            getSampleLabelValidateResult({
                                name: '手机号',
                                required: true,
                                value: userAccount,
                            });
                        if (accountError) {
                            this.addErrorInfo(`${receiver.roleId}_userAccount`, accountErrorMsg);
                        }
                    }
                });
                resolve();
            });
        },
        addErrorInfo(key, message) {
            this.$set(this.errorInfo, key, message);
        },
        labelFillValidate() {
            return new Promise((resolve) => {
                this.labels.forEach(label => {
                    const result = getSampleLabelValidateResult({
                        labels: this.labels,
                        name: label.labelName,
                        value: label.labelType === 'MULTIPLE_BOX' && !label.value?.length ? '' : label.value,
                        // 多选未选时，空值为[]
                        required: label.labelExtends.required,
                    });
                    if (result.isFailed) {
                        this.addErrorInfo(label.labelId, result.errorMessage);
                    } else {
                        this.$set(this.errorInfo, label.labelId, '');
                        this.addErrorInfo(label.labelId, '');
                    }
                });
                resolve();
            });
        },
        handleReceiverIdNumberChange(value, receiver) {
            this.addErrorInfo(`${receiver.roleId}_idNumber`, '');
            receiver.signConfig.idNumber = value;
        },
        handleReceiverChange(value, receiver, key) {
            this.addErrorInfo(`${receiver.roleId}_${key}`, '');
            receiver[key] = value;
        },
        handleInputValueChange(value, label) {
            if (label.labelName === '还款方式') {
                const numberLabel = this.labels.find((item) => item.labelName === '还款期数');
                const defaultValue = value === '一次性' ? 1 : '';
                numberLabel.defaultValue = defaultValue;
                numberLabel.value = defaultValue;
                numberLabel.disabled = value === '一次性';
                this.updateLabelValue(numberLabel);
            }
            this.addErrorInfo(label.labelId, '');
            label.value = value;
            this.updateLabelValue(label);
        },
        updateLabelValue(label) {
            this.$emit('updateLabelValue', {
                label,
            });
        },
        handlePopLevelChange() {
            this.sendPoint();
        },
        sendPoint(isConfirm = false) {
            if (this.activeStep === 'receiverFill') {
                return;
            }
            const eventHalf = 'enter_sample_fill_fields_page_half';
            const eventAll = 'enter_sample_fill_fields_page_all';
            if (isConfirm) {
                const newDate = new Date();
                const time = newDate - this.date;
                this.date = newDate;
                this.$point(this.popupLevel === 2 ? eventAll : eventHalf, {
                    biz_id: this.sampleId,
                    wxdata_staytime: time,
                });
            } else {
                const { popupLevel } = this.$refs.customPopup;
                if (popupLevel === 0 || popupLevel === 1 && this.popupLevel === 0) {
                    this.popupLevel = popupLevel;
                    return;
                }
                const newDate = new Date();
                const time = newDate - this.date;
                this.date = newDate;
                if (popupLevel === 2) {
                    this.$point(eventHalf, {
                        biz_id: this.sampleId,
                        wxdata_staytime: time,
                    });
                } else {
                    this.$point(eventAll, {
                        biz_id: this.sampleId,
                        wxdata_staytime: time,
                    });
                }
                this.popupLevel = popupLevel;
            }
        },
    },
};
</script>
<style lang="scss">
.info-fill-popup {
    height: 100%;
    &__header {
        width: 100%;
        padding: 10rpx 0;
        display: flex;
        flex-direction: row;
        justify-content: center;
        background-color: $--background-color-base;
        .step-item {
            display: flex;
            height: 60rpx;
            line-height: 60rpx;
            padding: 0 10rpx;
            font-size: 12px;
            color: $--color-text-placeholder;
            &.active {
                color: $--color-primary;
            }
            &.mid-icon {
                color: $--color-primary;
                font-size: 8px;
            }
        }
    }
    &__body {
        height: calc(100% - 270rpx);
        overflow: auto;
        .label-fill-item {
            display: flex;
            border-bottom: 1px solid $--border-color-lighter;
            height: 80rpx;
            line-height: 80rpx;
            padding: 0 20rpx;
            box-sizing: border-box;
            position: relative;
            &__name {
                width: fix-content;
                color: $--color-text-primary;
            }
            &__filler {
                width: auto;
                text-align: right;
                flex-grow: 1;
                padding: 0 20rpx;
                color: $--color-text-primary;
            }
        }
        .receiver-fill-content {
            .receiver-item {
                &__header {
                    height: 80rpx;
                    line-height: 80rpx;
                    display: flex;
                    background-color: $--color-primary-light-9;
                    &-icon {
                        background-color: $--color-primary;
                        color: $--color-white;
                        margin: 0 20rpx;
                        width: 60rpx;
                        height: 60rpx;
                        line-height: 60rpx;
                        border-radius: 4rpx;
                        align-self: center;
                        text-align: center;
                    }
                    &-name {
                        color: $--color-primary;
                    }
                }
            }
            &__sender-tip {
                text-align: center;
                padding-top: 30rpx;
                font-size: 12px;
                color: $--color-text-secondary;
            }
        }
        &-input-tip {
            font-size: 12px;
            background-color: $--background-color-secondary;
            text-align: center;
            color: $--color-text-secondary;
            height: 20rpx;
            line-height: 20rpx;
            span {
                color: $--color-danger;
            }
        }
    }
    &__footer {
        height: 120rpx;
        display: flex;
        padding: 0 20rpx;
        .back-btn {
            width: 30%;
            margin-right: 20rpx;
        }
    }
}
</style>
