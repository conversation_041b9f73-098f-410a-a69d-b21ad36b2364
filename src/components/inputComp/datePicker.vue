<template>
    <picker
        mode="date"
        :value="selectDate"
        :start="startDate"
        :end="endDate"
        @change="selectedDateChange"
    >
        <view class="custom-date-picker">
            <view v-if="selectDate" class="value">{{ selectDate }}</view>
            <view v-else class="placeholder">{{ placeholder }}</view>
        </view>
    </picker>
</template>
<script>
export default {
    props: {
        startDate: {
            type: String,
            default: '',
        },
        endDate: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '请输入',
        },
    },
    data() {
        return {
            selectDate: '',
        };
    },
    methods: {
        selectedDateChange(e) {
            this.selectDate = e.target.value;
            this.$emit('change', e.target.value);
        },
    },
};
</script>
<style lang="scss">
.custom-date-picker .placeholder {
    color: $--color-text-placeholder;
    font-size: 28rpx;
}
.custom-date-picker .value {
    font-size: 16px;
}
</style>
