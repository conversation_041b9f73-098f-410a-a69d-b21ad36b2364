<template>
    <view class="label-fill-item"
        @click="$emit('labelClick')"
    >
        <view class="label-fill-item__name">
            <span class="label-fill-item__name-required" v-if="required">*</span>
            {{ name }}
        </view>
        <view class="label-fill-item__filler">
            <input v-if="type === 'TEXT'"
                class="input-item"
                :type="inputType"
                :inputBorder="false"
                :clearable="false"
                placeholder-style="font-size:28rpx;color:#ccc"
                trim="both"
                v-model="currentValue"
                :placeholder="placeholder"
                @input="handleInputValueChange"
                :cursor-spacing="80"
            />
            <input v-else-if="type === 'NUMERIC_VALUE'"
                class="input-item"
                :inputBorder="false"
                :clearable="false"
                placeholder-style="font-size:28rpx;color:#ccc"
                trim="both"
                :type="inputType"
                @input="handleInputValueChange"
                v-model="currentValue"
                :placeholder="placeholder"
                :disabled="disabled"
                :cursor-spacing="80"
            />
            <DatePicker v-else-if="type === 'BIZ_DATE'"
                :placeholder="placeholder"
                @change="valueChange"
            ></DatePicker>

            <SelectorPicker v-else-if="type === 'SINGLE_BOX'"
                :options="options"
                :placeholder="placeholder"
                @change="handleSelectorPickerChange"
            >
            </SelectorPicker>
            <SingleOrMulSelect
                v-else-if="type === 'MULTIPLE_BOX'"
                :selectedValue="currentValue"
                :selectOptions="options"
                selectType="multiple"
                :placeholder="placeholder"
                @change="handleMultiSelectChange"
                @popVisibleChange="handlePopupVisibleChange"
            ></SingleOrMulSelect>
        </view>
        <view v-if="suffix" class="label-fill-item__suffix">{{ suffix }}</view>
        <view class="label-fill-item__error" v-show="error">{{ error }}</view>
    </view>
</template>
<script>
import DatePicker from 'components/inputComp/datePicker.vue';
import SelectorPicker from 'components/inputComp/selectorPicker.vue';
import SingleOrMulSelect from 'components/templateDocForm/singleOrMulSelect/index.vue';

export default {
    components: { SingleOrMulSelect, SelectorPicker, DatePicker },
    props: {
        name: {
            type: String,
            default: '',
        },
        required: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: '',
        },
        suffix: {
            type: String,
            default: '',
        },
        inputType: {
            type: String,
            default: 'text',
        },
        placeholder: {
            type: String,
            default: '请输入',
        },
        options: {
            type: Array,
            default: () => ([]),
        },
        error: {
            type: String,
            default: '',
        },
        defaultValue: {
            type: String,
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            currentValue: this.defaultValue,
        };
    },
    watch: {
        defaultValue(v) {
            this.currentValue = v;
        },
    },
    methods: {
        valueChange(value) {
            this.$emit('valueChange', value);
        },
        handlePopupVisibleChange(visible) {
            this.$emit('innerPopupVisibleChange', visible);
        },
        handleInputValueChange(val) {
            let newVal = val.target.value;
            // 非身份证号数值要先过滤''和 无值场景
            if (!this.name.includes('身份证') && this.type === 'NUMERIC_VALUE' && val !== '' && val) {
                newVal = Number(Number(newVal).toFixed(3));
                this.currentValue = newVal;
            }
            this.valueChange(newVal);
        },
        handleMultiSelectChange(event) {
            this.currentValue = event.detail.value;
            this.valueChange(event.detail.value.join(','));
        },
        handleSelectorPickerChange(valIndex) {
            this.valueChange(this.options[valIndex]);
        },
    },
};
</script>
<style lang="scss">
.label-fill-item {
    display: flex;
    border-bottom: 1px solid $--border-color-lighter;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    position: relative;
    &__name {
        width: fix-content;
        min-width: 220rpx;
        color: $--color-text-primary;
        &-required {
            color: $--color-danger;
            padding-right: 5rpx;
        }
    }
    &__filler {
        width: auto;
        text-align: right;
        flex-grow: 1;
        padding: 0 20rpx;
        color: $--color-text-primary;
        & > view > view {
            background-color: transparent !important;
        }
        input {
            padding-right: 0 !important;
            height: 80rpx; // b避免高度覆盖父级边线
            line-height: 80rpx;
            box-sizing: border-box;
            text-align: right;
            font-size: 16px;
        }
        &.is-empty {
            color: $--color-text-placeholder;
        }
    }
    &__error{
        position: absolute;
        bottom: 2rpx;
        right: 40rpx;
        color: $--color-danger;
        font-size: 10px;
        line-height: 12px;
    }
}
</style>
