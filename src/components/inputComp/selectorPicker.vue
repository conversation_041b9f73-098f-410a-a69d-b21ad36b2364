<template>
    <picker
        :value="selectedIndex"
        :range="options"
        @change="handleChange($event)"
        class="custom-selector-picker"
        :mode="isMultiple? 'multiSelector' : 'selector'"
    >
        <span v-if="options[selectedIndex]" class="value">{{ options[selectedIndex] }}</span>
        <span v-else class="placeholder">{{ placeholder }}</span>
    </picker>
</template>
<script>
export default {
    props: {
        options: {
            type: Array,
            default: () => ([]),
        },
        isMultiple: {
            type: Boolean,
            default: false,
        },
        placeholder: {
            type: String,
            default: '请输入',
        },
    },
    data() {
        return {
            selectedIndex: '',
        };
    },
    methods: {
        handleChange(e) {
            this.selectedIndex = e.detail.value;
            this.$emit('change', e.detail.value);
        },
    },
};
</script>
<style lang="scss">
.custom-selector-picker .placeholder {
    color: $--color-text-placeholder;
}
.custom-selector-picker .value {
    font-size: 16px;
}
</style>
