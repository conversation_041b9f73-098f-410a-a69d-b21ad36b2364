<template>
    <CustomBottomPopup ref="customPopup" @popLevelChange="handlePopLevelChange">
        <view class="knew-detail-popup" slot="content">
            <view class="knew-detail-popup__title">
                知情同意书
            </view>
            <view class="knew-detail-popup__text">
                <view class="text-row">
                    在您同意接受本协议并使用杭州尚尚签网络科技有限公司（以下简称本平台）提供的合同范本文件时，请确保已充分阅读并知晓以下内容：</view>
                <view class="text-row">1、您已具备相应的民事行为能力，或者您是在中国大陆地区依法设立并合法开展经营活动或其他业务的法人或其他组织；您基于真实、合法的目的使用该文件，且内容不受您所属国家或地区法律的排斥；</view>
                <view class="text-row">2、您在使用本服务时请自行判断对方是否是完全民事行为能力人，以及是否具有履约能力，请您自行确定对方提供的信息与其本人对应，并决定是否与对方进行签约交易等。您知悉并同意对于非中华人民共和国大陆的公民、或者非在中华人民共和国大陆注册登记的法人或者其他组织在本平台签订法律文件可能判决无效；</view>
                <view
                    class="text-row"
                >3、您认可使用本服务是基于您个人真实意愿，认可您使用本服务签订的电子文件和纸质文件具备相同的法律效力，认可您使用本服务签订的电子签名、电子印章和您的手写签名、实体印章具备相同的效力。</view>
                <view
                    class="text-row risk-tip"
                >风险提示：您在使用本服务时，请仔细阅读文档内容，必要时请咨询专业人士。本服务所示文件不保证是全部适用的，仅供您参考使用。实际情况与模板内容不一致的，您可自由选择是否使用本服务。使用本服务签订相关文件产生的纠纷及风险，与平台无关，您需要承担相关责任，平台作为为双方签署文件提供中立性技术支持。
                </view>
                <view class="text-row">点击【同意并继续】表示认可以上内容，再次浏览或使用时不再重复告知。</view>
            </view>
            <view class="knew-detail-popup__footer">
                <button type="primary" @click="handleKnewDetailConfirm">同意并继续</button>
            </view>
        </view>
    </CustomBottomPopup>
</template>
<script>

import CustomBottomPopup from 'components/customBottomPopup/index.vue';

export default {
    name: 'KnewDetailPopup',
    components: { CustomBottomPopup },
    props: {

        sampleId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            date: new Date(),
            popupLevel: 1,
        };
    },
    methods: {
        handleKnewDetailConfirm() {
            this.sendPoint(true);
            this.$point('click_sample_informed_consent', {
                biz_id: this.sampleId,
            });
            this.$emit('knewDetailConfirm');
        },
        handlePopLevelChange() {
            this.sendPoint();
        },
        sendPoint(isConfirm = false) {
            const eventHalf = 'enter_sample_authorization_half';
            const eventAll = 'enter_sample_authorization_all';
            if (isConfirm) {
                const newDate = new Date();
                const time = newDate - this.date;
                this.date = newDate;
                this.$point(this.popupLevel === 2 ? eventAll : eventHalf, {
                    biz_id: this.sampleId,
                    wxdata_staytime: time,
                });
            } else {
                const { popupLevel } = this.$refs.customPopup;
                if (popupLevel === 0 || popupLevel === 1 && this.popupLevel === 0) {
                    this.popupLevel = popupLevel;
                    return;
                }
                const newDate = new Date();
                const time = newDate - this.date;
                this.date = newDate;
                if (popupLevel === 2) {
                    this.$point(eventHalf, {
                        biz_id: this.sampleId,
                        wxdata_staytime: time,
                    });
                } else {
                    this.$point(eventAll, {
                        biz_id: this.sampleId,
                        wxdata_staytime: time,
                    });
                }
                this.popupLevel = popupLevel;
            }
        },
    },
};
</script>
<style lang="scss">
.knew-detail-popup {
    height: 100%;
    &__title {
        padding: 30rpx 0;
        height: 50rpx;
        line-height: 50rpx;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        color: $--color-text-primary;
    }
    &__text {
        height: calc(100% - 270rpx);
        overflow: auto;
        padding: 0 30rpx;
        line-height: 50rpx;
        text-indent: 60rpx;
        box-sizing: border-box;
        color: $--color-text-primary;
        .risk-tip {
			font-weight: bold;
        }
    }
    &__footer {
        height: 120rpx;
    }
}
</style>
