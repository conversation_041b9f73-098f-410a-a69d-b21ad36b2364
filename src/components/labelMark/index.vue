<template>
    <view class="label"
        :style="{'left': `${newMark.x}px`, top: `${newMark.y}px`, 'minWidth': `${newMark.width}px`, 'minHeight': `${newMark.height}px`}"
        :class="{'label_date': labelType === 'DATE'}"
    >
        <view
            v-if="labelType !== 'QR_CODE'"
            @touchstart.stop.prevent="dragStart"
            class="label"
        >
            <!-- 印章、签名上方签署人 -->
            <view
                class="label-title"
                v-if="labelType === 'SIGNATURE' || labelType === 'SEAL'"
                :style="{'width': `${newMark.width + 2}px`}"
            >
                {{ currentReceiver.showName || currentReceiver.enterpriseName || currentReceiver.userName ||currentReceiver.userAccount }}
            </view>
            <!-- 盖章/签名 -->
            <view
                v-if="labelType === 'SIGNATURE' || labelType === 'SEAL'"
                :style="{'background': bgColor, 'width': newMark.width + 'px', 'height': newMark.height + 'px', 'opacity': 0.8}"
                class="label-sign p-center"
                :class="{'label-active': activeLabelId === curLabelId}"
            >
                <!-- 企业盖章背景圆圈 偏右-->
                <view v-if="labelType === 'SEAL'" :style="{transform: 'translateX('+ newMark.height *0.11 +'px)'}">
                    <view
                        class="p-center"
                        :style="{'width': newMark.height *0.8 + 'px', 'height': newMark.height *0.8+ 'px', 'background': '#ffffff', 'opacity': 0.3, 'border-radius': newMark.width *0.8 + 'px'}"
                    >
                    </view>
                    <image
                        :src="sealImg"
                        alt=""
                        :style="{'width': newMark.height *0.6 + 'px', 'height': newMark.height *0.6+ 'px', 'position': 'absolute'}"
                        style="left: 50%; top: 50%; transform: translate(-50%, -50%)"
                    ></image>
                </view>
                <!-- 个人签名 -->
                <image
                    v-else-if="labelType === 'SIGNATURE'"
                    :src="signImg"
                    alt=""
                    :style="{'width': newMark.width*0.7 + 'px', 'height': newMark.height *0.7+ 'px'}"
                ></image>
            </view>
            <!-- 签署日期 -->
            <view
                v-else-if="labelType === 'DATE'"
                :style="{'background': bgColor, 'width': `${newMark.width}px`, 'height': `${newMark.height}px`,
                         'line-height': `${newMark.height}px`, 'opacity': 0.8}"
                class="label-date"
                :class="{'label-active': activeLabelId === curLabelId}"
            >
                签署日期
            </view>
            <!-- 其他业务字段 -->
            <view
                class="label-text"
                v-else
                :style="{'background': bgColor, 'width': `${newMark.width}px`, 'height': `${newMark.height}px`,
                         'line-height': `${newMark.height}px`, 'opacity': 0.8}"
            >
                <!-- 本地发送 -->
                <text v-if="sendType === 'localSend'">
                    <text class="label-font-16" v-if="!mark.value">{{ mark.name }}</text>
                    <template v-else>
                        <view class="label-font-16" v-for="key in mark.value" :key="key" :style="{'width': `${newMark.width}px`, 'height': `${newMark.height}px`, 'line-height': `${newMark.height}px`}">{{ key }}</view>
                    </template>
                </text>

                <!-- 模板发送 -->
                <view v-else :style="{'width': `${newMark.width}px`, 'height': `${newMark.height}px`, position: 'relative'}">
                    <!-- 单复选 -->
                    <template v-if="labelType ==='SINGLE_BOX' || labelType ==='MULTIPLE_BOX' ">
                        <view v-for="(item, index) in mark.labelExtends.items" :key="index">
                            <image
                                :src="checkLabelImg"
                                :style="{position: 'absolute', width: `${newMark.width}px`, height: `${newMark.height / mark.labelExtends.items.length}px`, left:0, top:`${item.itemY * pageHeight}px`}"
                            />
                            <image
                                v-if="ifShowLabelImg(mark, item)"
                                :src="selectImg"
                                :style="{position: 'absolute', width: `${newMark.width}px`, height: `${newMark.height / mark.labelExtends.items.length}px`, left:0, top:`${item.itemY * pageHeight}px`}"
                            />
                        </view>
                    </template>
                    <!--图片字段-->
                    <template v-if="labelType ==='PICTURE'">
                        <image
                            v-if="mark.valueStr"
                            class="label-fill"
                            :mode="ifFillAllRegion ? 'scaleToFill' : 'aspectFit'"
                            :src="`${host}${mark.valueStr}?access_token=${accessToken}`"
                        />
                        <view v-else>{{ mark.labelName }}</view>
                    </template>
                    <!-- 其他类型字段 -->
                    <template v-else>
                        <text v-if="mark.valueStr" class="label-font-16">{{ mark.valueStr }}</text>
                        <template v-else-if="mark.valueList.length">
                            <view class="label-font-16" v-for="key in mark.valueList" :key="key" :style="{'width': `${newMark.width}px`, 'height': `${newMark.height}px`, 'line-height': `${newMark.height}px`}">{{ key }}</view>
                        </template>
                        <view v-else class="label-font-16">{{ mark.labelName }}</view>
                    </template>
                </view>

            </view>
        </view>
        <!-- 删除按钮 -->
        <image
            v-if="ifCanDelete"
            class="label-delete"
            :style="{'left': `${(newMark.width-25)/2}px`}"
            @click="deleteLabel"
            :src="iconDelImg"
        ></image>
    </view>
</template>

<script>
import { colorInfo } from '@/utils/contract';
import iconDel from '@/assets/images/icon_delete.png';
import signImg from '@/assets/images/label_sign.png';
import sealImg from '@/assets/images/label_seal.png';
import checkboxImg from '@/assets/images/label_checkbox.png';
import radioImg from '@/assets/images/label_radio.png';
import selectImg from '@/assets/images/label_select.png';
import { mapState } from 'vuex';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'receivers', 'pageHeight', 'activeLabelId'],
    data() {
        return {
            accessToken: uni.getStorageSync('accessToken'),
            host: this.$http.baseURL,
            iconDelImg: iconDel,
            signImg: signImg,
            sealImg: sealImg,
            selectImg: selectImg,
            labelButtonStyle: { // 单复选button默认样式
                width: 26,
                height: 26,
                split: 5,
                initSplit: 10,
            },
        };
    },
    computed: {
        ...mapState('send', ['sendType']),
        ...mapState('template', ['templatePermissions']),
        ifShowLabelImg() {
            return (mark, item) => {
                return mark.valueStr && mark.valueStr.split(',').includes(item.itemValue);
            };
        },
        checkLabelImg() {
            return this.labelType === 'MULTIPLE_BOX' ? checkboxImg : radioImg;
        },
        newMark() {
            return this.sendType === 'localSend' ? this.mark : this.mark.labelPosition;
        },
        labelType() {
            return  this.sendType === 'localSend' ? this.mark.type : this.mark?.labelType;
        },
        ifCanDelete() {
            const isLocalSend = this.sendType === 'localSend';
            const isTemplateSend = this.sendType === 'templateSend' && (['SEAL', 'SIGNATURE', 'DATE'].includes(this.labelType)) && this.templatePermissions.modifySignLabel;
            return this.labelType && this.labelType !== 'QR_CODE' && (isLocalSend || isTemplateSend);
        },
        queryName() {
            // return this.sendType === 'localSend' ? 'receiverId' : 'roleId';
            return 'receiverId';
        },
        currentReceiver() {
            const { queryName } = this;
            return this.receivers.find(item => item[queryName] === this.mark[queryName]);
        },
        bgColor() {
            if (this.labelType === 'QR_CODE') {
                return '#ffffff';
            }
            const { queryName } = this;
            const index = this.receivers.findIndex(item => item[queryName] === this.mark[queryName]);
            return colorInfo[index % 9] || 'transparent';
        },
        isEnt() {
            return this.currentReceiver && this.currentReceiver.userType === 'ENTERPRISE';
        },
        QRCodeUrl() {
            const { host, mark, accessToken } = this;
            return `${host}${mark.value || mark.imageHref}?access_token=${accessToken}`;
        },
        curLabelId() {
            return this.mark?.labelId || '';
        },
        ifFillAllRegion() {
            return this.mark?.labelExtends?.autoAdjust || false;
        },
    },
    methods: {
        // 删除标签
        async deleteLabel() {
            this.$emit('updateMark', 'delete', this.mark);
        },
        dragStart(e) {
            if (this.sendType === 'templateSend' && !this.templatePermissions.dragSignLabel) {
                return this.$toast.error('暂无移动签署字段权限');
            }
            if (['SEAL', 'SIGNATURE', 'DATE'].includes(this.labelType)) {
                this.$emit('dragStart', this.labelType, e, this.mark.markI);
            }
        },
    },
    // onLoad() {
    //     this.host = this.$http.baseURL;
    // },
};
</script>

<style lang="scss">
.p-center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.label {
    position: absolute;
    &-title {
        background: #fff7b6;
        color: #333;
        font-size: 16rpx;
        line-height: 40rpx;
        height: 40rpx;
        position: absolute;
        top: -45rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &-date {
        font-size: 24rpx;
        text-align: center;
    }
    &-active {
        border: 1px dashed #127fd2;
    }
    &-delete {
        position: absolute;
        top: -120rpx;
        width: 50rpx;
        height: 69rpx;
    }
    &-text {
        font-size: 24rpx;
    }
    &-font-16 {
        font-size: 16rpx;
    }
    &-fill {
        width: 100%;
        height: 100%;
    }
}
.label.label_date {
    .label-delete {
        top: -80rpx;
    }
}
</style>

