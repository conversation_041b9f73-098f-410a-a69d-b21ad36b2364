<template>
    <view class="login-agreement">
        <checkbox-group @change="loginCheckboxChange">
            <label>
                <checkbox value="1" color="#127fd2" style="transform:scale(0.7)" />我已阅读并同意
            </label>
        </checkbox-group>
        <span class="login-agreement-text">
            <view @click="showServiceAgreement('serviceAgreement')">《上上签服务协议》</view>、
            <view @click="showServiceAgreement('privacyPolicy')">《隐私政策》</view>和
            <view @click="showServiceAgreement('digitalCertificatePotocal')">《数字证书使用协议》</view>
        </span>
    </view>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        loginCheckboxChange(e) {
            this.$sensors.track({
                eventName: 'Mp_Login_BtnClick',
                eventProperty: {
                    icon_name: e.detail.value.length > 0 ? '我已同意协议' : '取消同意协议',
                },
            });
            this.$emit('input', e.detail.value.length > 0);
        },
        showServiceAgreement(type) {
            const iconNameMap = {
                'serviceAgreement': '上上签服务协议',
                'privacyPolicy': '隐私政策',
                'digitalCertificatePotocal': '数字证书使用协议',
            };
            this.$sensors.track({
                eventName: 'Mp_Login_BtnClick',
                eventProperty: {
                    icon_name: iconNameMap[type],
                },
            });
            const baseUrl = this.$http.baseURL;
            const urlMap = {
                'serviceAgreement': `${baseUrl}/account-center/legal-agreement/service-agreement`,
                'privacyPolicy': `${baseUrl}/account-center/legal-agreement/privacy-policy`,
                'digitalCertificatePotocal': `${baseUrl}/account-center/legal-agreement/digital-certificate-protocal`,
            };
            uni.navigateTo({
                url: `/views/agreementWebview/index?url=${urlMap[type]}`,
            });
        },
    },
};
</script>

<style lang="scss">
.login-agreement{
    font-size: 26rpx;
    &-text {
        display: inline;
        view{
            display: inline-block;
            color: $--color-primary;
        }
    }
    &-popup{
        width: 630rpx;
        height: 800rpx;
        padding: 50rpx 0;
        border-radius: 20rpx;
        &-content{
            height: 680rpx;
            overflow: scroll;
        }
        &-button button{
            width: 570rpx;
            margin-top: 30rpx;
        }
    }
    .uni-data-checklist {
        float: left;
        .checklist-group {
            display: inline-block;
            .checklist-box{
                display: inline-block;
                margin: 5rpx 5rpx 0 0;
                // #ifdef MP-ALIPAY
                margin: 0 !important;
                // #endif
            }
        }
    }
}
</style>
