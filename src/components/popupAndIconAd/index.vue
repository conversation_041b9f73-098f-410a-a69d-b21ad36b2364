<template>
    <view>
        <view v-for="adItem in showAdList" :key="adItem.adId">
            <view
                v-if="adItem.show"
                class="spread-region"
                :class="showPopupAd(adItem) ? 'spread-region__popup' : (showIconAd(adItem) ? 'spread-region__icon': '')"
            >
                <view class="spread-region__wrapper">
                    <view class="spread-region__body" :style="{'width': adImgWidth(adItem) + 'px'}">
                        <image
                            mode="widthFix"
                            class="spread-region__img"
                            :src="customerImg(adItem)"
                            alt="img"
                            @click="handleClick(adItem)"
                        />
                    </view>
                    <view class="spread-region__close-icon">
                        <text class="icon-ic_close"
                            :class="showPopupAd(adItem) ? 'spread-region__close-icon-light' : (showIconAd(adItem) ? 'spread-region__close-icon-dark': '')"
                            @click="closeAd(adItem)"
                        >
                        </text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { addAdRecord } from 'src/api/ad.js';
const RECORD_MAP = {
    'adShow': 0,
    'confirmClick': 1,
    'dialogShow': 2,
};
export default {
    name: 'PopupAndIconAd',
    props: {
        adList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            screenWidth: 375,
            recordMap: RECORD_MAP,
            showAdList: [],
        };
    },
    computed: {
        showPopupAd() {
            return (item) => {
                return item.show && item.showcase === 1;
            };
        },
        showIconAd() {
            return (item) => {
                return item.show && item.showcase === 2;
            };
        },
        customerImg() {
            return (item) => {
                return `${this.$http.config.baseURL}/ad-api/plan/file?fileId=${encodeURIComponent(item.appFileId || item.appBigFileId)}&&access_token=${uni.getStorageSync('accessToken')}`; // 字段 appFileId 和 appBigFileId 同一时间只有一个有值
            };
        },
        adImgWidth() {
            return (item) => {
                const userConfigWidth = item.imageSize.split('*')[0];
                const showWidth = Math.min(userConfigWidth, this.screenWidth * 2 / 3);
                return showWidth;
            };
        },
    },
    methods: {
        handleClick(item) {
            // 认为是H5页面
            if (item.consumerUrl.includes('https')) {
                uni.navigateTo({
                    url: `/views/webviewRedirect/index?url=${encodeURIComponent(`${item.consumerUrl}`)}`,
                });
            } else {
                // 否则认为是小程序页面
                uni.navigateTo({
                    url: item.consumerUrl,
                });
            }
            addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['confirmClick'] });
        },
        async closeAd(item) {
            this.showAdList.forEach(ad => {
                if (ad.adId === item.adId) {
                    ad.show = false;
                }
            });
        },
        addRecord() {
            this.showAdList.forEach((item) => {
                // 每展示一次记录一次
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['adShow'] });
            });
        },
        handleShowList() {
            const tempList = this.adList.filter(item => [1, 2].includes(item.showcase) && !item.externalFlag);
            this.showAdList = tempList.map((ad) => {
                return { ...ad, show: true };
            });
        },
    },
    async mounted() {
        this.handleShowList();
        this.addRecord();
    },
    onReady() {
        const _this = this;
        uni.getSystemInfo({
            success: function(res) {
                _this.screenWidth = res.screenWidth;
            },
        });
    },
};
</script>

<style lang="scss" scoped>
.spread-region__popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}
.spread-region__icon {
    position: fixed;
    right: 5px;
    top: 100px;
}
.spread-region {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &__wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .icon-ic_close {
            padding: 4px;
            font-size: 8px;
            border-radius: 10px;
        }
    }
    &__img {
        width: 100%;
        cursor: pointer;
    }
    &__close-icon-light {
        border: 1px solid $--background-color-base;
        color: $--background-color-base;
    }
    &__close-icon-dark {
        border: 1px solid $--color-text-regular;
        color: $--color-text-regular;
    }

}
</style>
