<template>
    <view class="private-message">
        <view class="add-receiver__item"><view><text class="add-receiver__unnecessary">*</text>{{ '签约须知' }}</view></view>
        <textarea
            v-model="privateLetter"
            class="add-receiver__item-info"
            placeholder-class="add-receiver__input_placeholder"
            placeholder="添加签约须知"
           :disabled="disabled"
            maxlength="255"
        />
        <!-- #ifdef MP-WEIXIN -->
        <view v-if="communicateInfo.signInstructionDocumentInfo.ifDisplayConfig">
            <view class="add-receiver__item-title">
                <div class="title">添加文档<span>（{{ communicateInfo.signInstructionDocumentInfo.ifRequired ? '必填' : '选填' }})</span></div>
                <div class="tip">支持Word、Excel、PDF以及图片，可在签署前在线预览。不超过3份</div>
            </view>
            <FileList
                :fileList="communicateInfo.signInstructionDocumentInfo.privateLetterFileInfos"
                :disabled="disabled"
                btnText="添加文档"
                @add="popUpOpen"
                @remove="removeFile($event,0)">
            </FileList>
        </view>
        <view v-if="communicateInfo.signInstructionOriginDocumentInfo.ifDisplayConfig">
            <view class="add-receiver__item-title">
                <div class="title">添加源文件
                    <span>（{{ communicateInfo.signInstructionOriginDocumentInfo.ifRequired ? '必填' : '选填' }})</span>
                </div>
                <div class="tip">支持word、excel、pdf，不可在线预览必须下载后才能查看，但下载后仍为word或excel或pdf，方便继续编辑。不超过3份。</div>
            </view>
            <FileList
                :fileList="communicateInfo.signInstructionOriginDocumentInfo.privateLetterFileInfos"
                :disabled="disabled"
                btnText="添加源文件"
                @add="handleSelectUploadType('file', 1)"
                @remove="removeFile($event,1)">
            </FileList>
        </view>
        <view v-if="isShowAddCompressedFile">
            <view class="add-receiver__item-title">
                <div class="title">添加压缩文件
                    <span>（{{ communicateInfo.signInstructionZipInfo.ifRequired ? '必填' : '选填' }}）</span>
                </div>
                <div class="tip"> 支持zip文件，压缩文件的哈希值将记入合同的签约存证页中。限制大小为{{signRequirementFileMaxSize}}M，仅限1份。）</div>
            </view>
            <FileList
                :fileList="compressedFile.fileId ?[compressedFile]:[]"
                :disabled="disabled"
                btnText="添加压缩文件"
                @add="zipInfoPopUpOpen"
                @remove="removeFile($event,2)">
            </FileList>
        </view>
        <!-- #endif -->
        <uni-popup ref="pickerPopUp" type="bottom">
            <view class="upload-file__popup">
                <view class="upload-file__popup_list"
                    v-for="item in uploadTypes"
                    :key="item.key"
                    @click="handleSelectUploadType(item.key, 0)"
                >{{ item.value }}</view>
                <view class="upload-file__popup_cancel">
                    <view class="upload-file__popup_btn" :class="{'margin-fix' : isIphoneX}" @click="popupCancel">取消</view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="zipInfoPopUpOpen" type="center">
            <view class="zipInfo__popup" >
                <radio-group @change="fileChange"  v-if="uploadFileList.length">
                    <label v-for="file in uploadFileList" :key="file.fileId">
                        <view class="zipInfo__radio-line">
                            <radio :value="file.fileId" :checked="selectedFileId === file.fileId" />
                             <text>{{file.fileName}}</text>
                        </view>
                    </label>
                </radio-group>
                <view  v-else class="file-list-empty">没有可选择的文件，请上传文件</view>
                <view class="opt-btns">
                    <button class="opt-btn" @click="handleSelectUploadType('file', 2)">
                    上传文件
                    </button>
                    <button  type="primary" class="opt-btn" @click="confirmSelectedCompressedFile">
                    确定
                    </button>
                </view>
                
            </view>
        </uni-popup>
    </view>
</template>
<script>
import { mapState, mapActions } from 'vuex';
import SelectUploadType from 'components/selectUploadType';
import  FileList from 'src/components/fileList/index.vue'

export default{
     components: {
        FileList,
        SelectUploadType,
    },
    data(){
        return {
            uploadTypes: [{
                key: 'camera',
                value: '拍摄照片',
            },  {
                key: 'album',
                value: '从手机相册选择',
            },  {
                key: 'file',
                value: '从微信选择',
            }],
            currentUploadIndex: 0,
            uploadFileList: [], //  压缩文件上传列表
            selectedFileId: '', // 压缩文件上传列表选中的fileId
            compressedFile:{},
            signRequirementFileMaxSize:200,  // 签约须知乐高城配置的文件大小
        }
    },
    computed: {
        ...mapState('send', ['curReceiver','isIphoneX']),
        ...mapState('template', ['draftId', 'templatePermissions', 'templateDocList']),
        ...mapState('user', ['featureIds']),
        disabled(){
            return !this.templatePermissions.modifySignRequirement;
        },
        isShowAddCompressedFile(){
            console.log('featureIds'+this.featureIds);
            return this.featureIds.indexOf('158') > -1 && this.communicateInfo.signInstructionZipInfo.ifDisplayConfig;
        },
        communicateInfo(){
            return this.curReceiver.communicateInfo;
        },
        privateLetter: {
            get() {
                return this.communicateInfo?.privateLetter;
            },
            set(val) {
                this.communicateInfo.privateLetter = val;
            },
        },
        uploadObject(){
            return [
                {
                    uploadUrl: `/template-api/v2/draft/${this.draftId}/private-letter/upload?keep=false`,
                    documentInfo:this.communicateInfo.signInstructionDocumentInfo,
                    fileTypes:['doc','docx','png','jpeg','pdf','xlsx','xls','csv'],
                },{
                    uploadUrl:`/template-api/v2/draft/${this.draftId}/private-letter/upload?keep=true`,
                    documentInfo: this.communicateInfo.signInstructionOriginDocumentInfo,
                    fileTypes:['doc','docx','png','jpeg','pdf','xlsx','xls','csv'],
                },{
                    uploadUrl: `/template-api/v2/draft/${this.draftId}/instructions_appendix/upload`,
                    fileTypes:['zip'],
                }
            ]
        },
    },
    methods:{
        ...mapActions('user', ['getFeatures']),
        // 获取压缩文档可选列表
        getCompressedFileList(){
            this.$http.get(`/template-api/v2/templates/user/${this.draftId}/instructions-appendix`).then((res) => {
                this.uploadFileList = res.data;
                this.selectedFileId = res.data[0] && res.data[0].fileId || '';
            });
        },
        // 确定选择压缩文档
        confirmSelectedCompressedFile() {
            if (!this.selectedFileId) {
                return this.$MessageToast.error('请选择一个文件');
            }
            this.compressedFile = (this.uploadFileList || []).filter(item => item.fileId === this.selectedFileId)[0];
            this.communicateInfo.signInstructionZipInfo.instructionsAppendixId =  this.compressedFile.fileId;
            this.communicateInfo.signInstructionZipInfo.instructionsAppendixName =  this.compressedFile.fileName;
            this.$refs.zipInfoPopUpOpen.close();
        },
        fileChange(e){
            this.selectedFileId = e.detail.value
        },
        popUpOpen() {
            if (this.uploadObject[0].documentInfo.privateLetterFileInfos.length>=3){
                this.$toast.error(`最多只能选择3个文件`);
                return false;
            }
            this.$refs.pickerPopUp.open();
        },
        popupCancel() {
            this.$refs.pickerPopUp.close();
        },
        zipInfoPopUpOpen(){
            this.$refs.zipInfoPopUpOpen.open();
            this.getCompressedFileList();
        },
        handleSelectUploadType(type,index) {
            this.currentUploadIndex = index;
            const _self = this;
            if (type === 'camera' || type === 'album') {
                uni.chooseImage({
                    count: 3,
                    sourceType: [type],
                    success: (res) => {
                        console.log(res.tempFiles);
                        _self.addFile(res.tempFiles);
                    },
                    fail: () => {},
                });
            } else {
                uni.chooseMessageFile({
                    count: this.currentUploadIndex == '2' ? 1:3,
                    type: 'all',
                    success(res) {
                        _self.addFile(res.tempFiles);
                    },
                    fail: () => {},
                });
            }
        },
        checkBeforeUpload(addFileList = []) {
            if (this.currentUploadIndex < 2){
                const totalLen = addFileList.length + this.uploadObject[this.currentUploadIndex].documentInfo.privateLetterFileInfos.length;
                if (totalLen > 3) {
                    this.$toast.error(`最多只能选择3个文件`);
                    return false;
                }
            }
            const maxSize = this.currentUploadIndex < 2?  5: this.compressedFileMaxSize;
            for (let i = 0; i < addFileList.length; i++) {
                if (addFileList[i].size / 1024 / 1024 > maxSize) {
                    return this.$toast.error(`请上传不超过${maxSize}M的文档`);
                }
                const fileType = addFileList[i].name && addFileList[i].name.split('.')[1];
                if (fileType && !this.uploadObject[this.currentUploadIndex].fileTypes.includes(fileType)){
                    return this.$toast.error('文档格式不符合要求');
                }
            }
            return true;
        },
        async addFile(tempFileList) {
            if (!this.checkBeforeUpload(tempFileList)) {
                return;
            }
            
            this.uploadFile(tempFileList);
            this.popupCancel();
        },
        /* 批量上传文件, 由于小程序的api:uploadFile不支持批量上传, 并发多个请求上传文档*/
        uploadFile(tempFileList = []) {
            if (!tempFileList.length) {
                return this.$toast.error('请先选择文件上传');
            }
            this.$toast.loading({ title: '文件上传中', mask: true });
            const _self = this;
            const promiseList = [];
            tempFileList.forEach((item) => {
                const promise = new Promise((resolve, reject) => {
                    uni.uploadFile({
                        url: `${_self.$http.baseURL}${_self.uploadObject[this.currentUploadIndex].uploadUrl}`,
                        filePath: item.path,
                        name: 'file',
                        header: { Authorization: `bearer ${uni.getStorageSync('accessToken')}` },
                        ...(item.name && {
                             formData:{
                                fileName:item.name
                            },
                        }),
                        success: ({ data }) => {
                            const dataObj = JSON.parse(data);
                            if (!dataObj) {
                                resolve();
                            } 
                            if (this.currentUploadIndex< 2){
                                const documentInfo =  _self.uploadObject[this.currentUploadIndex].documentInfo;
                                documentInfo.privateLetterFileInfos.push(dataObj);
                                documentInfo.privateLetterFileList = documentInfo.privateLetterFileInfos.map(i => i.fileId);
                            } else {
                                this.getCompressedFileList();
                            }
                            resolve();
                        },
                        fail: () => {
                            reject();
                        },
                    });
                });
                promiseList.push(promise);
            });
            return Promise.all(promiseList)
                .then(() => {
                    return _self.$toast.success('上传成功');
                }).catch((err) => {
                    /* 只要有一个Promise被reject了都会走到这来*/
                    uni.showModal({
                        content: '文件上传失败，请确保网络正常后重新上传',
                        showCancel: false,
                        confirmText: '知道了',
                        confirmColor: '#127fd2',
                    });
                }).finally(() => {
                    _self.$toast.hideLoading();
                });
        },
        // 删除文档
        removeFile(fileIndex,index){
            if (index === 0){
                const privateLetterFileInfos = this.communicateInfo.signInstructionDocumentInfo.privateLetterFileInfos;
                privateLetterFileInfos.splice(fileIndex, 1);
                this.communicateInfo.signInstructionDocumentInfo.privateLetterFileList = privateLetterFileInfos.map(i=>i.fileId)
            } else if (index ===1){
                const privateLetterFileInfos = this.communicateInfo.signInstructionOriginDocumentInfo.privateLetterFileInfos;
                privateLetterFileInfos.splice(fileIndex, 1);
                this.communicateInfo.signInstructionOriginDocumentInfo.privateLetterFileList = privateLetterFileInfos.map(i=>i.fileId)
            }else {
                this.compressedFile = {};
                this.communicateInfo.signInstructionZipInfo.instructionsAppendixId = null;
                this.communicateInfo.signInstructionZipInfo.instructionsAppendixName = null;
            }
        },
        // 获取乐高城配置的签约须知附件大小
        getConfigCompressedFileMaxSize() {
            if (this.isShowAddCompressedFile) {
                this.$http.get(`/template-api/v2/templates/query/instructions-appendix/maxSize`).then(res => {
                    this.signRequirementFileMaxSize = +res.data;
                }).catch(() => {
                    // 发生异常时按照 200M 默认值处理
                    this.signRequirementFileMaxSize = 200;
                });
            }
        },
    },
    mounted(){
        this.getFeatures();
        this.getConfigCompressedFileMaxSize();
        this.compressedFile = {
            fileId: this.communicateInfo.signInstructionZipInfo.instructionsAppendixId,
            fileName: this.communicateInfo.signInstructionZipInfo.instructionsAppendixName,
        };
    }
}
</script>
<style lang="scss">
.private-message{
     background:#fff;
    .uni-popup{
        position: absolute;
    }
   
   textarea{
    box-sizing: border-box;
    width: 100%;
    line-height: 50rpx;
    padding: 30rpx;
   }
    .add-receiver__item-title{
       border-top: 1rpx solid #EEEEEE;
       padding: 30rpx;
       padding-bottom: 16rpx;
       line-height: 40rpx;
       .title{
         color: $--color-text-primary;
         font-size: 28rpx;
         font-weight: bold;
         span{
            color:$--color-text-secondary;
            font-size: 22rpx;
         }
       }
       .tip{
         color:$--color-text-regular;
         font-size: 22rpx;
         line-height: 38rpx;
       }
    }
    .select-upload{
        display: inline-block;
    }
    .upload-file__popup {
        background-color: $--color-white;
        .upload-file__popup_list {
            height: 100rpx;
            line-height: 100rpx;
            color: $--color-text-primary;
            text-align: center;
            border-top: 1rpx solid $--border-color-lighter;
        }
        .upload-file__popup_cancel {
            background-color: $--border-color-extra-light;
            padding-top: 16rpx;
        }
        .margin-fix {
            margin-bottom: -70rpx;
        }
        .upload-file__popup_btn {
            background-color: $--color-white;
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
        }
    }
    .zipInfo__popup{
        background-color: $--color-white;
        width:600rpx;
       border-radius: 5px;
        overflow-y: scroll;
        overflow: hidden;
        min-height: 300rpx;
        radio-group{
            overflow-y: auto;
            padding:30rpx 20rpx;
            .zipInfo__radio-line{
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }
        .file-list-empty{
            padding-top: 80rpx;
            text-align: center;
            font-size: 34rpx;
        }
        .opt-btns{
            position: absolute;
            bottom:10rpx;
            .opt-btn {
                width: 250rpx;
                height: 80rpx;
                line-height: 80rpx;
                font-size: 32rpx;
                margin: 0px 25rpx;
                display: inline-block;
            }
        }
        
    }
}
</style>