<template>
    <div class="riding-seal-item"
        :style="{ top: `calc(${(1 - top - height) * 100}%)`}"
    >
        <p class="seal-title">{{ ridingSeal.showName }}</p>
        <div
            class="seal-content"
            :style="{
                'background': `${color}`,
                'opacity': 0.8,
            }"
        >
            <div class="seal-icon">
                <image
                    :src="sealImg"
                />
            </div>
        </div>
    </div>
</template>
<script>
import sealImg from '@/assets/images/label_riding_seal.png';
export default {
    props: {
        ridingSeal: {
            type: Object,
            default: () => {},
        },
        color: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            top: 0,
            height: 0,
            sealImg: sealImg,
        };
    },
    watch: {
        'ridingSeal.y': {
            handler(newVal) {
                this.top = newVal;
            },
            immediate: true,
        },
    },
    mounted() {
        this.top = this.ridingSeal.y;
        this.height = this.ridingSeal.height || 0.01;
    },
};
</script>

<style lang="scss">
.riding-seal-item {
    position: absolute;
    right: 0;
    transform: translate(0px, -12px);
    .seal-title {
        padding: 0 4rpx;
        margin-bottom: 4rpx;
        height: 20rpx;
        line-height: 20rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-sizing: border-box;
        background-color: rgb(255, 247, 182);
        font-size: 8px;
        width: 75px;
    }
    .seal-content {
        width: 75px;
        height: 75px;
        display: flex;
        justify-content: center;
        align-items: center;
        .seal-icon {
            width: 80%;
            height: 80%;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            image {
                width: 60%;
                height: 60%;
            }
        }
    }
}
</style>
