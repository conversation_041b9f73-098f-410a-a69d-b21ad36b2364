<template>
    <view class="label"
        :style="{'left': `${markPosition.x}px`, top: `${markPosition.y}px`, 'minWidth': `${markPosition.width}px`, 'minHeight': `${markPosition.height}px`}"
    >
        <!-- 盖章/签名 -->
        <view
            v-if="labelType === 'SIGNATURE' || labelType === 'SEAL'"
            :style="{'width': markPosition.width + 'px', 'height': markPosition.height + 'px',
                     'opacity': 0.8}"
            class="label-sign p-center"
        >
            <!-- 企业盖章背景圆圈 偏右-->
            <view v-if="labelType === 'SEAL'" :style="{transform: 'translateX('+ markPosition.height *0.11 +'px)'}">
                <view
                    class="p-center"
                    :style="{'width': markPosition.height *0.8 + 'px', 'height': markPosition.height *0.8+ 'px', 'background': '#ffffff', 'opacity': 0.3, 'border-radius': markPosition.width *0.8 + 'px'}"
                >
                </view>
                <image
                    :src="sealImg"
                    alt=""
                    :style="{'width': markPosition.height *0.6 + 'px', 'height': markPosition.height *0.6+ 'px', 'position': 'absolute'}"
                    style="left: 50%; top: 50%; transform: translate(-50%, -50%)"
                ></image>
            </view>
            <!-- 个人签名 -->
            <image
                v-else-if="labelType === 'SIGNATURE'"
                :src="signImg"
                :style="{'width': markPosition.width*0.7 + 'px', 'height': markPosition.height *0.7+ 'px'}"
                alt=""
            ></image>
        </view>
        <!-- 签署日期 -->
        <view
            v-else-if="labelType === 'DATE'"
            :style="{'width': `${markPosition.width}px`, 'height': `${markPosition.height}px`,
                     'line-height': `${markPosition.height}px`, 'opacity': 0.8}"
            class="label-date"
            :class="{'label-active': activeLabelId === curLabelId}"
        >
            签署日期
        </view>
        <!-- 其他业务字段 -->
        <view
            class="label-text"
            :class="{'label-active': activeLabelId === curLabelId}"
            v-else
            :style="{'width': `${markPosition.width}px`, 'height': `${markPosition.height}px`,
                     'line-height': `${markPosition.height}px`, 'opacity': 0.8}"
        >
            <!-- 本地发送 -->
            <view>
                <view class="label-font-18" v-if="!mark.value">{{ mark.labelName }}</view>
                <template v-else>
                    <view class="label-font-18"
                        :style="{'width': `${markPosition.width}px`, 'height':
                            `${markPosition.height}px`, 'line-height': `${markPosition.height}px`}"
                    >{{ mark.value }}</view>
                </template>
            </view>
        </view>
    </view>
</template>

<script>
import signImg from '@/assets/images/label_sign.png';
import sealImg from '@/assets/images/label_seal.png';
export default {
    props: {
        mark: {
            type: Object,
            default: () => ({}),
        },
        activeLabelId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            signImg: signImg,
            sealImg: sealImg,
        };
    },
    computed: {
        markPosition() {
            return this.mark.labelPosition;
        },
        labelType() {
            return  this.mark?.labelType;
        },
        curLabelId() {
            return this.mark?.labelId || '';
        },
    },
    methods: {},
};
</script>

<style lang="scss">
.p-center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.label {
    position: absolute;
    background: #c9e7ff;
    &-date {
        font-size: 24rpx;
        text-align: center;
    }
    &-active {
        border: 1px dashed #ffd65b;
        background-color: #ffd65b;
        box-sizing: border-box;
    }
    &-text {
        font-size: 24rpx;
    }
    &-font-18 {
        font-size: 18rpx;
    }
}
</style>

