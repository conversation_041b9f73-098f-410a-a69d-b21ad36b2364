<template>
    <view class="sample-list">
        <view class="sample-list__item"
            v-for="(sample, index) in samplesData"
            :key="index"
            @click="handleSampleUse(sample)"
        >
            <image class="sample-list__item-img"
                :src="`${host}${sample.previewUrl}?access_token=${accessToken}`"
            >
            </image>
            <text class="sample-list__item-title">{{ sample.sampleTitle }}</text>
        </view>
    </view>
</template>
<script>
import { mapState } from 'vuex';
import { authMixin } from 'mixins/auth';

export default {
    name: 'SampleList',
    mixins: [authMixin],
    props: {
        samplesData: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            dateStr: new Date().getTime(),
            host: this.$http.baseURL,
            accessToken: uni.getStorageSync('accessToken'),
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
    },
    watch: {
        commonHeaderInfo: {
            handler() {
                this.accessToken = uni.getStorageSync('accessToken');
            },
        },
    },
    methods: {
        handleSampleUse({ sampleId, sampleTitle }) {
            this.$sensors.track({
                eventName: 'Mp_ContractSendList_BtnClick',
                eventProperty: {
                    page_name: '文件范本',
                    icon_name: sampleTitle,
                    sample_id: sampleId,
                    sample_name: sampleTitle,
                },
            });
            this.$point('click_sample_document', {
                biz_id: sampleId,
            });

            if (this.entList[this.curEntIndex].authStatus !== 2) {
                this.handleGoAuth({
                    curIndex: this.curEntIndex,
                    showCancel: true,
                });
                return;
            }
            uni.navigateTo({ url: `/subSendViews/sampleSend/index?sampleId=${sampleId}` });
        },
    },
};
</script>

<style lang="scss">
.sample-list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: flex-start;
    padding: 20rpx 40rpx;
    &__item {
        margin-bottom: 30rpx;
        width: calc(50% - 20rpx);
        text-align: center;
        &-img {
            width: 310rpx;
            height: 440rpx;
            border: 1px solid $--border-color-lighter;
            border-radius: 4rpx;
        }
        &-title {
            margin-top: 30rpx;
        }
        &:hover image{
            border: 1px solid $--color-primary;
        }
    }
}
</style>
