<template>
    <div class="contract-search">
        <uni-search-bar v-model="searchContent" placeholder="请输入合同标题或合同参与方信息" clearButton="auto" cancelButton="none" @confirm="handleSearch" />
    </div>
</template>

<script>
export default {
    name: 'SearchBar',
    data() {
        return {
            searchContent: '',
        };
    },
    watch: {
        searchContent(val) {
            if (!val) {
                this.handleSearch();
            }
        },
    },
    methods: {
        handleSearch() {
            this.$emit('search', this.searchContent);
        },
    },
};
</script>

<style lang="scss">
.contract-search{
  width: 100%;
  height: 68rpx;
}
</style>
