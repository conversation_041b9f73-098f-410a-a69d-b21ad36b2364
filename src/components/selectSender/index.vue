<template>
    <view class="switch-ent">
        <picker
            @change="handleSwitchEnt"
            :value="curEntIndex"
            range-key="showName"
            :range="entList"
            @click="handleClickEvent"
        >
            <view v-if="fromAccountPage" class="switch-ent__account-page">
                <text>切换企业</text>
            </view>
            <view v-else-if="fromChargingPage && identityName.length" class="switch-ent__account-page">
                <text>{{ identityName }}</text>
            </view>

            <view v-else-if="fromAnniversaryPage" class="switch-ent__anniversary-page">
                <text class="name">{{ entList[curEntIndex].showName }}</text>
                <view class="right">
                    <text class="switch-name">切换企业</text>
                    <text class="icon-qiehuan"></text>
                </view>
            </view>
            <view v-else :class="fromLocalSendPage ? '' : 'switch-ent__content'">
                <text class="switch-ent__name" :class="fromLocalSendPage ? '' : 'switch-ent__name-account'">{{ entList[curEntIndex].showName }}</text>
                <text class="icon-qiehuanzhuti" v-if="showDownArrowIcon"></text>
                <text class="icon-ic_forward" v-if="fromLocalSendPage"></text>
            </view>
        </picker>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex';
import { switchEnt } from 'api/send';
import { authMixin } from 'mixins/auth';
const pageNameMap = {
    'sample': '文件范本',
    'template': '模板发起',
    'local': '本地发起',
};
export default {
    mixins: [authMixin],
    props: {
        fromLocalSendPage: {
            type: Boolean,
            default: false,
        },
        showDownArrowIcon: {
            type: Boolean,
            default: false,
        },
        fromAccountPage: {
            type: Boolean,
            default: false,
        },
        activeTab: {
            type: String,
            default: '',
        },
        fromAnniversaryPage: {
            type: Boolean,
            default: false,
        },
        fromChargingPage: {
            type: Boolean,
            default: false,
        },
        identityName: {
            type: String,
            default: '',
        },
        showAuthCancelBtn: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        ...mapState('send', ['isIphoneX']),
        ...mapState('user', ['commonHeaderInfo']),
        ...mapGetters('user', ['getUserPermissions']),
        ...mapState(['isWeWork']),
        // 是否有普通发起的权限
        hasLocalSendAuth() {
            // 企业需要是否有本地发起权限
            if ((this.commonHeaderInfo.userType || '').toLowerCase() === 'enterprise') {
                return this.getUserPermissions.sendLocal;
            }
            // 个人可以直接发起
            return true;
        },
        authReturnUrl() {
            return this.fromLocalSendPage ? '/subSendViews/localSend/index' : '/views/account/index';
        },
    },
    watch: {
        hasLocalSendAuth: {
            handler(val) {
                this.setHasLocalSendRight(val);
            },
            immediate: true,
        },
    },
    methods: {
        ...mapMutations('send', ['setH5Params', 'setHasLocalSendRight', 'setCurEntIndex', 'setCurEntId']),
        ...mapActions('user', ['getFeatures', 'getHeadInfo', 'setEntList']),
        toAuth(type) {
            let authUrl;
            const returnUrl = this.fromLocalSendPage ? '/subSendViews/localSend/index' : '/views/account/index';
            if (type === 'person') {
                authUrl = encodeURIComponent(`${this.$http.baseURL}/mp/auth-m/individual/auth?isBestSignApplet=true&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&returnUrl=${returnUrl}`);
            } else {
                authUrl = encodeURIComponent(`${this.$http.baseURL}/auth-p/enterprise?isBestSignApplet=true&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&&returnUrl=${returnUrl}`);
            }

            uni.navigateTo({
                url: `/views/webviewRedirect/index?url=${authUrl}`,
            });
        },
        async changeEnt(entId) {
            try {
                this.$toast.loading({ mask: true });
                const { data } = await switchEnt(entId);
                uni.setStorageSync('accessToken', data.access_token);
                uni.setStorageSync('refreshToken', data.refresh_token);
                await this.getFeatures();
                await this.getHeadInfo(); // roleDetails 权限 和主体有关系，此处需重新请求head-info
                this.$toast.hideLoading();
            } catch {
                this.$toast.hideLoading();
            }
        },
        async handleSwitchEnt(e) {
            const curIndex = e.target.value;
            const entId = this.entList[curIndex].entId;
            this.$toast.loading({ mask: true });
            await this.changeEnt(entId);
            this.$toast.hideLoading();
            if (this.entList[curIndex].authStatus !== 2) {
                this.handleGoAuth({
                    curIndex,
                    returnUrl: this.authReturnUrl,
                    showCancel: this.showAuthCancelBtn,
                });
            }
            this.setCurEntIndex(curIndex);
            this.setCurEntId(entId);
            this.$emit('switchEntSucces');
        },
        handleClickEvent() {
            if (this.fromAnniversaryPage) {
                return this.$emit('switchEntIdentity');
            }
            this.$sensors.track({
                eventName: this.fromAccountPage ? 'Mp_PersonalCenter_BtnClick' : 'Mp_ContractSendList_BtnClick',
                eventProperty: {
                    page_name: this.fromAccountPage ? null : pageNameMap[this.activeTab],
                    icon_name: this.fromAccountPage ? '切换企业' : '切换发件方',
                },
            });
        },
    },
};
</script>
<style lang="scss">
.switch-ent {
    &__account-page{
        width: 120rpx;
        height: 40rpx;
        font-size: 22rpx;
        background-color: $--color-primary;
        border-radius: 20rpx;
        text-align: center;
        color: $--color-white;
        padding-top: 2rpx;
        line-height: 36rpx;
    }
    &__anniversary-page{
        width: calc(100vw - 180rpx);
        height: 60rpx;
        line-height: 60rpx;
        font-size: 22rpx;
        margin-bottom: 42px;
        box-sizing: border-box;
        border: 1px solid #DD9240;
        border-radius: 10rpx;
        display: flex;
        text-align: center;
        color: $--color-text-primary;
        .name{
            flex: 1;
            padding: 0 10rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .right{
            width: 180rpx;
            height: 100%;
            background-color: #DD9240;
            display: flex;
            justify-content: center;
        }
        .switch-name{
            color: #fff;
        }
        .icon-qiehuan{
            color: #fff !important;
            font-size: 18rpx;
            margin-left: 5px;
        }
    }
    &__content {
        background-color: $background-color;
        color: $--color-primary;
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-radius: 40rpx;
        max-width: 50%;
        margin-top: 20rpx;
        padding: 0 20rpx;
    }
    &__name {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 100rpx;
        line-height: 100rpx;
    }
    &__name-account {
        height: 80rpx;
        line-height: 80rpx;
    }
    .icon-ic_forward {
        color: $--color-text-secondary;
        font-size: 30rpx;
        float: right;
    }
    .icon-qiehuanzhuti {
        color: $--color-primary;
        font-size: 30rpx;
        margin-left: 5px;
    }
}

</style>
