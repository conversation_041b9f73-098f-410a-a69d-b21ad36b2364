<template>
    <view class="upload-file">
        <view
            v-if="showDefaultUploadBtn"
            class="upload-file__btn"
            :class="{
                'upload-file__btn_higher': changeBtnHeight,
                'upload-file__btn_right': changeBtnRight,
            }"
            @click="popUpOpen"
        >{{ uploadBtnText }}</view>
        <slot></slot>
        <uni-popup ref="pickerPopUp" type="bottom">
            <view class="upload-file__popup">
                <view class="upload-file__popup_list"
                    v-for="item in uploadTypes"
                    :key="item.key"
                    @click="handleSelectUploadType(item.key)"
                >{{ item.value }}</view>
                <view class="upload-file__popup_cancel">
                    <view class="upload-file__popup_btn" :class="{'margin-fix' : isIphoneX}" @click="popupCancel">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>
<script>
import { uniPopup } from '@dcloudio/uni-ui';
import { mapMutations, mapState } from 'vuex';
export default {
    components: {
        uniPopup,
    },
    props: {
        uploadBtnText: {
            type: String,
            default: '请上传',
        },
        uploadUrl: {
            type: String,
            default: '/contract-api/file?type=CONTRACT_SOURCE',
        },
        uploadedFileList: {
            type: Array,
            default: () => [],
        },
        showDefaultUploadBtn: {
            type: Boolean,
            default: true,
        },
        uploadFileType: {
            type: String,
            default: 'file',
        },
        changeBtnHeight: {
            type: Boolean,
            default: false,
        },
        changeBtnRight: {
            type: Boolean,
            default: false,
        },
        maxUploadNum: {
            type: Number,
            default: 10,
        },
    },
    data() {
        return {
            uploadTypes: [{
                key: 'camera',
                value: '拍摄照片',
            },  {
                key: 'album',
                value: '从手机相册选择',
            }],
        };
    },
    computed: {
        ...mapState(['isWeWork']),
        ...mapState('send', ['isIphoneX']),
    },
    methods: {
        ...mapMutations('send', ['setUploadedFileList']),
        popUpOpen() {
            this.$refs.pickerPopUp.open();
        },
        popupCancel() {
            this.$refs.pickerPopUp.close();
        },
        checkBeforeUpload(addFileList = []) {
            const totalLen = addFileList.length + this.uploadedFileList.length;
            if (this.maxUploadNum !== 1 && totalLen > this.maxUploadNum) {
                this.$toast.error(`最多只能选择${this.maxUploadNum}个文件`);
                return false;
            }
            return true;
        },
        async addFile(tempFileList, type) {
            if (!this.checkBeforeUpload(tempFileList)) {
                return;
            }
            for (let i = 0; i < tempFileList.length; i++) {
                if (tempFileList[i].size / 1024 / 1024 > 5 && type !== 'file') {
                    return this.$toast.error('请上传不超过5M的照片');
                }
            }
            this.uploadFile(tempFileList);
            this.popupCancel();
        },
        /* 批量上传文件, 由于小程序的api:uploadFile不支持批量上传, 并发多个请求上传照片*/
        uploadFile(tempFileList = []) {
            if (!tempFileList.length) {
                return this.$toast.error('请先选择文件上传');
            }
            this.$toast.loading({ title: '文件上传中', mask: true });
            const _self = this;
            const promiseList = [];
            tempFileList.forEach((item) => {
                const promise = new Promise((resolve, reject) => {
                    uni.uploadFile({
                        url: `${_self.$http.baseURL}${this.uploadUrl}`,
                        filePath: item.path,
                        name: 'file',
                        header: { Authorization: `bearer ${uni.getStorageSync('accessToken')}` },
                        success: ({ data }) => {
                            if (JSON.parse(data).code === '180012') {
                                return reject({ code: '180012' });
                            }
                            const dataObj = JSON.parse(data);
                            _self.$emit('uploadResponse', dataObj);
                            _self.uploadedFileList.push({ fileId: dataObj.fileId, fileName: dataObj.fileName, pageNum: _self.uploadedFileList.length, tempUrl: item.path, name: item.name, type: item.type || '' });
                            resolve();
                        },
                        fail: () => {
                            reject();
                        },
                    });
                });
                promiseList.push(promise);
            });
            return Promise.all(promiseList)
                .then(() => {
                    _self.$emit('uploadFileList', _self.uploadedFileList);
                    return _self.$toast.success('上传成功');
                }).catch((err) => {
                    /* 只要有一个Promise被reject了都会走到这来*/
                    uni.showModal({
                        content: err.code === '180012' ? '尚未开启跨平台加签的模板不能使用带数字证书的文件。' : '文件上传失败，请确保网络正常后重新上传',
                        showCancel: false,
                        confirmText: '知道了',
                        confirmColor: '#127fd2',
                    });
                }).finally(() => {
                    _self.$toast.hideLoading();
                });
        },
        handleSelectUploadType(type) {
            const _self = this;
            if (type === 'camera' || type === 'album') {
                uni.chooseImage({
                    count: this.maxUploadNum,
                    sourceType: [type],
                    success: (res) => {
                        _self.addFile(res.tempFiles, type);
                    },
                    fail: () => {},
                });
            } else {
                if (this.isWeWork) {
                    uni.qy.chooseMessageFile({
                        count: this.maxUploadNum,
                        type: this.uploadFileType,
                        success(res) {
                            _self.addFile(res.tempFiles, type);
                        },
                        fail: () => {},
                    });
                } else {
                    uni.chooseMessageFile({
                        count: this.maxUploadNum,
                        type: this.uploadFileType,
                        success(res) {
                            _self.addFile(res.tempFiles, type);
                        },
                        fail: () => {},
                    });
                }
            }
        },
    },
    created() {
        // #ifdef MP-WEIXIN
        this.uploadTypes.push({
            key: 'file',
            value: '从微信选择',
        });
        // #endif
    },
};
</script>

<style lang="scss">
.upload-file {
    &__btn {
        height: 84rpx;
        line-height: 84rpx;
        background-color: $--color-white;
        text-align: center;
        color: $--color-primary;
    }
    &__btn_higher {
        height: 99rpx;
        line-height: 99rpx;
        box-sizing: border-box;
    }
    &__btn_right {
        text-align: right;
    }
    &__popup {
        background-color: $--color-white;
        &_list {
            height: 100rpx;
            line-height: 100rpx;
            color: $--color-text-primary;
            text-align: center;
            border-top: 1rpx solid $--border-color-lighter;
        }
        &_cancel {
            background-color: $--border-color-extra-light;
            padding-top: 16rpx;
        }
        .margin-fix {
            margin-bottom: -70rpx;
        }
        &_btn {
            background-color: $--color-white;
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
        }
    }
}
</style>
