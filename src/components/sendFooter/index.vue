<template>
    <view class="send-footer" :class="{'send-footer_template': sendType !== 'localSend'}">
        <view class="send-footer-receivers-title">选择签约方：</view>
        <scroll-view
            class="send-footer-receivers"
            scroll-x="true"
        >
            <view class="send-footer-receivers-container">
                <view class="send-footer-receivers-list">
                    <view
                        class="send-footer-receivers-item"
                        v-for="(item, index) in receivers"
                        :key="item.receiverId"
                        :style="{'background': bgColor(item)}"
                        @click="switchReceiver(item)"
                    >
                        <text>{{ item.enterpriseName || item.userName || item.userAccount || item.roleName }}</text>
                        <text v-if="receiverActiveId ? receiverActiveId === item.receiverId : index === 0 " class="icon-ic_singleoption_selected"></text>
                    </view>
                </view>

            </view>
        </scroll-view>
        <view class="send-footer-btn">
            <view class="send-footer-icon" v-if="['SEAL', 'SEAL_AND_SIGNATURE'].includes(signType)" @click.prevent="emitClickAddMark('SEAL')">
                <text class="iconic icon-ic_homepage_examine_selected"></text>
                <text>{{ '盖章' }}</text>
            </view>
            <view class="send-footer-line" v-if="['SEAL', 'SEAL_AND_SIGNATURE'].includes(signType)">|</view>
            <view class="send-footer-icon" v-if="['SIGNATURE', 'SEAL_AND_SIGNATURE', 'ENTERPRISE_SIGNATURE'].includes(signType)" @click.prevent="emitClickAddMark('SIGNATURE')">
                <text class="iconic icon-ic_contract_sign"></text>
                <text>{{ signatureName }}</text>
            </view>
            <view class="send-footer-line" v-if="['SIGNATURE', 'SEAL_AND_SIGNATURE', 'ENTERPRISE_SIGNATURE'].includes(signType)">|</view>
            <view class="send-footer-icon" @touchstart="emitClickAddMark('DATE')">
                <text class="icon-ic_contract_date iconic"></text>
                <text>签署日期</text>
            </view>
            <view class="send-footer-action" @click="toNext">发送</view>
        </view>
    </view>
</template>

<script>
import { mapMutations, mapActions, mapState } from 'vuex';
import { colorInfo } from '@/utils/contract';
import { validDraft } from 'src/api/template.js';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['receivers', 'docList', 'isEnt', 'contractId', 'marks', 'receiverActiveIndex'],
    data() {
        return {
            isTipsShow: uni.getStorageSync('needShowSendTips') === '1',
            receiverActiveId: 0,
        };
    },
    computed: {
        ...mapState('send', ['sendType']),
        ...mapState('template', ['templatePermissions']),
        bgColor() {
            return (item) => {
                const index = this.receivers.findIndex(receiver => receiver.receiverId === item.receiverId);
                return colorInfo[index % 9] || 'transparent';
            };
        },
        signType() {
            return this.receivers[this.receiverActiveIndex]?.signType;
        },
        signatureName() {
            let name = '';
            // 签名/盖章/经办人签字
            switch (this.signType) {
                case 'SEAL': name = '盖章'; break;
                case 'SEAL_AND_SIGNATURE': name = '盖章人签字'; break;
                case 'ENTERPRISE_SIGNATURE': name = '经办人签字'; break;
                case 'SIGNATURE': name = '签名';
            }
            return name;
        },
    },
    methods: {
        ...mapActions('send', ['getCharging']),
        ...mapMutations('send', ['setDefinesData', 'setDefineIndex', 'setFlowStepList', 'setMarks']),
        ...mapMutations('template', ['setTemplateFlowList']),
        ...mapMutations(['setToastParam']),
        // 切换当前签署方
        switchReceiver(item) {
            this.receiverActiveId = item.receiverId;
            this.$emit('updateReceiverActiveId', item.receiverId);
        },
        emitClickAddMark(type) {
            if (this.sendType === 'templateSend' && !this.templatePermissions.modifySignLabel) {
                return this.$toast.error('暂无新增签署字段权限');
            }
            this.$emit('clickAddMark', type);
        },
        getDefines() {
            const url = this.sendType === 'localSend'
                ? `/contract-api/contracts/${this.contractId}/send-defines`
                : `/template-api/v2/draft/${this.contractId}/find-workflow`;
            return this.$http.get(url, {}, { noToast: this.sendType !== 'localSend' });
        },
        checkSignLabel() {
            const marks = this.sendType === 'localSend' ? this.marks : Object.values(this.marks).flat();
            const everyReceiverHasMarked = this.receivers.every(receiver => {
                return marks.some(mark => {
                    return mark.receiverId === receiver.receiverId && ((mark.type || mark.labelType) === 'SEAL' || (mark.type || mark.labelType) === 'SIGNATURE');
                });
            });

            const everyDocHasMarked = this.receivers.every(receiver => {
                return this.docList.every(doc => {
                    return (this.marks[doc.documentId] || []).some(mark => {
                        return mark.receiverId === receiver.receiverId && (mark.labelType === 'SEAL' || mark.labelType === 'SIGNATURE');
                    });
                });
            });

            return {
                everyReceiverHasMarked,
                everyDocHasMarked,
            };
        },
        handleFlowList() {
            // 审批流
            this.getDefines()
                .then(({ data }) => {
                    // 触发审批流程
                    if (this.sendType === 'localSend' && data.length) {
                        this.setFlowStepList([]); // 清空数据，fix 点击返回按钮、重新选择类型出错
                        this.setDefinesData(data);
                        if (data.length === 1) {
                            this.setDefineIndex(0);
                        }
                        uni.navigateTo({
                            url: data.length > 1 ? '/subSendViews/selectApproval/index' : '/subSendViews/sendApproval/index',
                        });
                    } else if (this.sendType === 'templateSend' && data.length) {
                        this.setTemplateFlowList(data);
                        uni.navigateTo({
                            url: '/subSendViews/templateSelectApproval/index',
                        });
                    } else { // 不触发审批流程，弹出计费弹窗
                        this.getCharging();
                    }
                })
                .catch(err => {
                    const { data, code, message } = err.response?.data;
                    if (code === '300001' && data?.contractDecoration === false) {
                        uni.showModal({
                            title: '试用结束/功能到期',
                            content: '合同装饰：骑缝章+水印功能已到期停用，请清除配置，或者续费后，方可继续使用。',
                            confirmText: '直接购买',
                            success(res) {
                                if (res.confirm) {
                                    uni.navigateTo({ url: '/views/charge/index' });
                                }
                            },
                        });
                        return;
                    }
                    uni.showToast({
                        title: message,
                        icon: 'none',
                    });
                });
        },
        async checkDraft() {
            if (this.sendType === 'templateSend') {
                const { data } = await validDraft({ draftId: this.contractId });
                const signTypeErrorList = this.signTypeErrorList(data);
                if (data.errorMsg.length || signTypeErrorList.length) {
                    uni.showModal({
                        title: '提示',
                        content: [
                            data.errorMsg.join(';'),
                            signTypeErrorList.join(';'),
                        ].join(';'),
                        confirmText: '确定',
                        confirmColor: '#127fd2',
                        showCancel: false,
                    });
                    // this.setToastParam({ show: true, message: data.errorMsg.join(';') });
                    // setTimeout(() => {
                    //     this.setToastParam({ show: false, message: '' });
                    // }, 2000);
                    return false;
                }
            }
            // 本地发送接口需要labels参数
            this.setMarks(this.marks);
            return true;
        },
        signTypeErrorList(data) {
            const signTypeError = data.signTypeError;
            const list = [];
            if (signTypeError.needSeal.length) {
                list.push('根据【盖章】的签署要求，需在下列文件中设置【盖章】签署位置');

                signTypeError.needSeal.forEach((item) => {
                    const documentNames = item.documents
                        .map((document) => document.name)
                        .join('、');
                    list.push(`签署人（${item.roleName}）:${item.errMsg}${documentNames}`);
                });
            }
            if (signTypeError.needSignature.length) {
                list.push('根据【签字】的签署要求，需在下列文件中设置【签字】签署位置');

                signTypeError.needSignature.forEach((item) => {
                    const documentNames = item.documents
                        .map((document) => document.name)
                        .join('、');
                    list.push(`签署人（${item.roleName}）:${item.errMsg}${documentNames}`);
                });
            }
            if (signTypeError.needSealSignature.length) {
                list.push('根据【盖章并签字】的签署要求，同一文件中的签署位置需同时设置【盖章】和【盖章人签字】');
                signTypeError.needSealSignature.forEach((item) => {
                    const documentNames = item.documents
                        .map((document) => document.name)
                        .join('、');
                    list.push(`签署人（${item.roleName}）:${item.errMsg}${documentNames}`);
                });
            }
            return list;
        },
        // 发送
        async toNext() {
            const obj = this.checkSignLabel();
            if (!obj.everyReceiverHasMarked) {
                return this.$toast.error('请为每个签署方指定签署位置'); // 需要拦截
            } else if (this.sendType === 'templateSend' && !obj.everyDocHasMarked) {
                const _this = this;
                uni.showModal({
                    title: '提示',
                    content: '没有在文件中指定签署位置的签署人，将接收不到此合同',
                    confirmText: '继续发送',
                    confirmColor: '#127fd2',
                    cancelText: '取消',
                    success: async function(res) {
                        if (res.confirm) {
                            const res = await _this.checkDraft();
                            if (res) {
                                _this.handleFlowList();
                            }
                        }
                    },
                });
            } else {
                const res = await this.checkDraft();
                if (res) {
                    this.handleFlowList();
                }
            }
        },
    },
};
</script>
<style lang="scss">
$mainColor: #127fd2;
$whiteColor: #ffffff;
.send-footer {
    width: 750rpx;
    color: $mainColor;
    position:relative;
    left: 50%;
    transform: translateX(-50%);
    background-color: $whiteColor;
    &.send-footer_template {
        width: 100%;
        .send-footer-action {
            border-radius: 8rpx;
        }
    }
    &-receivers {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 90rpx;
        padding: 15rpx;
        box-sizing:border-box;
        &-title {
            color: $--color-info;
            font-size: 20rpx;
            height: 40rpx;
            line-height: 40rpx;
            border-top: 1rpx solid $--border-color-lighter;
            padding-left: 20rpx;
        }
        &-list {
            display:flex;
        }
        &-item {
            width: auto;
            height: 40rpx;
            line-height: 40rpx;
            font-size: 24rpx;
            color: $mainColor;
            padding: 4rpx 12rpx;
            white-space: nowrap;
            border: 1rpx solid $mainColor;
            margin-left: 16rpx;
            border-radius: 4rpx;
            display:flex;
            .icon-ic_singleoption_selected {
                margin-left: 10rpx;
            }
        }
    }
    &-btn {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 100rpx;
        border-top: 1rpx solid $--border-color-lighter;
        box-sizing: border-box;
        padding: 0 10rpx;
    }
    &-icon {
        background: $whiteColor;
        height: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .iconic {
            font-size: 34rpx;
            margin-right: 12rpx;
        }
        text {
            font-size: 28rpx;
        }
    }
    &-line {
        color: #ddd;
    }
    &-action {
        width: 165rpx;
        height: 80rpx;
        background: $mainColor;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        color: $whiteColor;
    }
    &-tips {
        position: absolute;
        top: -54rpx;
        left: 4rpx;
        color: #FF7979 ;
        width: 240rpx;
        border: 1px solid #FF7979 ;
        border-radius: 4rpx;
        font-size: 20rpx;
        padding: 8rpx;
        background: #FFFAFA;
        &:after {
            background: url('@/assets/images/<EMAIL>');
            background-size: 59rpx 86rpx;
            content: '';
            width: 59rpx;
            height: 86rpx;
            position: absolute;
            left: 88rpx;
            top: 52rpx;
        }
        text {
            font-size: 16rpx;
            padding: 24rpx 12rpx;
        }
        &-right {
            width: 280rpx;
            left: 280rpx;
            &:after {
                background: url('@/assets/images/<EMAIL>');
                background-size: 59rpx 86rpx;;
                left: 64rpx;
            }
        }
    }
}
</style>

