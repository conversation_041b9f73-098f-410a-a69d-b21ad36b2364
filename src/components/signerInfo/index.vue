<template>
    <view class="signer-info">
        <view class="signer-info__title">签约方</view>
        <view class="signer-info__content">
            <view><text class="signer-info__unnecessary">*</text>顺序签署</view>
            <switch :checked="signOrderly" @change="switchChange" class="switchBtn" color="#127fd2" />
        </view>
        <SignerList
            :list="filterReceiverList"
            :isMoveAble="signOrderly"
            @change="onDragSortChange"
            @delete="onDeleteReceiver"
        ></SignerList>
        <AddReceiverBtn></AddReceiverBtn>
    </view>
</template>

<script>
import SignerList from 'components/signerList';
import AddReceiverBtn from 'components/addReceiverBtn';
import { mapMutations, mapState } from 'vuex';
export default {
    components: {
        SignerList,
        AddReceiverBtn,
    },
    data() {
        return {
        };
    },
    computed: {
        ...mapState('send', ['receiverList', 'signOrderly']),
        // 若该签署人为新建状态则不显示
        filterReceiverList() {
            return this.receiverList.filter(item => {
                return !item.isBlank;
            });
        },
    },
    methods: {
        ...mapMutations('send', ['setSignOrderly', 'setReceiverList', 'deleteReceiverList']),
        switchChange(e) {
            this.setSignOrderly(e.target.value);
        },
        onDragSortChange(e) {
            const list = e.data;

            list.sort((item1, item2) => {
                return item1.index - item2.index;
            });
            this.setReceiverList(list);
        },
        onDeleteReceiver(index) {
            this.deleteReceiverList(index);
        },
    },
};
</script>

<style lang="scss">
.signer-info {
    &__title {
        height: 90rpx;
        line-height: 90rpx;
        color: $--color-text-secondary;
        padding: 0 30rpx;
    }
    &__content {
        height: 100rpx;
        line-height: 100rpx;
        background-color: $--color-white;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 30rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        position: relative;
        .switchBtn {
            transform:scale(0.5);
            position: absolute;
            right: 0;
        }
    }
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
}
</style>
