<template>
    <movable-area class="drag-sort" :style="{height: moveHeight}" id="drag">
        <movable-view
            v-for="(item, itemIndex) in currentList"
            :key="itemIndex"
            :x="0"
            :y="item.y"
            direction="vertical"
            disabled
            damping="40"
            :animation="item.animation"
            class="drag-sort-item drag-sort-list"
            :style="{ height: height + 'px' }"
            :class="{'active': active == itemIndex, 'vh-1px-t': item.index > 0, 'vh-1px-b': item.index === currentList.length - 1}"
        >
            <view class="touch-left" v-if="isMoveAble">
                <view class="ico_drag"></view>
            </view>

            <view class="drag-sort-list move-box" @click="goSingerInfoPage(itemIndex)">
                <view class="signer-info-list__info">
                    <text class="signer-info-list__info__unnecessary">*</text>
                    <text :class="item.userType === 'PERSON' ? 'icon-ic_personal_avatar' : 'icon-ic_company_avatar'"></text>
                    {{ getShowName(item) }}
                </view>
                <view v-if="hasModifyReceiverRight" @click.stop="deleteSigner(itemIndex)">
                    <text class="icon-ic_delete"></text>
                </view>
            </view>
        </movable-view>
        <movable-view
            class="touch"
            :x="0"
            @touchstart="touchstart"
            @touchmove="touchmove"
            @touchend="touchend"
        ></movable-view>
    </movable-area>
</template>

<script>
import { mapState } from 'vuex';
import { getUserNameShow } from 'utils/send';

export default {
    name: 'SignerList',
    props: {
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        isMoveAble: {
            type: Boolean,
            default: () => {
                return false;
            },
        },
        hasModifyReceiverRight: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            currentList: [],
            height: 60, // 列表信息每行的高度
            active: -1, // 当前激活的item
            index: 0, // 当前激活的item的原index
            topY: 0, // 距离顶部的距离
            deviationY: 0, // 偏移量
        };
    },
    computed: {
        ...mapState('send', ['sendType']),
        moveHeight: function() {
            return this.currentList.length * this.height + 'px';
        },
        PaddingLeft: function() {
            return this.isMoveAble ? '15px' : '20px';
        },
        /* 获取签约主体的展示名 */
        getShowName() {
            return (item) => {
                const userInfo = this.sendType !== 'localSend' ? item.userInfo : item;
                return getUserNameShow(userInfo, item.roleName, item.userType) || `${userInfo?.userAccount}(${item.userType === 'PERSON' ? '个人账号' : '企业账号'})`;
            };
        },

    },
    watch: {
        list() {
            this.onUpdateCurrentList();
        },
    },
    onReady() {
        this.onUpdateCurrentList();
    },

    methods: {
        deleteSigner(index) {
            this.$emit('delete', index);
        },
        onUpdateCurrentList() {
            const arr = [];

            for (const key in this.list) {
                arr.push({
                    ...this.list[key],
                    index: Number(key),
                    y: key * this.height,
                    animation: false,
                });
            }
            this.currentList = arr;
        },
        touchstart(e) {
            if (!this.isMoveAble || !this.hasModifyReceiverRight) {
                return false;
            }
            // 计算y轴点击位置
            var query = uni.createSelectorQuery().in(this);
            query.select('#drag').boundingClientRect();
            query.exec((res) => {
                this.topY = res[0].top;
                const touchY = e.mp.touches[0].clientY - res[0].top;
                this.deviationY = touchY % this.height;

                for (const key in this.currentList) {
                    if ((this.currentList[key].index * this.height < touchY) && ((this.currentList[key].index + 1) * this.height > touchY)) {
                        this.active = key;
                        this.index = this.currentList[key].index;
                        break;
                    }
                }
            });
        },
        touchmove(e) {
            if (!this.isMoveAble || !this.hasModifyReceiverRight) {
                return false;
            }
            if (this.active < 0) {
                return;
            }
            const touchY = (e.mp.touches[0].clientY - this.topY) - this.deviationY;
            this.currentList[this.active].y = touchY;
            this.currentList[this.active].animation = false;
            for (const key in this.currentList) {
                // 跳过当前操作的item
                if (this.currentList[key].index !== this.currentList[this.active].index) {
                    if (this.currentList[key].index > this.currentList[this.active].index) {
                        if (touchY > this.currentList[key].index * this.height - this.height / 2) {
                            this.currentList[this.active].index = this.currentList[key].index;
                            this.currentList[key].index = this.currentList[key].index - 1;
                            this.currentList[key].y = this.currentList[key].index * this.height;
                            break;
                        }
                    } else {
                        if (touchY < this.currentList[key].index * this.height + this.height / 2) {
                            this.currentList[this.active].index = this.currentList[key].index;
                            this.currentList[key].index = this.currentList[key].index + 1;
                            this.currentList[key].y = this.currentList[key].index * this.height;
                            break;
                        }
                    }
                }
            }
        },
        touchend() {
            if (!this.isMoveAble || !this.hasModifyReceiverRight) {
                return false;
            }

            /* 这里index代表了拖拽后的顺序,要排下序，要不然展示会有问题。展示的顺序是按照数组数据来渲染的*/

            const list = this.currentList;
            list.map((item) => {
                item.routeOrder = item.index + 1;
                return item;
            });
            this.$emit('change', {
                data: list,
            });
            this.currentList[this.active].animation = true;
            this.currentList[this.active].y = this.currentList[this.active].index * this.height;
            this.active = -1;
        },
        goSingerInfoPage(index) {
            uni.navigateTo({
                url: `/subSendViews/addReceiver/index?receiverIndex=${index}`,
            });
        },
    },
};
</script>

<style lang="scss">
    @mixin setTopLine {
        content: " ";
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        height: 1upx;
        border-top: 1upx solid #eee;
        color: #eee;
        transform-origin: 0 0;
    }
    @mixin setBottomLine {
        content: " ";
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        height: 1upx;
        border-bottom: 1upx solid #eee;
        color: #eee;
        transform-origin: 0 100%;
    }

    .vh-1px-t {
        position: relative;
        &:before {
            @include setTopLine;
        }
    }
    .vh-1px-b {
        position: relative;
        &:after {
            @include setBottomLine;
        }
    }
    .drag-sort {
        width: 100%;
        .icon-ic_delete {
            color: #999;
        }
        .drag-sort-item {
            position: absolute !important;
            display: flex;
            align-items: center;
            width: 100%;
            padding: 0;
            margin: 0;
            background: #fff;
            box-sizing: border-box;
            .item {
                flex: 1;
            }
            .touch-left {
                width: 44upx;
                display: flex;
                padding-left: 15px;
                justify-content: center;
            }
        }
        .drag-sort-list {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding:0 16upx;
        }
        .move-box{
            flex:1;
        }
        .touch {
            height: 100%;
            width: 100upx;
        }
        .ico_drag {
            display: inline-block;
            width: 35upx;
            height: 20upx;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAYCAYAAAC8/X7cAAAAAXNSR0IArs4c6QAAAEtJREFUWAnt1cEJACAMA0B1/506moIr5FEK51+Jl0d2Vd01+JzB2X90H5jeoPwECBDIBLYlzgDj25Y4JvQAAQIERgtY4u76LHF3Aw8rGQnK3sYAXQAAAABJRU5ErkJggg==) 0 0 no-repeat;
            background-size: 100% auto;
        }
        .active {
            box-shadow: 0 0 40upx #DDDDDD;
            z-index: 99;
        }
        .signer-info-list__info {
            display: flex;
            flex-direction: row;
            align-items: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text {
                margin-right: 20upx;
                color: $--color-text-placeholder;
                font-size: 50upx;
            }
            &__unnecessary {
                margin-right: 6upx;
                visibility: hidden;
                font-size: 0upx !important;
            }
        }
    }
</style>
