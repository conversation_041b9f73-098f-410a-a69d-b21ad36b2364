<template>
    <view class="template-doc">
        <view v-if="templateDocList.length">
            <view class="template-doc__tip"><text class="template-doc__unnecessary">*</text>共 {{ templateDocList.length }} 份文档</view>
            <view class="template-doc__wrapper">
                <view class="template-doc__wrapper_header">合同描述信息</view>
                <view class="template-doc__wrapper_btn" v-if="showDeleteDocBtn" @click="handelDelete">删除合同</view>
            </view>
            <view class="template-doc__upload" v-if="showBlankUploadBtn">
                <view><text class="template-doc__unnecessary">*</text>合同文件</view>
                <SelectUploadType
                    :uploadUrl="uploadUrl"
                    :maxUploadNum="1"
                    @uploadResponse="onUploadDocSuccess"
                ></SelectUploadType>
            </view>
            <TemplateDocFields
                v-for="item in orderedDescribeFields"
                :key="item.describeFieldId"
                :fieldName="item.fieldName"
                :type="item.bizFieldType"
                :necessary="item.necessary"
                :options="item.buttons || []"
                :value="item.fieldValue"
                :isLocked="item.fieldLocking === 'LOCKED'"
                @change="onDesFieldChange(item.index, 'fieldValue', $event)"
            >
            </TemplateDocFields>
            <template v-if="contentFieldsPlain && contentFieldsPlain.length > 0">
                <view class="template-doc__wrapper">
                    <view class="template-doc__wrapper_header">合同内容字段</view>
                </view>
                <TemplateDocFields
                    v-for="item in contentFieldsPlain"
                    :key="item.labelId"
                    :fieldName="item.labelName"
                    :type="item.labelType"
                    :value="item.labelValue"
                    :options="item.items || []"
                    :necessary="item.necessary"
                    @change="onContentFieldChange(item.vForKey, $event)"
                >
                </TemplateDocFields>
            </template>
        </view>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
import TemplateDocFields from './templateDocFields';
import SelectUploadType from 'components/selectUploadType';
export default {
    components: {
        TemplateDocFields,
        SelectUploadType,
    },
    props: {
        curDocIndex: {
            type: Number,
            default: 0,
        },
        draftId: {
            type: String,
            default: '',
        },
    },
    computed: {
        ...mapState('template', ['templateDocList', 'templatePermissions']),
        ...mapState('send', ['sendType']),
        showDeleteDocBtn() {
            return this.sendType === 'localSend' || (this.sendType === 'templateSend' && this.templatePermissions.modifyDocument);
        },
        showBlankUploadBtn() {
            return this.templateDocList[this.curDocIndex]?.documentType === 'BLANK';
        },
        orderedDescribeFields() {
            return (this.templateDocList[this.curDocIndex]?.descriptionFieldConfigs || []).map((item, index) => {
                item.index = index;
                return item;
            }).sort((a, b) => a.fieldOrder - b.fieldOrder);
        },
        contentFieldsPlain() {
            const _contentFields = (this.templateDocList[this.curDocIndex]?.contentFieldLabels || []).map((item, index) => {
                item.vForKey = index;
                return item;
            });
            return _contentFields.filter(item => !['TERM', 'DYNAMIC_TABLE'].includes(item.labelType));
        },

        uploadUrl() {
            const docId = this.templateDocList[this.curDocIndex]?.documentId;
            return `/template-api/v2/draft/${this.draftId}/document/${docId}/replace-blank-document?draftId=${this.draftId}&draftDocumentId=${docId}` || '';
        },
    },
    methods: {
        ...mapMutations('template', ['updateDescFieldValByIndex', 'updateContentFieldValByIndex', 'updateTemplateDocListByIndex', 'setTemplateDocList']),
        ...mapActions('template', ['computeFieldsHasComplete']),
        onDesFieldChange(index, type, e) {
            this.updateDescFieldValByIndex({
                contractIndex: this.curDocIndex,
                fieldIndex: index,
                val: e.detail.value,
                type: type,
            });
            this.computeFieldsHasComplete(this.curDocIndex);
        },
        onContentFieldChange(index, e) {
            this.updateContentFieldValByIndex({
                contractIndex: this.curDocIndex,
                fieldIndex: index,
                val: e.detail.value,
            });
            this.computeFieldsHasComplete(this.curDocIndex);
        },
        handelDelete() {
            this.$emit('deleteDoc');
        },
        onUploadDocSuccess(uploadedData) {
            const { descriptionFieldConfigs } = this.templateDocList[this.curDocIndex];
            this.updateTemplateDocListByIndex({
                index: this.curDocIndex,
                data: {
                    ...this.templateDocList[this.curDocIndex],
                    ...uploadedData,
                    // 替换id
                    descriptionFieldConfigs: descriptionFieldConfigs.map((item, index) => {
                        item.describeFieldId = uploadedData.descriptionFieldConfigs[index].describeFieldId;
                        if (item.fieldName === 'contractTitle') {
                            item.fieldValue = uploadedData.descriptionFieldConfigs[index].fieldValue;
                        }
                        return item;
                    }),
                },
            });
            this.setTemplateDocList(this.templateDocList);
        },
    },
};
</script>

<style lang="scss">
.template-doc {
    height: calc(100% - 410rpx);
    overflow-y: scroll;
    background-color: $--color-white;
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
    &__title {
        background-color: $--background-color-base;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 30rpx;
        font-size: 30rpx;
        margin-bottom: -10rpx;
        color: $--color-text-primary;
    }
    &__tip {
        padding: 0 30rpx;
    }
    &__wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background-color: $--background-color-base;
        height: 84rpx;
        line-height: 84rpx;
        padding: 0 30rpx;
        font-size: 24rpx;
        &_btn {
            color: $--color-primary;
        }
        &_header {
            color: $--color-info;
        }
    }
    &__upload {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        color: $--color-text-primary;
        border-bottom: 1rpx solid $--border-color-lighter;
        box-sizing: border-box;
    }
    &__pic {
        margin-bottom: 30rpx;
    }
}
</style>
