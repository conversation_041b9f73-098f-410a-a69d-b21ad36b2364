<template>
    <view class="select-popup">
        <view class="select-popup__picker" @click="popUpOpen">
            <view>
                <text :class="selectedValueText ? '' : 'select-popup__placeholder'">{{ selectedValueText || placeholder
                }}</text>
            </view>
        </view>
        <uni-popup ref="selectPopUp" type="bottom" @change="popVisibleChange">
            <!--单选-->
            <view v-if="selectType==='single'"
                class="select-popup__wrapper"
                :class="{'margin-fix' : isIphoneX}"
            >
                <view class="select-popup__wrapper_list"
                    v-for="(item, index) in selectOptions"
                    :key="index"
                    @click="handleSelect(item)"
                >
                    {{ item }}
                </view>
                <view class="select-popup__wrapper_cancel">
                    <view class="select-popup__wrapper_btn" @click="popupCancel">取消</view>
                </view>
            </view>

            <!--复选-->
            <view v-else class="select-popup__wrapper" :class="{'margin-fix' : isIphoneX}">
                <checkbox-group @change="handleMulSelect" class="select-popup__wrapper_group">
                    <label
                        v-for="(item, index) in selectOptions"
                        :key="index"
                    >
                        <checkbox
                            :value="item"
                            :checked="selectedOptions.includes(item)"
                            color="#127FD2"
                        />
                        <view>{{ item }}</view>
                    </label>
                </checkbox-group>
                <view class="select-popup__wrapper_cancel">
                    <view class="select-popup__wrapper_btn" @click="popupCancel">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import { uniPopup } from '@dcloudio/uni-ui';
import { mapState } from 'vuex';
export default {
    components: {
        uniPopup,
    },
    props: {
        // eslint-disable-next-line vue/require-default-prop
        selectedValue: null,
        selectOptions: {
            type: Array,
            default: () => [],
        },
        selectType: {
            type: String,
            default: 'multiple',  // 'single'
        },
        necessary: {
            type: Boolean,
            default: false,
        },
        placeholder: {
            type: String,
            default: '请输入',
        },
    },
    data() {
        return {
            selectedOptions: [],
        };
    },
    computed: {
        ...mapState('send', ['isIphoneX']),
        selectedValueText() {
            if (this.selectType === 'multiple') {
                return this.selectedOptions.join(',');
            }
            return this.selectedValue;
        },
    },
    methods: {
        popVisibleChange(e) {
            this.$emit('popVisibleChange', e.show);
        },
        popUpOpen() {
            this.$refs.selectPopUp.open();
        },
        popupCancel() {
            this.$refs.selectPopUp.close();
        },
        handleSelect(item) {
            this.$emit('change', { detail: { value: item } });
            this.popupCancel();
        },
        handleMulSelect(e) {
            this.selectedOptions = e.detail.value;
            this.$emit('change', e);
        },
    },
};
</script>
<style lang="scss">
.select-popup {
    &__wrapper {
        background-color: $--color-white;
        overflow-y: scroll;
        &_list {
            height: 100rpx;
            line-height: 100rpx;
            color: $--color-text-primary;
            text-align: center;
            border-top: 1rpx solid $--border-color-lighter;
        }
        &_cancel {
            background-color: $--border-color-extra-light;
            padding-top: 16rpx;
        }
        &_btn {
            background-color: $--color-white;
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
        }
        &_group {
            width: 100%;
            height: 100rpx;
            line-height: 100rpx;
            label {
                display: flex;
                border-bottom: 1rpx solid $--border-color-lighter;
            }
        }

    }
    .margin-fix {
        margin-bottom: -70rpx;
    }
    &__placeholder {
        font-size: 14px;
        color: $--color-text-placeholder;
    }
    &__picker {
        font-size: 16px;
    }
}

</style>
