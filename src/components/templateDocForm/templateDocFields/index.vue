<template>
    <view class="doc-fields">
        <view v-if="fieldName === 'contractTitle'" class="doc-fields__item">
            <view><text :class="necessary ? 'doc-fields__necessary' : 'doc-fields__unnecessary'">*</text>合同名称</view>
            <view>
                <input
                    :placeholder-class="'doc-fields__placeholder'"
                    :placeholder="'请输入'"
                    v-model="modelValue"
                    @input="handleChange(fieldName, $event)"
                />
            </view>
        </view>
        <view v-else-if="['contractTypeIdsForApprove', 'contractTypeId'].includes(fieldName)" class="doc-fields__item">
            <view v-if="['contractTypeId'].includes(fieldName)"><text class="doc-fields__unnecessary">*</text>合同类型</view>
            <view v-if="['contractTypeIdsForApprove'].includes(fieldName)"><text class="doc-fields__unnecessary">*</text>合同类型（备用）</view>
            <view>
                <picker
                    range-key="folderName"
                    :range="contractTypes"
                    :value="modelValue"
                    :disabled="isLocked"
                    @change="handleChange(fieldName, $event)"
                >
                    <view>
                        <text :class="contractTypeName(modelValue) ? '' : 'doc-fields__placeholder'">{{ contractTypeName(modelValue) || '请选择' }}</text>
                    </view>
                </picker>
            </view>
        </view>
        <view v-else-if="fieldName === 'signExpireDays'" class="doc-fields__item">
            <view><text class="doc-fields__unnecessary">*</text>签约截止时间</view>
            <view>
                <picker
                    mode="date"
                    :value="signDate"
                    :start="startDate"
                    :end="endDate"
                    :disabled="isLocked"
                    @change="handleChange(fieldName, $event)"
                >
                    {{ signDate }}
                </picker>
            </view>
        </view>
        <view v-else-if="fieldName === 'contractContentExpireDays'" class="doc-fields__item">
            <view><text :class="necessary ? 'doc-fields__necessary' : 'doc-fields__unnecessary'">*</text>合同到期日</view>
            <view>
                <picker
                    mode="date"
                    :value="signDate"
                    :start="startDate"
                    :disabled="isLocked"
                    @change="handleChange(fieldName, $event)"
                >
                    <view>
                        <text :class="signDate? '' : 'doc-fields__placeholder'">{{ signDate || '请选择' }}</text>
                    </view>
                </picker>
            </view>
        </view>
        <view v-else-if="fieldName === 'customNumber'" class="doc-fields__item">
            <view><text :class="necessary ? 'doc-fields__necessary' : 'doc-fields__unnecessary'">*</text>合同内部编号</view>
            <view>
                <input
                    v-model="modelValue"
                    :placeholder-class="'doc-fields__placeholder'"
                    :placeholder="'请输入'"
                    :maxlength="100"
                    :disabled="isLocked"
                    @input="handleChange(fieldName, $event)"
                />
            </view>
        </view>
        <view v-else class="doc-fields__item">
            <view><text :class="necessary ? 'doc-fields__necessary' : 'doc-fields__unnecessary'">*</text>{{ fieldName }}</view>
            <view v-if="type === 'TEXT' || type === 'NUMERIC_VALUE'">
                <input
                    v-model="modelValue"
                    :placeholder-class="'doc-fields__placeholder'"
                    :placeholder="'请输入'"
                    :disabled="isLocked"
                    :maxlength="800"
                    :type="type === 'NUMERIC_VALUE' ? 'digit' : 'text'"
                    @input="handleChange(type, $event)"
                />
            </view>

            <view v-else-if="type === 'BIZ_DATE' || type === 'BIZDATE'">
                <picker
                    mode="date"
                    v-model="modelValue"
                    :disabled="isLocked"
                    @change="handleChange(type, $event)"
                >
                    <view>
                        <text :class="modelValue? '' : 'doc-fields__placeholder'">{{ modelValue || '请选择' }}</text>
                    </view>
                </picker>

            </view>
            <!-- 'CHECKBOX' || 'SINGLE_BOX' || 'RADIOBUTTON' || 'COMBOBOX' -->
            <view v-else-if="['CHECKBOX','SINGLE_BOX','RADIOBUTTON','COMBOBOX'].includes(type)">
                <SingleOrMulSelect
                    v-if="type === 'CHECKBOX'"
                    :selectedValue="modelValue"
                    :selectOptions="options"
                    :selectType="type === 'CHECKBOX' ? 'multiple' : 'single'"
                    :necessary="necessary"
                    @change="handleChange(type, $event)"
                ></SingleOrMulSelect>

                <picker
                    v-else
                    :value="selectedIndex"
                    :range="options"
                    @change="handleChange(type, $event)"
                >
                    <span v-if="options[selectedIndex]">{{ options[selectedIndex] }}</span>
                    <span v-else class="doc-fields__placeholder">{{ '请选择' }}</span>
                </picker>
            </view>
            <view v-else-if="type === 'PICTURE'">
                <TemplatePictureFields
                    :fieldName="fieldName"
                    :fieldValue="value"
                    :necessary="necessary"
                    @change="handleChange(type, $event)"
                ></TemplatePictureFields>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import SingleOrMulSelect from 'components/templateDocForm/singleOrMulSelect';
import TemplatePictureFields from 'components/templateDocForm/templatePictureFields';

export default {
    components: {
        SingleOrMulSelect,
        TemplatePictureFields,
    },
    props: {
        fieldName: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: '',
        },
        necessary: {
            default: null,
            type: Boolean,
        },
        options: {
            type: Array,
            default: () => [],
        },
        isLocked: {
            type: Boolean,
            default: false,
        },
        // eslint-disable-next-line vue/require-default-prop
        value: null,
    },
    data() {
        return {
            startDate: '',
            endDate: '',
            contractDate: '', // 合同到期时间
        };
    },
    computed: {
        ...mapState('template', ['contractTypes']),
        ...mapState('send', ['isIphoneX']),
        modelValue: {
            set(value) {
                const val =  this.isContractType ? this.contractTypes[value]?.folderId : value;
                this.$emit('input', val);
            },
            get() {
                // 初始进来合同类型中modelValue为folderId（位数大于4，1000或者"3100647414256984068"），后续change时picker默认绑定的value为选择的index
                const val = (this.isContractType && this.value.length < 4) ? this.contractTypes[this.value]?.folderId : this.value;
                return val;
            },
        },
        selectedIndex: {
            set(value) {
                const val =  this.options[value];
                this.$emit('input', val);
            },
            get() {
                const index = (this.options || []).indexOf(this.modelValue);
                return index;
            },
        },
        signDate() {
            return this.value && this.getDate({ time: +this.value }) || '';
        },
        isContractType() {
            return ['contractTypeIdsForApprove', 'contractTypeId'].includes(this.fieldName);
        },
        contractTypeName() {
            return (modelValue) => {
                return (this.contractTypes || []).find(item => item.folderId === modelValue)?.folderName || '';
            };
        },
    },
    methods: {
        getContractTypeFolderId(index) {
            return this.contractTypes[index]?.folderId;
        },
        validateData(fieldName, e) {
            switch (fieldName) {
                case 'contractTitle':
                    if (/【|\[|】|\]/.test(e.detail.value)) {
                        return this.$toast.error('合同名称请不要包含特殊字符');
                    }
            }
            return true;
        },
        formatData(fieldName, e) {
            let res = e;
            switch (fieldName) {
                case 'contractTypeIdsForApprove':
                case 'contractTypeId':
                    res =  { detail: { value: this.getContractTypeFolderId(e.detail.value) } };
                    break;
                case 'signExpireDays':
                case 'contractContentExpireDays':
                    res = { detail: { value: new Date(e.detail.value).getTime() } };
                    break;
                case 'SINGLE_BOX':
                case 'RADIOBUTTON':
                case 'COMBOBOX':
                    res = { detail: { value: this.options[e.detail.value] } };
                    break;
            }
            return res;
        },
        handleChange(fieldName, e) {
            console.log('handleChange', fieldName, e);
            if (!this.validateData(fieldName, e)) {
                return;
            }
            const res = this.formatData(fieldName, e);
            this.$emit('change', res);
        },
        getDate({ addYear = 0, time = '' } = {}) {
            const date = time ? new Date(time) : new Date();
            const year = date.getFullYear() + addYear;
            let  month = date.getMonth() + 1;
            let day = date.getDate();

            month = month > 9 ? month : '0' + month;
            day = day > 9 ? day : '0' + day;
            return `${year}-${month}-${day}`;
        },
        initSignDate() {
            this.startDate = this.getDate();
            this.endDate = this.getDate({ addYear: 1 });
        },
    },
    created() {
        this.initSignDate();
    },
};
</script>

<style lang="scss">
.doc-fields {
    &__item {
        padding: 0 30rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 100rpx;
        line-height: 100rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        color: $--color-text-primary;
        width: 100%;
        box-sizing: border-box;
        input {
            height: 100rpx;
            line-height: 100rpx;
            text-align: right;
        }
        &>view:nth-child(1) {
            width: 350rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &>view:nth-child(2) {
            width: calc(100% - 350rpx);
            text-align: right;
        }
    }
    &__placeholder {
        color: $--color-text-placeholder;
    }
    &__necessary {
        color: red;
        margin-right: 6rpx;
    }
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
}
</style>
