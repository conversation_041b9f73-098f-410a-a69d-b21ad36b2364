<template>
    <view class="pic-label-upload">
        <SelectUploadType
            :uploadUrl="picLabelUploadUrl"
            :maxUploadNum="1"
            :uploadFileType="'image'"
            ref="selectUploadType"
            :showDefaultUploadBtn="!fileId"
            :changeBtnHeight="true"
            :changeBtnRight="true"
            @uploadResponse="onUploadSuccess"
            @uploadFileList="handleUploadFileList"
        >
            <image
                v-if="picLabelUploadedImg"
                :src="`${host}${picLabelUploadedImg}?access_token=${accessToken}`"
                class="pic-label-upload_img"
                @click="handleViewFile(item)"
            />
            <text v-if="picLabelUploadedImg" class="icon-ic_close" @click="handleDeleteImg"></text>
        </SelectUploadType>
    </view>
</template>
<script>
import { mapState, mapMutations } from 'vuex';
import SelectUploadType from 'components/selectUploadType';
export default {
    components: {
        SelectUploadType,
    },
    props: {
        necessary: {
            type: Boolean,
            default: false,
        },
        fieldName: {
            type: String,
            default: '',
        },
        fieldValue: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            host: this.$http.baseURL,
            accessToken: uni.getStorageSync('accessToken'),
            fileList: [],
            fileId: this.fieldValue,
        };
    },
    computed: {
        ...mapState('send', ['contractId']),
        ...mapState('template', ['pictureFieldsPreviewUrls']),
        picLabelUploadUrl() {
            return `/template-api/v2/draft/${this.contractId}/upload-picture`;
        },
        picLabelUploadedImg() {
            return this.fileId ? `/template-api/v2/draft/${this.contractId}/picture-label/preview/${this.fileId}` : '';
        },
    },
    methods: {
        ...mapMutations('template', ['setPictureFieldsPreviewUrls']),
        onUploadSuccess(id) {
            this.fileId = id;
            this.$emit('change', { detail: { value: id } });
        },
        handleDeleteImg() {
            this.fileId = '';
            this.$emit('change', { detail: { value: '' } });
        },
        handleUploadFileList(fileList) {
            this.setPictureFieldsPreviewUrls({ fileId: this.fileId, tempUrl: fileList[0].tempUrl });
        },
        handleViewFile() {
            uni.previewImage({
                urls: [this.pictureFieldsPreviewUrls[this.fileId]],
            });
        },
    },
};
</script>
<style lang="scss">
 .pic-label-upload {
    image {
        width: 130rpx;
        height: 80rpx;
        margin: 10rpx 20rpx;
    }
    .icon-ic_close {
        color: $--color-text-secondary;
        font-size: 20rpx;
        position: relative;
        bottom: 40rpx;
    }
}
</style>
