<template>
    <view class="template-docList">
        <template v-if="showAddDocBtn">
            <SelectUploadType
                ref="selectUploadType"
                :uploadUrl="uploadLocalFileRequestUrl"
                :maxUploadNum="1"
                :showDefaultUploadBtn="false"
                @uploadResponse="onUploadDocSuccess"
                @uploadFileList="handleUploadFileList"
            >
                <view class="template-docList__add-btn" @click="uploadContractFile">
                    <view>+</view>
                    <view>添加文档</view>
                </view>
            </SelectUploadType>
        </template>
        <template
            v-else-if="!showAddDocBtn && !templateDocList.length"
        >
            <view class="template-docList__add-tip"> 请联系管理员分配模板的调整文档权限</view>
        </template>
        <scroll-view
            v-if="templateDocList.length"
            class="template-docList__scroll-view"
            :scroll-x="true"
            :enable-flex="true"
            :scroll-left="scrollLeft"
            :style="{'width': `calc(100% - ${showAddDocBtn ? '230rpx' : '0rpx'})`, 'margin-left': `${showAddDocBtn ? '230rpx' : 0}`}"
        >
            <view v-for="(item, index) in templateDocList" :key="index" class="template-docList__scroll-view-list">
                <view
                    v-if="item.documentType ==='BLANK'"
                    class="template-docList__scroll-view-list-blank"
                    :class="curDocIndex === index ? 'active' : ''"
                    @click="handleSelectDoc(index)"
                >
                    空白文档
                </view>
                <img
                    v-else-if="item.documentPreviews[0].imagePreviewUrl"
                    :src="`${host}${item.documentPreviews[0].imagePreviewUrl}?access_token=${accessToken}`"
                    :class="curDocIndex === index ? 'active':''"
                    @click="handleSelectDoc(index)"
                />
                <view class="template-docList__scroll-view-list-tip" :class="fieldHasComplete(item.documentId) ? 'template-docList__scroll-view-list-green' : 'template-docList__scroll-view-list-blue'">{{ fieldHasComplete(item.documentId) ? '已完成':'待填写' }}</view>
                <text v-if="item.documentType !=='BLANK' && item.documentPreviews[0].imagePreviewUrl" class="template-docList__scroll-view-list-btn" @click="handlePreview(item)">点击预览</text>
                <text class="template-docList__scroll-view-list-name">{{ contractTitle(item) }}</text>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import { getDraftPreviewInfo } from 'api/template';
import SelectUploadType from 'components/selectUploadType';
import { mapState } from 'vuex';
export default {
    components: {
        SelectUploadType,
    },
    props: {
        draftId: {
            type: String,
            default: '',
        },
        curDocIndex: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            docImgList: [],
            host: this.$http.baseURL,
            accessToken: uni.getStorageSync('accessToken'),
            scrollLeft: 0,
        };
    },
    computed: {
        ...mapState('template', ['templateDocList', 'templatePermissions', 'ifDynamicTemplate']),
        ...mapState('send', ['sendType']),
        showAddDocBtn() {
            return this.sendType === 'localSend' || (this.sendType === 'templateSend' && this.templatePermissions.modifyDocument && !this.ifDynamicTemplate);
        },
        fieldHasComplete() {
            return (documentId) => {
                const doc = this.templateDocList.find(item => item.documentId === documentId) || {};
                return (doc.fieldHasComplete && doc.documentType !== 'BLANK') || false;
            };
        },
        contractTitle() {
            return (item) => {
                return (item?.descriptionFieldConfigs || []).find(item => item.fieldName === 'contractTitle')?.fieldValue;
            };
        },
        uploadLocalFileRequestUrl() {
            return `/template-api/v2/draft/${this.draftId}/document?draftId=${this.draftId}`;
        },
    },
    watch: {
        curDocIndex(val) {
            if (val === (this.templateDocList.length - 1)) {
                this.scrollLeft += 115;
            }
        },
    },
    methods: {
        handlePreview(item) {
            getDraftPreviewInfo({ draftId: this.draftId, documentId: item.documentId })
                .then((res) => {
                    const imgList =  (res.data.imageUrls || []).map(item => `${this.host}${item.imagePreviewUrl}?access_token=${this.accessToken}`);
                    if (!imgList.length) {
                        return;
                    }
                    uni.previewImage({
                        urls: imgList,
                    });
                });
        },
        handleSelectDoc(index) {
            this.$emit('changeCurDocIndex', index);
        },
        uploadContractFile() {
            this.$refs.selectUploadType.popUpOpen();
        },
        onUploadDocSuccess(data) {
            this.$emit('uploadFileSuccess', data);
        },
        handleUploadFileList(fileList) {
            this.$emit('uploadFileList', fileList);
        },
    },
};
</script>

<style lang="scss">
.template-docList {
    display: flex;
    border-top: 1rpx solid $--border-color-lighter;
    background-color: $--color-white;
    &__add-btn {
        position: fixed;
        width: 200rpx;
        height: 280rpx;
        margin: 40rpx 0 40rpx 30rpx;
        border: 1rpx dashed $--border-color-base;
        text-align: center;
        color: $--color-text-placeholder;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    &__add-tip {
        margin: 20rpx;
        color: $--border-color-base;
    }
    &__scroll-view {
        height: 420rpx;
        background-color: $--color-white;
        padding: 40rpx 30rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        &-list {
            width: 230rpx;
            position: relative;
            image, &-blank {
                width: 200rpx;
                height: 280rpx;
                margin-right: 30rpx;
                border: 1rpx solid $--border-color-base;
                text-align: center;
                line-height: 280rpx;
                color: $--color-text-placeholder;
            }
            .active {
                border: 2rpx solid $--color-primary;
                box-shadow: 2px 0 4px 0 rgba(18,127,210,.1);
            }
            text {
                color: $--color-text-primary;
                font-size: 24rpx;
            }
            &-btn {
                position: absolute;
                top: 222rpx;
                left: 1rpx;
                width: 202rpx;
                height: 60rpx;
                background-color: $--color-black;
                opacity: 0.5;
                text-align: center;
                line-height: 60rpx;
                color: $--color-white !important;
            }
            &-tip {
                position: absolute;
                top: 0;
                right: 28rpx;
                color: $--color-white;
                font-size: 20rpx;
                width: 80rpx;
                height: 40rpx;
                line-height: 40rpx;
                text-align: center;
                opacity: 0.9;
            }
            &-name {
                display: inline-block;
                width: 200rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            &-blue {
                background-color: $--color-primary;
            }
            &-green {
                background-color: $--color-success;
            }
        }
    }
}
</style>
