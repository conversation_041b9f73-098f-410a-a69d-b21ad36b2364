<template>
    <view class="template-list__wrapper">
        <!-- 筛选模板的下拉列表选择框 -->
        <uni-data-select
            v-model="value"
            class="template-list__wrapper-select"
            :localdata="createEntOptions"
            placeholder="请选择模板的创建企业"
            @change="searchEntTemplate"
        ></uni-data-select>
        <!-- 有模板 -->
        <template v-if="templateList.length && !loading">
            <scroll-view class="template-list" :scroll-y="true" @scrolltolower="loadMore">
                <view class="template-list__item" v-for="item in templateList" :key="item.templateId" @click="toTemplateDetail(item)">
                    <view>
                        <text class="template-list__item-name">{{ item.templateName }}</text>
                        <text class="icon-ic_forward"></text>
                    </view>
                    <view>
                        <text>创建企业：</text>
                        <text class="template-list__item-name">{{ getEntBizName(item) }}</text>
                    </view>
                </view>
            </scroll-view>
        </template>
        <!-- 下拉搜索出来无模板 -->
        <template v-if="currentSelectedEntId && !templateList.length && !loading">
            <view class="template-list__tip">该企业暂无模板</view>
        </template>

        <!-- 企业有模板但当前账号没模板 -->
        <template v-if="!currentSelectedEntId && totalRecord && !templateList.length && !loading">
            <view class="template-list__tip">请联系企业管理人员为您分配模板</view>
        </template>

        <!-- 企业无模板 -->
        <view v-if="!totalRecord && !loading && canCreateTemplate" class="template-list__send">
            <view class="template-list__send-icon" @click="sendQuickly">
                <text class="icon-fasong"></text>
            </view>
            <view class="template-list__send-text" @click="sendQuickly">发送合同</view>
        </view>
    </view>

</template>

<script>
import _get from 'lodash/get';
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import { getTemplateList } from 'api/template.js';
import { switchEnt, getAllEntList, createDraftId, getCreateTemplatePermission } from 'api/send';
import log from 'src/utils/log.js';
export default {
    props: {
        shouldUpdate: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            totalPages: 0,
            totalRecord: 0, // 可用和不可用模板总数
            pageIndex: 1,
            pageSize: 50,
            emptySrc: require('img/empty.png'),
            allEntList: [],
            inJump: false, // 正在跳转中，防止快速重复点击模板
            canCreateTemplate: false,
            value: '', // 字段名受限于uniapp的组件，不可自定义
            createEntOptions: [],
            currentSelectedEntId: '',
        };
    },
    computed: {
        ...mapState(['loading']),
        ...mapState('template', ['templateList']),
        ...mapState('send', ['curEntId']),
        ...mapState('user', ['entList', 'commonHeaderInfo']),
        ...mapGetters('user', ['ifManager']),
    },
    watch: {
        shouldUpdate: {
            async handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.pageIndex = 1;
                    await this.initTemplateList({ type: 'init', createEntId: this.currentSelectedEntId });
                    if (this.templateList.length) {
                        this.getCreateEntList();
                    }
                }
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions(['setLoading']),
        ...mapActions('user', ['getFeatures', 'getHeadInfo']),
        ...mapMutations('template', ['setTemplateList', 'setIfDynamicTemplate']),
        ...mapMutations('send', ['setSendType', 'setCurEntIndex', 'setCurEntId']),
        handleNoTemplateSend() {
            const curEnt = this.allEntList.find(item => item.entId === this.curEntId);
            const groupId = curEnt.groupId;
            const entName = curEnt.entName;
            const ifGroup = !(!groupId || groupId === '0'); // 个人null 非集团企业'0'
            if (ifGroup) {
                log.info(JSON.stringify({ group: entName, account: this.commonHeaderInfo.platformUser.account }));
            } else if (this.ifManager) {
                log.info(JSON.stringify({ manager: entName, account: this.commonHeaderInfo.platformUser.account }));
            } else {
                log.info(JSON.stringify({ noManager: entName, account: this.commonHeaderInfo.platformUser.account }));
            }
            uni.showModal({
                title: '提示',
                confirmText: '我知道了',
                showCancel: false,
                content: '你的企业还没有创建合同模板，请进入电脑网页创建模板后重试',
                confirmColor: '#127FD2',
                success: () => {},
            });
        },
        loadMore() {
            if (this.pageIndex < this.totalPages && !this.loading) {
                this.pageIndex++;
                this.initTemplateList({ type: 'loadMore', createEntId: this.currentSelectedEntId });
            }
        },
        async getTotalRecord() {
            return getTemplateList({
                filterType: 'ALL_TEMPLATE',
                pageIndex: 1,
                pageSize: 1,
                templateCategory: 'ALL',
            });
        },
        async initTemplateList({ type, createEntId } = { type: 'init', createEntId: '' }) {
            try {
                this.setLoading(true);
                if (type === 'init' && !this.currentSelectedEntId) {
                    const { data: templateRes1 }  = await this.getTotalRecord();
                    this.totalRecord = _get(templateRes1, 'totalRecord', 0);
                }
                const { data: templateRes } = await getTemplateList({
                    filterType: 'MY_TEMPLATE',
                    pageIndex: type === 'init' ? 1 : this.pageIndex,
                    pageSize: 20,
                    templateCategory: 'ALL',
                    permissionType: 'sendContract',
                    createEntId,
                });
                this.setLoading(false);
                this.totalPages = _get(templateRes, 'totalPages', 0);
                const templateList = _get(templateRes, 'results', []);

                if (type === 'init') {
                    this.setTemplateList(templateList);
                } else {
                    this.setTemplateList(this.templateList.concat(templateList));
                }
            } catch (err) {
                this.setLoading(false);
            }
        },
        initAllEntList() {
            getAllEntList().then((res) => {
                this.allEntList = res.data || [];
            });
        },
        async checkIdentity(entList) {
            if (entList.length < 2) {
                // 如果权限只对应一个主体，以该主体的名义操作
                const targetEntId = entList[0];
                if (targetEntId !== this.curEntId) {
                    const { data } = await switchEnt(targetEntId);
                    uni.setStorageSync('accessToken', data.access_token);
                    uni.setStorageSync('refreshToken', data.refresh_token);
                    this.setCurEntIndex(this.entList.findIndex(item => item.entId === targetEntId));
                    this.setCurEntId(targetEntId);
                    this.$toast.none(`已为您切换至${this.allEntList.find(item => item.entId === targetEntId).entName}`);
                    await this.getFeatures();
                    await this.getHeadInfo(); // roleDetails 权限 和主体有关系，此处需重新请求head-info
                    return Promise.resolve(false);
                }
            } else if (!entList.includes(this.curEntId)) {
                // 如果权限对应多个主体，弹窗提示用户去切换主体
                this.$toast.none(`请前往账号管理切换至有权限的企业主体`);
                return Promise.resolve(false);
            }
            return Promise.resolve(true);
        },
        async toTemplateDetail(row) {
            const { templateName, templateId, templateRemark, templateCategory, approvalStatus, enable } = row;
            this.$sensors.track({
                eventName: 'Mp_ContractSendList_BtnClick',
                eventProperty: {
                    page_name: '模板发起',
                    icon_name: templateName,
                    template_id: templateId,
                    template_name: templateName,
                    template_comment: templateRemark,
                    template_type: templateCategory === 'STATIC' ? '静态模板' : '动态模板',
                    approval_status: ({ APPROVING: '审批中', REJECT: '驳回', NONE: '' })[approvalStatus],
                    enabled_status: enable ? '启用中' : '已关闭',
                },
            });
            if (this.inJump) {
                return;
            }
            this.inJump = true;
            this.setLoading(true);
            const noWait = await this.checkIdentity(row.templatePermission.operationFromSubject['usable']);
            setTimeout(() => {
                this.setSendType('templateSend');
                this.setIfDynamicTemplate(row.templateCategory !== 'STATIC');
                uni.navigateTo({
                    url: `/subSendViews/settingTemplateDoc/index?templateId=${row.templateId}`,
                });
                this.inJump = false;
            }, noWait ? 0 : 1000);
            this.setLoading(false);
        },
        sendQuickly() {
            // 后端给一个draftId，直接进入使用页面
            createDraftId()
                .then((res) => {
                    if (res.data?.draftId) {
                        this.setSendType('templateSend');
                        uni.navigateTo({
                            url: `/subSendViews/settingTemplateDoc/index?draftId=${res.data.draftId}`,
                        });
                    }
                });
        },
        checkCreateTemplateRight() {
            getCreateTemplatePermission(!!this.commonHeaderInfo.hybridServer)
                .then(() => {
                    this.canCreateTemplate = true;
                }).catch(() => {
                    this.canCreateTemplate = false;
                });
        },
        getCreateEntList() {
            this.createEntOptions = this.commonHeaderInfo.enterprises
                .filter((item) => item.entId !== '0')
                .map(({ entName, bizName, entId }) => ({
                    text: `${entName}${bizName ? '_' + bizName : ''}`,
                    value: entId,
                }));
        },
        searchEntTemplate(entId) {
            this.currentSelectedEntId = entId; // 清空选择时entId为''
            this.initTemplateList({ createEntId: this.currentSelectedEntId, type: 'init' });
        },
        getEntBizName({ enterpriseName, bizLineName }) {
            return `${enterpriseName}${bizLineName ? '_' + bizLineName : ''}`;
        },
    },
    created() {
        this.initAllEntList();
        this.checkCreateTemplateRight();
    },
};
</script>

<style lang="scss">
.template-list{
    height: calc(100vh - 180rpx);

    // #ifdef MP-WEIXIN
    height: calc(100vh - 300rpx);
    // #endif
    &__item {
        height: 100rpx;
        line-height: 50rpx;
        text-align: left;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        background-color: $--color-white;
        color: $--color-text-primary;
        display: flex;
        flex-direction: column;

        view:first-child {
            display: flex;
            justify-content: space-between;
        }
        view:last-child {
            font-size: 24rpx;
            color: $--color-info;
        }
        &-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    &__tip {
        color: $--color-text-secondary;
        margin-top: 10rpx;
    }
    &__btn, &__empty, .icon-ic_forward {
        color: $--border-color-base;
    }
    &__btn {
        display: inline-block;
        height: 100rpx;
        line-height: 100rpx;
    }
    &__wrapper {
        height: 100%;
        overflow-y: hidden;
        &-select {
            view {
                border-top: none !important;
            }
        }
    }
    &__send {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 200rpx;
        height: 100%;
        background-color: $--color-white;
        &-icon {
            width: 140rpx;
            height: 140rpx;
            line-height: 140rpx;
            border-radius: 70rpx;
            text-align: center;
            background-color: $--color-primary;
            margin-bottom: 20rpx;
            .icon-fasong {
                color: $--color-white;
                font-size: 60rpx;
            }
        }
        &-text {
            color: $--color-primary;
        }
    }

}
</style>
