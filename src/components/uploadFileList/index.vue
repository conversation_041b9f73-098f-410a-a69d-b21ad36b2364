<template>
    <view class="upload-file-list" v-if="uploadedFileList.length">
        <view
            :key="index"
            v-for="(item, index) in uploadedFileList"
            @click="handleViewFile(item)"
            class="upload-file-list__item"
        >
            <view class="upload-file-list__name">{{ item.name || imgShowName(item.tempUrl) }}</view>
            <view class="upload-file-list__delete" @click.stop="removeUploadedFileList(index)">
                <text class="icon-ic_close"></text>
            </view>
        </view>
    </view>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
export default {
    data() {
        return {

        };
    },
    computed: {
        ...mapState('send', ['uploadedFileList']),
        ...mapState(['isWeWork']),
    },
    methods: {
        ...mapMutations('send', ['removeUploadedFileList']),
        imgShowName(path = '') {
            return path.split('//')[1];
        },
        handleViewFile(file) {
            if (this.isWeWork) {
                return;
            }
            if (file.type) {
                const fileType = file.tempUrl.split('.')[1];
                uni.openDocument({
                    showMenu: true,
                    fileType: fileType,
                    filePath: file.tempUrl,
                    success() {},
                    fail() {
                        uni.showToast({
                            title: '该类型文件不支持预览',
                            icon: 'none',
                        });
                    },
                });
            } else {
                uni.previewImage({
                    urls: [file.tempUrl],
                });
            }
        },
    },
};
</script>

<style lang="scss">
.upload-file-list {
    background-color: $--color-white;
    &__item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        color: $--color-primary;
        height: 100rpx;
        line-height: 100rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        padding: 0 30rpx;
    }
    &__name {
        margin: 0 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &__delete {
        .icon-ic_close {
            color: $--color-text-secondary;
            font-size: 25rpx;
        }
    }
}
</style>
