
<template>
    <div>
        <web-view :src="url" @message="handleMessage"></web-view>
    </div>
</template>
<script>
import { payMixin } from 'mixins/pay';
export default {
    mixins: [payMixin],
    props: {
        url: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            ssqFaceQuery: '',
            webviewPath: '',
        };
    },
    methods: {
        async handleQuickPay(message) {
            const { accessToken, refreshToken, orderId, returnUrl } = message;
            uni.setStorageSync('accessToken', accessToken);
            uni.setStorageSync('refreshToken', refreshToken);
            const requestData = await this.getPaymentData(orderId);
            // #ifdef MP-ALIPAY
            uni.redirectTo({
                url: `/views/webviewRedirect/index?url=${encodeURIComponent(returnUrl)}`,
            });
            // #endif
            uni.requestPayment({
                ...requestData,
                success: (res) => {
                    // #ifdef MP-Alipay
                    if (res.resultCode !== '9000') {
                        return;
                    }
                    // #endif
                    uni.showToast({
                        title: '支付成功',
                        icon: 'success',
                        duration: 2000,
                    });
                },
                fail: (err) => {
                    console.log(err);
                },
            });
        },
        handleMessage(msgData) {
            console.log('webview message', msgData);
            let message;
            // #ifdef MP-WEIXIN
            message = msgData.target.data[0];
            // #endif
            // #ifdef MP-ALIPAY
            message = msgData.detail.data;
            // #endif
            // 消息类型 face： 需要跳转刷脸小程序
            const messageType = message.messageType;
            if (['hubbleRiskPay', 'signerPay'].includes(messageType)) {
                return this.handleQuickPay(message);
            }
            if (messageType === 'face') {
                // 刷脸小程序需要的参数
                const ssqFaceQuery = message.ssqFaceQuery;
                // 上上签页面对应的webview路径，需要主动跳回
                const webviewPath = message.ssqPageUrl;
                uni.showModal({
                    title: '提示',
                    content: '请点击“确定”跳到新的小程序完成刷脸',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateToMiniProgram({
                                appId: 'wx6dd34865858963a5',
                                path: `index/index?ssqFaceQuery=${ssqFaceQuery}`,
                                envVersion: 'release', // develop 开发板；trial 体验版；release 正式版
                                success: () => {
                                    uni.navigateTo({
                                        url: `/views/webviewRedirect/index?url=${encodeURIComponent(webviewPath)}`,
                                    });
                                    uni.showModal({
                                        title: '提示',
                                        content: '是否已经完成刷脸',
                                    });
                                },
                                fail: (err) => {
                                    console.log(err);
                                },
                            });
                        } else if (res.cancel) {
                            uni.navigateTo({
                                url: `/views/webviewRedirect/index?url=${encodeURIComponent(webviewPath)}`,
                            });
                        }
                    },
                });
            }
        },
    },
};
</script>
