<!--
component: 内容高度动态计算、带有吸底 的页面组件，适配iphoneX
props: bottom 吸底元素底部距离, 单位px，故使用屏幕尺寸375单位
slot: 默认slot为内容, slot footer为吸底元素
使用demo见：pages/send-contract/send-approve.vue
-->
<template>
    <view :class="allClassName" @touchmove.native="$emit('dragMove', $event)" @touchend.native="$emit('dragEnd', $event)">
        <CustomToast></CustomToast>
        <view class="xdpage-content" :style="{'height': `${contentHeight}px`}">
            <slot></slot>
        </view>
        <!-- 吸底元素 -->
        <view class="xdpage-footer" :style="{'padding-bottom': `${bottom}px`}">
            <slot name="footer"></slot>
        </view>
        <view>
            <slot name="float"></slot>
        </view>
    </view>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import CustomToast from '../customToast/index.vue';
export default {
    components: {
        CustomToast,
    },
    props: {
        bottom: {
            default: 0,
            type: Number,
        },
        className: {
            default: '',
            type: String,
        },
        noCalIphoneXBottom: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            allHeight: 0,
            footerHeight: 0,
        };
    },
    computed: {
        ...mapState('send', ['isIphoneX']),
        contentHeight() {
            const bottom = (this.isIphoneX && !this.noCalIphoneXBottom) ? uni.upx2px(68) : 0;
            return this.allHeight - this.footerHeight - bottom;
        },
        allClassName() {
            let str = `xdpage ${this.isIphoneX ? 'xdpage-iphonex' : ''}`;
            // #ifdef MP-ALIPAY
            str += ` ${this.className}`;
            // #endif
            return str;
        },
    },
    methods: {
        ...mapMutations('send', ['setIsIphoneX']),
        dragMove(e) {
            console.log(e);
        },
    },
    onReady() {
        // ⚠️注意：组件内部查询元素 需要绑定this
        const query = uni.createSelectorQuery().in(this);
        query.select('.xdpage').boundingClientRect(res => {
            this.allHeight = res.height;
        }).exec();
        query.select('.xdpage-footer').boundingClientRect(res => {
            this.footerHeight = res.height;
        }).exec();
        const _this = this;
        uni.getSystemInfo({
            success: function(res) {
                const safeBottom = res.screenHeight - res.safeArea.bottom;
                _this.setIsIphoneX(safeBottom === 34);
            },
        });
    },
};
</script>

<style lang="scss">
page{
    height: 100%;
    display: flex;
    font-size: 28rpx;
    line-height: 1.8;
}
.xdpage {
    height: 100%;
    width: 750rpx;
    overflow:hidden; // 避免子元素margin触发的滚动
    position: absolute;
    top:0;
    left: 0;
    &-footer {
        position: fixed;
        bottom: 0;
        width: 750rpx;
        z-index: 2;
    }
    &-content {
        background: #f6f6f6;
    }
}
// iphonex适配
.xdpage.xdpage-iphonex {
    .xdpage-footer {
        bottom: 68rpx;
    }
}
</style>

