function getLocalSendReceiverInfo(updateInfo) {
    return {
        userType: '',
        routeOrder: 1,
        fullName: '',
        userName: '',
        userId: '',
        enterpriseName: '',
        enterpriseId: '',
        enterprises: [],
        userAccount: '',
        photoHref: '',
        requireIdentityAssurance: true, // 企业实名情况
        requireEnterIdentityAssurance: false, // 企业经办人实名情况
        receiverType: 'SIGNER',
        forceHandWrite: false,
        notification: '',
        privateLetter: '',
        attachmentRequired: false,
        faceVerify: false,
        messageVerifyCode: '',
        thirdPartyPlatformId: '',
        // 附件
        attachmentList: [],
        userAvatar: null, // 头像
        isBlank: true, // 指明该条数据为新建数据
        ...updateInfo,
    };
}

/**
 * @desc 创建签署人/抄送人的基础数据
 * @params type 类型 ENTERPRISE: 企业 PERSON: 个人
 * @params isCC 是否是添加抄送人 true: 是 null: 否
*/
function getTemplateSendReceiverInfo(updateInfo) {
    return {
        'receiverId': '',
        'batchAdd': false,
        'roleName': '',
        'routeOrder': 1,
        'editable': false,
        'receiverType': 'SIGNER',
        'userType': '',
        'disabled': false,
        'userInfo': {
            'userId': '',
            'userName': '',
            'userAccount': '',
            'enterpriseId': '',
            'enterpriseName': '',
            'employeeId': '',
        },
        'communicateInfo': {
            'ifEditorComplement': false,
            'privateLetter': '',
            'signInstructionDocumentInfo': {
                'ifAllowedDownload': true,
                'ifDisplayConfig': true,
                'ifRequired': false,
                'privateLetterFileInfos': [],
                'privateLetterFileList': [],
            },
            'signInstructionOriginDocumentInfo': {
                'ifDisplayConfig': true,
                'ifRequired': false,
                'privateLetterFileInfos': [],
                'privateLetterFileList': [],
            },
            'signInstructionZipInfo': {
                'ifDisplayConfig': true,
                'ifRequired': false,
                'instructionsAppendixId': '',
                'instructionsAppendixName': '',
            },
        },
        'encryptionSign': {
            'encryptionSignConfig': false,
            'encryptionSignPassword': '',
            'twoFactorAuthentication': false,
        },
        'signerConfig': {
            'forceHandWrite': false,
            'handWriteNotAllowed': false,
            'messageAndFaceVerify': false,
            'faceVerify': false,
            'faceFirst': false,
            'messageVerifyCode': '',
            'accessPassword': '',
            'isCertSign': false,
            'requireCertType': '',
            'handWritingRecognition': false,
            'signImmediately': false,
            'signSealFileName': '',
            'newAttachmentRequired': false,
            'attachmentRequired': false,
            'autoSign': false,
            'mustReadBeforeSign': false,
            'signType': updateInfo.userType === 'ENTERPRISE' ? 'SEAL' : 'SIGNATURE',
            'contractPayer': false,
            'scanOnly': null,
            'notValidateSignVerificationCode': false,
            'noticeSigning': false,
        },
        'noticeConfig': {
            'downloadReceiveEmail': '',
            'noticePhone': '',
            'noticeMail': '',
            'needNotice': true,
            'noticeAfterShared': false,
            'contractPayer': false,
            'sendNoticeLanguage': '',
        },
        'realNameAuthentication': {
            'requireIdentityAssurance': true,
            'requireEnterIdentityAssurance': false,
            'showRealNameAuthentication': true,
            'idNumber': '',
        },
        'proxyClaimer': {
            'ifProxyClaimer': false,
            'proxyClaimerAccounts': [],
        },
        'necessaryConfig': {
            'idNumber': false,
            'username': false,
        },
        'multiEmployeeSignaturesConfig': {
            'useMultiEmployeeSignatures': false,
            'multiEmployeeSignaturesCount': 1,
            'employeeRealNameAuthConfigList': [],
            'needIdNumber': false,
            'needUserName': true,
        },
        'attachRequires': [],
        'contractDownloadControl': false,
        'isBlank': true,
        ...updateInfo,
    };
}

export { getLocalSendReceiverInfo, getTemplateSendReceiverInfo };
