export const RECEIVER_CONFIG_MAP = {
    // 签署校验
    'messageAndFaceVerify': '刷脸+验证码校验',
    'faceFirst': '优先刷脸，备用验证码签署',
    'faceVerify': '必须刷脸签署',
    'noticeSigning': '签约须知',
    'mustReadBeforeSign': '阅读完毕再签署',
    'forceHandWrite': '必须手写签名',
    'handWritingRecognition': '必须手写并开启笔迹识别',
    'handWriteNotAllowed': '使用上上签系统签名',
    'notValidateSignVerificationCode': '只在登录时校验验证码，无需签署校验',
    'scanOnly': '扫码认领',
    // 采集材料
    'attachmentRequired': '添加合同附属资料',
    'newAttachmentRequired': '提交签约主体资料',
    // 其他
    'contractPayer': '付费方',
    'contractDownloadControl': '启用合同下载码',
};

export const SIGN_CONFIG = ['messageAndFaceVerify', 'faceFirst', 'mustReadBeforeSign', 'forceHandWrite', 'handWritingRecognition', 'handWriteNotAllowed', 'notValidateSignVerificationCode', 'scanOnly', 'faceVerify'];
export const FACE_CONFIG = ['messageAndFaceVerify', 'faceFirst', 'faceVerify'];
export const ATTACHMENT_CONFIG = ['newAttachmentRequired'];
export const OTHER_CONFIG = ['contractPayer', 'contractDownloadControl'];
export const NO_SUPPORT_SIGN_TYPE = ['CONFIRMATION_REQUEST_SEAL'];
