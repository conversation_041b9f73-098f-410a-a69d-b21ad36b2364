var Fly = require('flyio/dist/npm/wx');
// #ifdef MP-ALIPAY
Fly = require('flyio/dist/npm/ap');
// #endif

var fly = new Fly();
import interceptor from './interceptors';
// fly.config.baseURL = 'https://wx502.bestsign.info';
// fly.config.baseURL = 'https://ent-k8s.bestsign.tech';
fly.config.baseURL = 'https://ent2-hwy.bestsign.info';
// fly.config.baseURL = 'http://**************:7001';
// fly.config.baseURL = 'https://ent.bestsign.cn';

if (process.env.NODE_ENV === 'production') {
    fly.config.baseURL = 'https://ent.bestsign.cn';
    // #ifdef MP-ALIPAY
    fly.config.baseURL = 'https://wx502.bestsign.cn';
    // #endif
}

fly.baseURL = fly.config.baseURL;
interceptor(fly);
export default fly;
