/**
 * 全局拦截器
 */
import store from 'store';

// 解决flyio 支付宝小程序ios端 PUT请求后端无法正常接收参数的问题
function handleAlipayPutRequest(request) {
    return new Promise((resolve, reject) => {
        const { baseURL, url, method, headers, body } = request;
        uni.request({
            url: baseURL + url,
            method: method,
            header: headers,
            data: body,
            success: (res) => {
                resolve(res);
            },
            fail: (err) => {
                reject(err);
            },
        });
    });
}

export default function interceptor(fly) {
    // http request 请求拦截器
    fly.interceptors.request.use((request, promise) => {
        // 给所有请求添加自定义header
        let requestSource;
        // #ifdef MP-WEIXIN
        requestSource = store.state.isWeWork ? 'QWX_APPLET' : 'WX_APPLET';
        // #endif
        // #ifdef MP-ALIPAY
        requestSource = 'ALIPAY_APPLET';
        // #endif
        request.headers['request-source'] = requestSource;
        request.headers['terminal'] = requestSource;

        const accessToken = uni.getStorageSync('accessToken');
        if (accessToken) {
            request.headers.Authorization = `bearer ${accessToken}`;
        }
        // 时间戳
        request.method === 'GET' && (request.params._ = new Date().getTime());
        // #ifdef MP-ALIPAY
        if (request.method === 'PUT') {
            return promise.resolve(handleAlipayPutRequest(request));
        }
        // #endif
        return request;
    });

    // http response 拦截器
    fly.interceptors.response.use(
        (response) => {
            const data = response.data;
            try {
                response.data = JSON.parse(data);
            } catch (err) {
                response.data = data;
            }
            return response;
        },
        (error) => {
            const originalRequest = error.request;
            const status = error.response && error.response.status;
            let data = error.response && error.response.data;
            try {
                data = JSON.parse(data);
            } catch (err) {
                console.log(err);
            }
            const message =  data && data.message;
            if (status === 401 && !originalRequest._retry && !['/users/applets/login-status2', '/users/login-status'].includes(originalRequest.url)) { // 重新登录
                originalRequest._retry = true;
                store.dispatch('loginPromise').then(() => {
                    return fly.request(originalRequest);
                });
            } else if (status >= 500) {
                uni.showToast({
                    title: '服务器开了点小差，请稍后再试',
                    icon: 'none',
                });
            } else {
                message && !originalRequest.noToast && uni.showToast({
                    title: message,
                    icon: 'none',
                });
            }
            return Promise.reject(error);
        },
    );
}
