// 切换主体、发送合同时拦截
import { mapState } from 'vuex';

export const authMixin = {
    computed: {
        ...mapState('send', ['curEntIndex']),
        ...mapState('user', ['entList']),
    },
    methods: {
        toAuth(type, returnUrl) {
            let authUrl;
            if (type === 'person') {
                authUrl = encodeURIComponent(`${this.$http.baseURL}/mp/auth-m/individual/auth?isBestSignApplet=true&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&returnUrl=${returnUrl}`);
            } else {
                authUrl = encodeURIComponent(`${this.$http.baseURL}/ent/sign/guide?access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&&returnUrl=${returnUrl}`);
            }

            uni.navigateTo({
                url: `/views/webviewRedirect/index?url=${authUrl}`,
            });
        },
        handleGoAuth({ curIndex = 0, returnUrl = '/views/sendGuide/index', showCancel = true }) {
            let content;
            // 个人
            if (this.entList[curIndex].entId === '0') {
                content = '您还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证';
            } else {
                // 企业
                content = '该企业还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证';
            }
            const _self = this;
            // 当前主体未实名的时候
            uni.showModal({
                title: '实名认证后发起签约更有保障',
                content: content,
                showCancel: showCancel,
                cancelText: '取消',
                confirmText: showCancel ? '确定' : '去认证',
                confirmColor: '#127fd2',
                success(res) {
                    if (res.confirm) {
                        // 个人
                        if (_self.entList[curIndex].entId === '0') {
                            _self.toAuth('person', returnUrl);
                        } else {
                            _self.toAuth('enterprise', returnUrl);
                        }
                    } else if (!showCancel) {
                        uni.switchTab({
                            url: returnUrl,
                        });
                    }
                },
            });
        },
    },
};
