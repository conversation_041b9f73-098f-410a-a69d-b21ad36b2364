import { mapState } from 'vuex';
import { logout } from 'api/account';

export const jumpAcccountCompareMixin = {
    data() {
        return {
            homeOriginAccount: '',
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
        needPopConflictTip() {
            const currentAccount = this.commonHeaderInfo?.platformUser?.account;
            // 如果H5首页登录的账号与当前小程序的账号不一致会做弹窗提示
            return this.homeOriginAccount && this.homeOriginAccount !== currentAccount;
        },
    },
    methods: {
        redirectToLogin(path, noLogin = false) {
            // #ifdef MP-WEIXIN
            uni.setStorageSync('loginSuccessPage', path);
            logout().finally(() => {
                uni.setStorageSync('accessToken', '');
                uni.setStorageSync('refreshToken', '');
                uni.setStorageSync('thirdCorpId', '');
                uni.setStorageSync('sensorsUserInfo', null);
                uni.reLaunch({
                    url: `/subViews/login/index?account=${this.homeOriginAccount}`,
                });
            });
            // #endif
            // #ifdef MP-ALIPAY
            if (noLogin) {
                this.oauthLogin();
            }
            // #endif
        },
        resolveHomeAccountCompare(path, callback) {
            if (!this.needPopConflictTip) {
                callback && callback();
                return;
            }
            let content = `跳转账号与小程序当前登录账号不一致，您可选择切换账号登录进入后续页面，或者点击我知道了，停留在原账号的小程序页面。`;
            let showCancelBtn = true;
            // #ifdef MP-ALIPAY
            content = `跳转账号与小程序当前登录账号不一致，点击下方按钮关闭弹窗，停留在原账号的小程序页面。`;
            showCancelBtn = false;
            // #endif
            // 如果需要处理账号不一致提示(H5首页跳转场景)，则在用户选择完成后继续请求
            uni.showModal({
                title: '账号不一致',
                content,
                confirmText: '我知道了',
                confirmColor: '#127fd2',
                cancelText: '切换账号',
                showCancel: showCancelBtn,
                success: (res) =>  {
                    if (res.cancel) {
                        this.redirectToLogin(path);
                    } else {
                        this.contractId = '';
                        callback && callback();
                    }
                },
            });
        },
    },
    onLoad(query) {
        if (query.isFromH5Home === '1') { // H5首页过来会带账号，用作后续对比
            this.homeOriginAccount = query.account;
        }
    },
};
