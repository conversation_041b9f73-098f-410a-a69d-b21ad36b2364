import { mapState, mapGetters } from 'vuex';
import {
    getOrderId,
    getWeChatPayParam,
    getAlipayPayParam,
    getFeaturesOrderId,
    getAiOrderId,
} from 'api/charge';
export const payMixin = {
    data() {
        return {
            payLoading: false,
            contractId: '',
            routeQuery: {},
            pageName: 'charge', // charge(充值页) || anniversary(周年庆页)
            cachePaymentData: {}, // 缓存唤起支付的参数，活动弹窗可以继续唤起相关处理
            showCancelPayPopup: false,
            orderInfo: {},
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
        ...mapGetters(['mpSourceType']),

    },
    methods: {
        handleCloseCancelPayPopup() {
            this.showCancelPayPopup = false;
            this.cachePaymentData = {};
            this.orderInfo = {};
        },
        handleContinuePayer() {
            this.showCancelPayPopup = false;
            // 根据缓存数据重新唤起支付
            this.openPayment(this.cachePaymentData, this.orderInfo);
        },
        async getPaymentData(orderId) {
            let requestData = {};
            // #ifdef MP-WEIXIN
            const { data: { timeStamp, nonceStr, packageStr, signType, paySign } } = await getWeChatPayParam({ orderId, sourceType: this.mpSourceType });
            requestData = {
                timeStamp,
                nonceStr,
                package: packageStr,
                signType,
                paySign,
            };
            // #endif
            // #ifdef MP-ALIPAY
            const { data: { tradeNo } } = await getAlipayPayParam({ orderId, sourceType: this.mpSourceType });
            requestData = {
                tradeNO: tradeNo,
            };
            // #endif
            return requestData;
        },
        async goToPay(product) {
            const { productPackageId, name, originPrice, contractNum, price, featureId, isNumChangeable, productType } = product;
            this.$sensors.track({
                eventName: this.pageName === 'charge' ? 'Mp_CommodityList_BtnClick' : 'Mp_TenYearsActivity_BtnClick',
                eventProperty: {
                    commodity_id: productPackageId,
                    commodity_name: name,
                    commodity_price: originPrice,
                    purchase_num: contractNum,
                    purchase__price: price,
                    icon_name: this.pageName === 'charge' ? '去支付' : '立即购买',
                },
            });
            if (this.payLoading) {
                return;
            }
            this.payLoading = true;
            this.$point('click_go_to_pay', {
                biz_id: productPackageId,
            });
            let res = {};
            if (featureId) { // 高级功能充值
                res = await getFeaturesOrderId(productPackageId, this.commonHeaderInfo.hasGroupConsole);
            } else if (productType === 24) { // ai套餐充值
                res = await getAiOrderId(productPackageId);
            } else { // 合同套餐充值
                res =  await getOrderId({
                    id: productPackageId,
                    contractId: this.contractId,
                    contractNum: isNumChangeable ? contractNum : 0,
                });
            }
            const { data: { orderId } } = res;
            const requestData = await this.getPaymentData(orderId);
            this.openPayment(requestData, { orderId, productPackageId, originPrice, contractNum, price, name });
        },
        openPayment(requestData, { orderId, productPackageId, originPrice, contractNum, price, name }) {
            const _self = this;
            uni.requestPayment(
                {
                    ...requestData,
                    success: function(res) {
                        _self.$point('enter_payment_completion_page', {
                            biz_id: orderId,
                        });
                        _self.$sensors.track({
                            eventName: 'Mp_OrderSubmit_Result',
                            eventProperty: {
                                order_id: orderId,
                                commodity_id: productPackageId,
                                commodity_name: name,
                                commodity_price: originPrice,
                                purchase_num: contractNum,
                                purchase__price: price,
                                pay_type: '微信支付',
                                is_use_discount: false,
                                discount_name: '',
                                discount_amount: 0,
                                is_success: true,
                            },
                        });
                        _self.payLoading = false;
                        // #ifdef MP-ALIPAY
                        const { resultCode } = res;
                        if (resultCode !== '9000') {
                            return;
                        }
                        // #endif
                        uni.redirectTo({
                            url: `/views/payResult/index?isFromH5=${this.routeQuery?.isFromH5Home === '1'}&isAnniversaryPage=${this.pageName}`,
                        });
                    },
                    fail(err) {
                        _self.payLoading = false;
                        if (_self.calcNeedPopupOrNot()) {
                            _self.showCancelPayPopup = true;
                            _self.cachePaymentData = { ...requestData };
                            _self.orderInfo = {
                                orderId, productPackageId, originPrice, contractNum, price, name,
                            };
                        }
                        _self.$sensors.track({
                            eventName: 'Mp_OrderSubmit_Result',
                            eventProperty: {
                                order_id: orderId,
                                commodity_id: productPackageId,
                                commodity_name: name,
                                commodity_price: originPrice,
                                purchase_num: contractNum,
                                purchase__price: price,
                                pay_type: '微信支付',
                                is_use_discount: false,
                                discount_name: '',
                                discount_amount: 0,
                                is_success: false,
                                fail_http_code: '',
                                fail_error_code: '',
                                fail_reason: JSON.stringify(err),
                                request_url: '',
                            },
                        });
                        console.log('pay fail:' + JSON.stringify(err));
                    },
                });
        },
        calcNeedPopupOrNot() {
            const lastPopupDate = uni.getStorageSync('cancelPopupDate');
            if (!lastPopupDate) {
                return true;
            }
            // 一天只展示一次
            return new Date().getTime() - Number(lastPopupDate) > 24 * 60 * 60 * 1000;
        },
    },
    async onLoad(query) {
        this.routeQuery = query;
        this.contractId = query.contractId || '';
    },
};
