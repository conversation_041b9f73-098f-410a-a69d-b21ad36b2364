import { switchEnt } from 'api/send';
export const targetMixin = {
    methods: {
        goToTarget() {
            // 跳转个人签署页（扫码签，指定签署人签署)
            if (this.query.MPTargetPage === 'signPageFromQR') {
                this.handleSign();
            }
        },
        // 处理签署
        async handleSign() {
            if (this.query.account) {
                if (this.supplierAuthInfo.mobile !== this.query.account) {
                    uni.showModal({
                        title: '提示',
                        content: '您当前授权登录的账号与发件方指定的签收账号不一致，无法查看签署该合同',
                        confirmText: '我知道了',
                        showCancel: false,
                    });
                } else {
                    // 无权限拦截
                    const isIntercept = await this.needToAuthInterceptPage(this.query.contractId, 'PERSON');
                    // 如果被拦截就走以前的短链接签署 不执行后面的逻辑
                    if (isIntercept) {
                        return uni.showModal({
                            title: '提示',
                            content: '当前账号的实名与发件方指定的实名不一致，您无法签署该合同，请联系发件方！',
                            confirmText: '我知道了',
                            showCancel: false,
                        });
                    }
                    this.goSign(0, this.query.contractId);
                }
            } else {
                // 获取扫码待认领的receiverId
                const receiverId = await this.getClaimInfo();
                // 认领合同
                this.claimContarct(this.query.contractId, receiverId);
            }
        },
        // 认领合同
        async  claimContarct(contractId, receiverId) {
            try {
                await this.$http.get({
                    url: `/contract-api/contracts/${contractId}/claim-decision-for-other/${receiverId}`,
                    data: {
                        selectedEntId: '0',
                    },
                });
                // 默认为个人，根据需求后面需要切换主体
                this.goSign(0, this.query.contractId);
            } catch (error) {
                uni.hideLoading();
                console.log(error);
            }
        },
        // 根据token获取认领信息receiverId
        async getClaimInfo() {
            const splitTokenIndex = this.query.shortUrl.lastIndexOf('/');
            const token = this.query.shortUrl.substring(splitTokenIndex + 1);
            const { data: { result } } = await this.$http.get(`/contract-api/ignore/contracts/claim-info/${token}`);
            return result.receiverId;
        },
        // 签署短链接进入
        shortUrlLoginSign() {
            uni.reLaunch({
                url: `/views/webviewRedirect/index?url=${this.query.shortUrl}`,
            });
        },
        // 切主体跳转签署页面
        async goSign(entId, contractId) {
            const { data } = await switchEnt(entId);
            uni.setStorageSync('accessToken', data.access_token);
            uni.setStorageSync('refreshToken', data.refresh_token);

            const signUrl = encodeURIComponent(`${this.$http.baseURL}/mobile/sign?isBestSignApplet=true&channel=notice&contractId=${contractId}&type=sign&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`);
            uni.reLaunch({
                url: `/views/webviewRedirect/index?url=${signUrl}`,
            });
        },
        // 实名拦截
        async needToAuthInterceptPage(contractId, userType) {
            const data = await this.$http.get({
                url: `/contract-api/contracts/detail/check-person-auth?contractId=${contractId}`,
            });
            if (userType === 'PERSON') {
                const { mustSignWithSameEntity, hasUserAuthenticated, idNumberForVerify, inputUserName } = data;
                if ((mustSignWithSameEntity && !hasUserAuthenticated) || (mustSignWithSameEntity && hasUserAuthenticated && (idNumberForVerify || inputUserName))) {
                    return true;
                }
            } else if (userType === 'ENTERPRISE') {
                const { mustSignWithSameEntity, hasEntAuth, hasEntEnroll } = data;
                if ((mustSignWithSameEntity && !hasEntAuth) || (mustSignWithSameEntity && hasEntAuth && !hasEntEnroll)) {
                    return true;
                }
            }
            return false;
        },
    },
};
