// import store from 'store';
import { RouterMount, createRouter } from 'uni-simple-router';

const router = createRouter({
    platform: process.env.VUE_APP_PLATFORM,
    // detectBeforeLock: (router, to, navType) => { // 配置解锁[重要]。关闭跳转路由锁
    //     router.$lockStatus=false;
    // },
    routerErrorEach: err => {
        console.log(err);
    },
    routes: [...ROUTES],
});

router.beforeEach((to, from, next) => {
    // if (to.path === '/subViews/login/index' || to.path === '/views/agreementWebview/index') {
    //     return next();
    // }
    next();
});

export {
    router,
    RouterMount,
};
