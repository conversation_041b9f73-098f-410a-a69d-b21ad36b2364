import Vue from 'vue';
import Vuex from 'vuex';
import { switchEnt } from 'api/send';
import { getLoginStatus, wxCodeLogin, weWorkCodeLogin, getBindingStatus } from 'api/account.js';
import user from './modules/user';
import send from './modules/send';
import template from './modules/template';
let isWeWork = false;
let loginText = '立即登录';
// #ifdef MP-WEIXIN
isWeWork = wx.hasOwnProperty('qy');
// #endif
// #ifdef MP-ALIPAY
loginText = '实名登录';
// #endif
Vue.use(Vuex);
const state = {
    loading: false,
    isWeWork: isWeWork,
    loginText: loginText,
    weWorkSuiteId: process.env.NODE_ENV === 'production' ? 'ww2858490cd421822a' : 'wwe954a9c25eeba458', // 企业微信小程序suidId，区分生产和预发环境
    toastParam: {
        show: false,
        message: '',
    },
    captchaVerifyParam: '',
};
const mutations = {
    changeLoading(state, data) {
        state.loading = data;
    },
    setToastParam(state, { show, message }) {
        state.toastParam.show = show;
        state.toastParam.message = message;
    },
};
const modules = {
    user,
    send,
    template,
};

const getters = {
    // 小程序的sourceType，微信小程序和企业微信小程序下不同，但不区分是正式环境还是测试环境
    mpSourceType(state) {
        let sourceType = 101;
        // #ifdef MP-WEIXIN
        const wxCode = 101;
        const weWorkCode = 201;
        sourceType = state.isWeWork ? weWorkCode : wxCode;
        // #endif
        // #ifdef MP-ALIPAY
        sourceType = 301;
        // #endif
        return sourceType;
    },
};
const actions = {
    oauthLogin() {
        uni.navigateTo({
            url: 'views/alipayLogin/index',
        });
    },
    setLoading({ commit }, bool) {
        commit('changeLoading', bool);
        if (bool) {
            uni.showLoading({
                title: '',
                mask: true,
            });
        } else {
            uni.hideLoading();
        }
    },
    // 登录保证，未登录状态下会登录并设置token，uni.login()获取的code只用一次
    loginPromise(store) {
        return new Promise((resolve, reject) => {
            function login() {
                uni.clearStorageSync();
                reject();
            }
            function handleLogin() {
                if (store.state.isWeWork) {
                    uni.qy.login({
                        success(res) {
                            if (res.code) {
                                weWorkCodeLogin(res.code).then(({ data }) => {
                                    const { code, access_token, refresh_token, userId } = data;
                                    uni.setStorageSync('thirdCorpId', data?.thirdCorpId || '');
                                    if (code === 0) {
                                        userId && Vue.$sensors.sensorsLogin(userId);
                                        uni.setStorageSync('userId', userId);
                                        uni.setStorageSync('accessToken', access_token);
                                        uni.setStorageSync('refreshToken', refresh_token);
                                        Vue.$sensors.track({
                                            eventName: 'Mp_Login_Result',
                                            eventProperty: {
                                                icon_name: '登录',
                                                is_success: true,
                                                is_first_time: false,
                                            },
                                        });
                                        resolve(true);
                                        return;
                                    } else if (code === 2) {
                                        wxCodeLogin(res.code, data?.thirdCorpId).then(({ data }) => {
                                            const { access_token, refresh_token, userId } = data;
                                            userId && Vue.$sensors.sensorsLogin(userId);
                                            uni.setStorageSync('userId', userId);
                                            uni.setStorageSync('accessToken', access_token);
                                            uni.setStorageSync('refreshToken', refresh_token);
                                            Vue.$sensors.track({
                                                eventName: 'Mp_Login_Result',
                                                eventProperty: {
                                                    icon_name: '登录',
                                                    is_success: true,
                                                    is_first_time: false,
                                                },
                                            });
                                            resolve(true);
                                            return;
                                        });
                                    }
                                    login();
                                }).catch((err) => {
                                    const failCode = err.response?.data?.code || '';
                                    const failReason = err.response?.data?.message || '';
                                    Vue.$sensors.track({
                                        eventName: 'Mp_Login_Result',
                                        eventProperty: {
                                            icon_name: '登录',
                                            is_success: false,
                                            fail_code: failCode,
                                            fail_reason: failReason,
                                        },
                                    });
                                    login();
                                });
                            }
                        },
                    });
                } else {
                    uni.login({
                        success(res) {
                            if (res.code) {
                                wxCodeLogin(res.code).then(({ data }) => {
                                    const { code, access_token, refresh_token, chosenEntId, userId,  thirdOpenId } = data;
                                    if (code === 0) {
                                        userId && Vue.$sensors.sensorsLogin(userId);
                                        uni.setStorageSync('userId', userId);
                                        uni.setStorageSync('accessToken', access_token);
                                        uni.setStorageSync('refreshToken', refresh_token);
                                        uni.setStorageSync('chosenEntId', chosenEntId);
                                        Vue.$sensors.track({
                                            eventName: 'Mp_Login_Result',
                                            eventProperty: {
                                                icon_name: '登录',
                                                is_success: true,
                                                is_first_time: false,
                                            },
                                        });
                                        resolve(true);
                                        return;
                                    } else if (code === 1) {
                                        Vue.$sensors.setIdentifyId(thirdOpenId);
                                    }
                                    login();
                                }).catch((err) => {
                                    const failCode = err.response?.data?.code || '';
                                    const failReason = err.response?.data?.message || '';
                                    Vue.$sensors.track({
                                        eventName: 'Mp_Login_Result',
                                        eventProperty: {
                                            icon_name: '登录',
                                            is_success: false,
                                            fail_code: failCode,
                                            fail_reason: failReason,
                                        },
                                    });
                                    login();
                                });
                            }
                        },
                    });
                }
            }
            const request = store.state.isWeWork ? getLoginStatus() : getBindingStatus();
            request.then(resp => {
                if (resp.data.value || store.state.isWeWork) {
                    resolve(true);
                    return;
                }
                handleLogin();
            }).catch(async() => {
                handleLogin();
            });
        });
    },
    switchEntId(store, entId) {
        return new Promise((resolve) => {
            switchEnt(entId).then(({ data }) => {
                const { access_token, refresh_token } = data;
                uni.setStorageSync('accessToken', access_token);
                uni.setStorageSync('refreshToken', refresh_token);
                resolve(true);
            });
        });
    },
};
export default new Vuex.Store({
    state,
    actions,
    getters,
    mutations,
    modules,
});
