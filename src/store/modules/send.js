import Vue from 'vue';
import { getChargingData } from '@/api/charge';
export default {
    namespaced: true,
    name: 'send',
    state: {
        sendType: '', // 发起签名类型： 本地发起 localSend 模板发起 templateSend
        isIphoneX: false, // 是否为IphoneX及其以上版本机型
        // 审批流
        contractId: '', // 当前发送合同的contractId
        flowStepList: [], // 审批流顺序
        definesData: [], // 审批id\name， 可能存在多个审批流
        currentSelectDefineIndex: 0, // 当前选中的审批index
        hasLocalSendRight: false, // 当前主体 是否有本地发起权限
        receiverList: [], // 签署人列表
        curReceiver: {},
        draftInfo: {},
        fromAddress: null, // 当前页面是否由地址簿跳转而来，值为 null, 'inner', 'outer'
        uploadedFileList: [],
        h5Params: { type: '', params: {} }, // 跳转h5url需要带的参数 personRealNameFace personRealName
        contractDescribeInfo: {},
        signOrderly: false, // 是否是顺序签署
        curEntIndex: '0',
        curEntId: '',
        marks: [],
    },
    getters: {},
    actions: {
        // 获取审批流列表
        async getApprovalFlowInfo({ commit, state }) {
            const { defInfoId } = state.definesData[state.currentSelectDefineIndex];
            let { data: flowStepList } = await Vue.$http.get(`ents/flow/infos/${defInfoId}/option/steps`);
            flowStepList = flowStepList.map(item => {
                const defaultPeople = item.defineApproverVOList && item.defineApproverVOList[0];
                return {
                    ...item,
                    ...defaultPeople,
                    portraitSrc: `${Vue.$http.baseURL}/ents/${item.orgId}/employees/${item.empId || defaultPeople.empId}/portrait?access_token=${uni.getStorageSync('accessToken')}`,
                };
            });
            commit('setFlowStepList', flowStepList);
        },
        // 计费
        async getCharging({ dispatch, state }) {
            const url = state.sendType === 'localSend' ? `/contract-api/charging/contracts/${state.contractId}` : `/template-api/v2/draft/${state.contractId}/charge`;
            let chargeStatus;
            let toBCount;
            let toCCount;
            let isReceivePay;
            let isCCReceiverPay;
            if (state.sendType === 'localSend') {
                const { data: { chargingLineStatus, toBForUse, toCForUse } } = await getChargingData(url);
                chargeStatus = chargingLineStatus;
                toBCount = toBForUse;
                toCCount = toCForUse;
            } else {
                const { data: { chargingStatus, tobAmount, tocAmount, isCCReceiverPayer, isReceivePayer } } = await getChargingData(url);
                chargeStatus = chargingStatus;
                toBCount = tobAmount;
                toCCount = tocAmount;
                isReceivePay = isReceivePayer;
                isCCReceiverPay = isCCReceiverPayer;
            }

            // 签署人/抄送方付费
            if (state.sendType === 'templateSend' && (isReceivePay || isCCReceiverPay)) {
                uni.showModal({
                    title: '提示',
                    content: isReceivePay ? '该合同由您指定的签署方付费' : '该合同由您指定的抄送方付费',
                    confirmText: '确定',
                    cancelText: '取消',
                    confirmColor: '#127fd2',
                    success: (res) => {
                        if (res.confirm) {
                            dispatch('onChargeConfirm', {
                                selectProductType: chargeStatus === 'TOB_ENOUGH' ? 'TO_B' : '',
                            });
                        }
                    },
                });
                return false;
            }

            // 对公不足
            if (chargeStatus !== 'ENOUGH' && chargeStatus !== 'TOB_ENOUGH') {
                // 提示去充值
                uni.showModal({
                    title: '提示',
                    content: '可用合同份数不足',
                    confirmText: '去购买',
                    cancelText: '取消',
                    confirmColor: '#127fd2',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateTo({ url: '/views/charge/index' });
                        }
                    },
                });
                return false;
            }

            let content = '';
            // 对私不足
            if (chargeStatus === 'TOB_ENOUGH') {
                content += '对私合同可用份数不足时会扣除对公合同；';
            }
            if (toCCount) {
                content += `${toCCount}份对私合同`;
            }
            if (toCCount && toBCount) {
                content += '；';
            }
            if (toBCount) {
                content += `${toBCount}份对公合同`;
            }

            if (Vue.$store.state.user.commonHeaderInfo.userType === 'Person') {
                content += `\n\n发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。`;
            }
            uni.showModal({
                title: '是否确认发送',
                content,
                confirmText: '发送',
                confirmColor: '#127fd2',
                success: (res) => {
                    if (res.confirm) {
                        dispatch('onChargeConfirm', {
                            selectProductType: chargeStatus === 'TOB_ENOUGH' ? 'TO_B' : '',
                        });
                    }
                },
            });
        },
        // 计费确认，最终发送合同
        async onChargeConfirm({ commit, state }, signChargeRes) {
            const url = state.sendType === 'localSend'
                ? `/contract-api/contracts/${state.contractId}/send/`
                : `/template-api/v2/draft/${state.contractId}/send`;
            uni.showLoading({
                title: '发送中...',
                mask: true,
            });
            try {
                const selectDefine = state.definesData[state.currentSelectDefineIndex];
                const data = {
                    defInfoId: selectDefine && selectDefine.defInfoId,
                    flowStepList: state.flowStepList || [],
                    sendPlatform: state.isWeWork ? 'QWX' : 'WX', // 'WEB',  QWX
                };
                let params;
                if (state.sendType === 'localSend') {
                    params = {
                        ...data,
                        labels: state.marks,
                        selectProductType: signChargeRes.selectProductType,
                        notAllowNotify: false,
                        notifyInEnglish: false,
                    };
                } else {
                    params = {
                        ...data,
                        draftId: state.contractId,
                        sendPlatform: state.isWeWork ? 'QWX' : 'WX', // 'WEB',  QWX,
                        thirdApproval: false,
                    };
                }
                await Vue.$http.post(url, params);
                uni.showToast({
                    icon: 'success',
                    title: '已发送',
                    duration: 3000,
                    mask: true,
                });
                setTimeout(() => {
                    // 发送成功，回到首页
                    uni.reLaunch({
                        url: 'views/home/<USER>',
                    });
                    commit('setReceiverList', []);
                    commit('setUploadedFileList', []);
                    commit('setContractDescribeInfo', {});
                    uni.hideLoading();
                }, 300);
            } finally {
                setTimeout(uni.hideLoading, 2000);
            }
        },
    },
    mutations: {
        setIsIphoneX(state, data) {
            state.isIphoneX = data;
        },
        setH5Params(state, data) {
            state.h5Params.type = data.type;
            state.h5Params.params = data.params || {};
        },
        setSendType(state, data) {
            state.sendType = data;
        },
        setContractId(state, data) {
            state.contractId = data;
        },
        setFlowStepList(state, data) {
            state.flowStepList = data;
        },
        setDefinesData(state, data) {
            state.definesData = data;
        },
        setDefineIndex(state, data) {
            state.currentSelectDefineIndex = data;
        },
        setUploadedFileList(state, uploadedFileList) {
            state.uploadedFileList = uploadedFileList;
        },
        removeUploadedFileList(state, index) {
            state.uploadedFileList.splice(index, 1);
        },
        setReceiverList(state, receiverList) {
            state.receiverList = receiverList;
        },
        setDraftInfo(state, draftInfo) {
            state.draftInfo = draftInfo;
        },
        updateReceiverList(state, data) {
            const { receiverIndex, updateData, rewrite = false, mustCCUser = false } = data;
            if (state.receiverList.length < +receiverIndex + 1) {
                return false;
            }
            const currentReceiver = rewrite ? {
                ...updateData,
                mustCCUser,
            } : {
                ...state.receiverList[receiverIndex],
                ...updateData,
            };
            Vue.set(state.receiverList, receiverIndex, currentReceiver);
            Vue.set(state.draftInfo, 'receiverInfos', state.receiverList);
        },
        setCurReceiver(state, receiver) {
            state.curReceiver = receiver;
        },
        addReceiverList(state, receiver) {
            state.receiverList.push(receiver);
            Vue.set(state.draftInfo, 'receiverInfos', state.receiverList);
        },
        deleteReceiverList(state, index) {
            state.receiverList.splice(index, 1);
            Vue.set(state.draftInfo, 'receiverInfos', state.receiverList);
        },
        setHasLocalSendRight(state, data) {
            state.hasLocalSendRight = data;
        },
        setFromAddress(state, data) {
            state.fromAddress = data;
        },
        setContractDescribeInfo(state, data) {
            state.contractDescribeInfo = data;
        },
        setSignOrderly(state, data) {
            state.signOrderly = data;
        },
        setCurEntIndex(state, data) {
            state.curEntIndex = data;
        },
        setCurEntId(state, data) {
            state.curEntId = data;
        },
        setMarks(state, data) {
            state.marks = data;
        },
    },
};
