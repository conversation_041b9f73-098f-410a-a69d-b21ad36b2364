import Vue from 'vue';
export default {
    namespaced: true,
    name: 'template',
    state: {
        templateList: [],
        templateDocList: [],
        contractTypes: [],
        draftId: '',
        templateFlowList: [],
        templateSelectedFlow: {},
        ifDynamicTemplate: false,
        templatePermissions: {
            sendContract: true, // 使用模板
            modifyDocument: true, // 调整文档
            modifySignRequirement: true, // 调整签署要求
            dragSignLabel: true, // 拖拽标签
            modifySignLabel: true, // 新增/删除签署字段
            editable: true, // 编辑模板
            templateDuplicate: true, // 复制模板
            modifyDocumentFederation: true, // 配置文档组合
            grantManage: true, // 权限分配
            editCustomScene: true, // 编辑场景定制
            invalidStatementOperation: true, // 作废申明
            templateSpecialSeal: true,
            editSupplyAgree: true, // 补充协议
            modifyReceiver: true, // 调整签约方
            contractConfidentiality: true, // 合同保密
            addCCReceiver: true, // 添加抄送方
        }, // 模板操作权限
        pictureFieldsPreviewUrls: {}, // 图片字段临时预览地址
        isLimitFaceConfig: false, // 配置刷脸售是否受限（乐高城低于刷脸成本价，不能使用刷脸签）
    },
    mutations: {
        setIsLimitFaceConfig(state, isLimitFaceConfig) {
            state.isLimitFaceConfig = isLimitFaceConfig;
        },
        setIfDynamicTemplate(state, data) {
            state.ifDynamicTemplate = data;
        },
        setTemplateList(state, data) {
            state.templateList = data;
        },
        setPictureFieldsPreviewUrls(state, { fileId, tempUrl }) {
            state.pictureFieldsPreviewUrls[fileId] = tempUrl;
        },
        // 文档部分
        setTemplateDocList(state, contractList) {
            state.templateDocList = contractList.map(contract => {
                contract.isOriginEmptyDoc = contract.documentType === 'BLANK';
                return contract;
            });
        },
        // 删除文档
        deleteTemplateDoc(state, index) {
            state.templateDocList.splice(index, 1);
        },
        addTemplateDoc(state, doc) {
            state.templateDocList.push(doc);
        },
        updateTemplateDocListByIndex(state, { data, index }) {
            Vue.set(state.templateDocList, index, data);
        },
        // 更新描述字段
        updateDescFieldValByIndex(state, { contractIndex, fieldIndex, val, type }) {
            Vue.set(state.templateDocList[contractIndex].descriptionFieldConfigs[fieldIndex], type, val);
            // console.log('update-describe', state.templateDocList);
        },
        updateContentFieldValByIndex(state, { contractIndex, fieldIndex, val }) {
            Vue.set(state.templateDocList[contractIndex].contentFieldLabels[fieldIndex], 'labelValue', val);
            // console.log('update-content', state.templateDocList);
        },
        setContractTypes(state, contractTypes) {
            state.contractTypes = contractTypes;
        },
        setFieldsHasComplete(state, { contractIndex, val }) {
            Vue.set(state.templateDocList[contractIndex], 'fieldHasComplete', val);
        },
        setDraftId(state, data) {
            state.draftId = data;
        },
        setTemplateFlowList(state, data) {
            state.templateFlowList = data;
        },
        setTemplateSelectedFlow(state, data) {
            state.templateSelectedFlow = data;
        },
        setTemplatePermission(state, permissions) {
            state.templatePermissions = permissions;
        },
    },
    actions: {
        getIsLimitFaceConfig({ commit }) {
            return Vue.$http.request('/template-api/v2/draft/sender/config')
                .then(({ data }) => {
                    const faceFeatureLimit = data?.result?.faceFeatureLimit;
                    commit('setIsLimitFaceConfig', faceFeatureLimit);
                });
        },
        computeFieldsHasComplete({ commit, state }, contractIndex) {
            let contentFieldErrorNum = 0;
            let descriptionFieldErrorNum = 0;

            if ((state.templateDocList[contractIndex]?.contentFieldLabels || []).some(item => !!item.necessary && !item.labelValue)) {
                contentFieldErrorNum++;
            }
            if ((state.templateDocList[contractIndex]?.descriptionFieldConfigs || []).some(item => !!item.necessary && !item.fieldValue)) {
                descriptionFieldErrorNum++;
            }
            commit('setFieldsHasComplete', { contractIndex: contractIndex, val: !(contentFieldErrorNum || descriptionFieldErrorNum) });
        },
        getTemplatePermission({ commit }, templateId) {
            const url = `/template-api/v2/draft/template-permission/${templateId}`;
            return Vue.$http.get(url, {
                noToast: 1,
            }).then((res) => {
                commit('setTemplatePermission', (res.data.authorizedPermissionItem || {}));
            });
        },
    },
};
