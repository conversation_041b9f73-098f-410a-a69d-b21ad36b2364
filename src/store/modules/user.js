
import Vue from 'vue';
import { getSensorsCurrentEntInfo, getSensorsCurrentRoleName } from 'src/utils/sensorsUtils.js';
const state = {
    qwxUserName: '',
    featureIds: [],
    commonHeaderInfo: {},
    entList: [], // 获取账号的所有主体
    userAccount: '',
    currentEntName: '',
    userType: '',
};

// getters
const getters = {
    getUserPermissions: state => {
        const roles = state.commonHeaderInfo.roleDetails;
        const permissionObj = {
            DOWNLOAD_CONTRACT: false,
            SEND_TEMPLATE_CONTRACT: false,
            SEND_LOCAL_FILE: false,
            SHARE_CONTRACT: false,
            SIGN_CONTRACT: false,
            CREATE_CONTRACT_TEMPLATE: false,
            STATS_CONTRACT: false,
            SIGN_M: false,
            ACCOUNT_M: false,
            CHARGE_M: false,
        };
        // 遍历角色权限
        if (roles) {
            roles.forEach(role => {
                role.privileges.forEach(privilege => {
                    permissionObj[privilege.name] = true;
                });
            });
        }
        return {
            download: permissionObj['DOWNLOAD_CONTRACT'],
            sendTemp: permissionObj['SEND_TEMPLATE_CONTRACT'],
            sendLocal: permissionObj['SEND_LOCAL_FILE'],
            share: permissionObj['SHARE_CONTRACT'],
            sign: permissionObj['SIGN_CONTRACT'],
            createTemp: permissionObj['CREATE_CONTRACT_TEMPLATE'],
            statistics: permissionObj['STATS_CONTRACT'],
            sign_m: permissionObj['SIGN_M'],
            account_m: permissionObj['ACCOUNT_M'],
            recharge_m: permissionObj['CHARGE_M'],
        };
    },
    // 是否是主管理员
    ifManager: state => {
        let status = false;
        const roles = state.commonHeaderInfo.roleDetails;
        // 遍历角色权限
        if (roles) {
            roles.forEach(role => {
                if (role.name === '主管理员') {
                    status = true;
                }
            });
        }
        return status;
    },
};

// actions
const actions = {
    // 乐高城配置
    async getFeatures({ commit }) {
        const { data: { featureIds } } = await Vue.$http.get('/users/features');
        commit('setFeatureIds', featureIds);
    },
    // 获取headInfo信息
    async getHeadInfo({ commit }) {
        const { data: headInfo } = await Vue.$http.get('/users/head-info');
        commit('setCommonHeaderInfo', headInfo);
    },
    handleEntList({ commit, state }) {
        const { enterprises, platformUser } = state.commonHeaderInfo;
        enterprises.map((item) => {
            item.showName = '';
            // 企业账号
            if (item.entId !== '0') {
                if (item.entName) {
                    item.showName = item.showName + item.entName + (item.bizName ? ('_' + item.bizName) : '');
                } else {
                    item.showName = `企业账号 - ${platformUser.account}`;
                }
            // 个人账号
            } else {
                item.authStatus = platformUser.authStatus;
                item.showName = platformUser.authStatus === 2 ? `个人账号 - ${platformUser.fullName}` : `个人账号 - ${platformUser.account}`;
            }
            return item;
        });
        commit('setEntList', enterprises);
    },
};

// mutations
const mutations = {
    setFeatureIds(state, data) {
        state.featureIds = data;
    },
    setQwxUserName(state, qwxUserName) {
        state.qwxUserName = qwxUserName;
    },
    setCommonHeaderInfo(state, commonHeaderInfo) {
        state.userAccount = commonHeaderInfo.platformUser.account;
        uni.setStorageSync('chosenEntId', commonHeaderInfo.currentEntId);
        state.commonHeaderInfo = commonHeaderInfo;
        let currentEntName;
        if (commonHeaderInfo.currentEntId === '0') {
            const { authStatus, fullName, account } = commonHeaderInfo.platformUser;
            currentEntName = authStatus === 2 ? `个人账号 - ${fullName}` : `个人账号 - ${account}`;
        } else {
            currentEntName = getSensorsCurrentEntInfo(commonHeaderInfo);
        }
        state.currentEntName = currentEntName;
        // sensors公共属性不能取对象的属性，只能把属性分别取出来存起来
        const userType = commonHeaderInfo?.userType || '';
        state.userType = userType;
        uni.setStorageSync('sensorsUserInfo', JSON.stringify({
            currentEntId: commonHeaderInfo?.currentEntId || '',
            currentAccountType: userType ? (userType.toLowerCase() === 'enterprise' ? '企业账号' : '个人账号') : '',
            currentEntName,
            currentRoleName: getSensorsCurrentRoleName(commonHeaderInfo),
        }));
    },
    setEntList(state, entList) {
        state.entList = entList;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
};
