/* 在这里写一些重置默认样式 */
button[type=primary], button[type=primary][loading], .button-hover[type=primary], uni-button[type=primary] {
    background-color: $--color-primary;
    color: $--color-white;
}
button[disabled][type=primary] {
    background: $--color-text-placeholder;
    color: $--color-info;
}
button[type=default] {
    background: $--color-white;
    border: 1px solid $--color-primary;
    color: $--color-primary;
}
button{
    width: 630rpx;
    height: 100rpx;
    line-height: 100rpx;
    margin: 30rpx auto;
}
input[disabled]{
    color: $--color-info;
}
.button-hover[plain]{
    color: $--color-primary;
}
.button[plain][disabled],button[disabled]:not([type]){
    background: none;
}
i, a, checkbox-group{  
    display: inline-block;
}
h2{
    font-size: 40rpx;
}
radio, checkbox{
    transform: scale(0.5);
}
.popup-content{
    background: $--color-white;
}
.disabled{
    color: $--color-info !important;
}
.ellipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 支付宝小程序样式重置
// #ifdef MP-ALIPAY
page{
    height: 100vh;
    background: #fff;
}
input{
    background: transparent;
}
span{
    line-height: inherit;
}
// #endif