<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <view class="add-receiver">
            <!-- 接收手机号/邮箱 -->
            <view class="add-receiver__first add-receiver__block">
                <view class="add-receiver__item">
                    <view><text class="add-receiver__necessary">*</text>接收手机号/邮箱</view>
                    <view class="add-receiver__input" v-if="!ifProxy && !ifMulAccountProxy">
                        <input
                            v-model="receiverUserAccount"
                            placeholder-class="add-receiver__input_placeholder"
                            placeholder="手机号/邮箱"
                            @blur="onBlurUserAccount"
                            :disabled="!hasModifyReceiverRight && fieldConditionEditable.userAccount"
                        />
                        <text class="icon-ic_contacts" v-if="!isPerson && (hasModifyReceiverRight || showCCBtn)" @click.stop="goAddressBook"></text>
                    </view>
                    <view v-else class="add-receiver__no-wrap add-receiver__right">
                        {{ ifProxy ? `对方前台代收${recipient.signerConfig.scanOnly ? '-扫码认领': ''}` : receiverUserAccount }}
                    </view>
                </view>
                <!-- 公司名称/个人姓名 -->
                <view class="add-receiver__item">
                    <view>
                        <text :class="(recipient.userType === 'ENTERPRISE' || (recipient.userType === 'PERSON' && sendType === 'templateSend' && recipient.necessaryConfig.username)) ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>
                        {{ recipient.userType === 'ENTERPRISE' ? '公司名称':'姓名' }}</view>
                    <view>
                        <!-- 企业名称 -->
                        <template v-if="recipient.userType === 'ENTERPRISE'">
                            <input v-if="!hasModifyReceiverRight && fieldConditionEditable.enterpriseName" v-model="receiverEnterpriseName" disabled />
                            <uni-combox
                                v-else
                                v-model="receiverEnterpriseName"
                                :candidates="canChoseEnterprises"
                                placeholder="请输入"
                                :emptyTips="'无可选企业名称，请手动输入'"
                                @input="handleEnterpriseNameUpdate"
                            ></uni-combox>
                        </template>
                        <!-- 个人姓名 -->
                        <template v-else>
                            <input
                                v-model="receiverUserName"
                                placeholder-class="add-receiver__input_placeholder"
                                placeholder="请输入"
                                :disabled="!hasModifyReceiverRight && fieldConditionEditable.userName"
                            />
                        </template>
                    </view>
                </view>
                <!-- 企业经办人 -->
                <view class="add-receiver__item" v-if="recipient.userType === 'ENTERPRISE'">
                    <view>
                        <text :class="(sendType === 'templateSend' && recipient.realNameAuthentication.requireUserName) ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>
                        经办人
                    </view>
                    <view>
                        <input
                            v-model="entUserName"
                            placeholder="请输入"
                            placeholder-class="add-receiver__input_placeholder"
                            :disabled="!hasModifyReceiverRight && rawEmpty.entUserName"
                        />
                    </view>
                </view>
                <!-- 个人身份证号 -->
                <view class="add-receiver__item" v-if="recipient.userType === 'PERSON'">
                    <view>
                        <text :class="(sendType === 'templateSend' && recipient.necessaryConfig.idNumber) ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>
                        身份证号
                    </view>
                    <view>
                        <input
                            type="text"
                            v-model="idNumber"
                            placeholder="请输入"
                            placeholder-class="add-receiver__input_placeholder"
                            :disabled="!hasModifyReceiverRight && fieldConditionEditable.idNumber"
                        />
                    </view>
                </view>
                <!-- 签约角色 -->
                <view class="add-receiver__item">
                    <view><text :class="sendType ==='templateSend' ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>签约角色</view>
                    <view>
                        <input
                            type="text"
                            v-model="recipient.roleName"
                            placeholder="请输入"
                            placeholder-class="add-receiver__input_placeholder"
                            :disabled="!hasModifyReceiverRight && rawEmpty.role"
                        />
                    </view>
                </view>
            </view>
            <view class="add-receiver__way add-receiver__block">
                <!-- 签署方式 -->
                <view class="add-receiver__item ">
                    <view><text class="add-receiver__unnecessary">*</text>签署方式</view>
                    <view>
                        <picker
                            @change="handleSelectSignWay"
                            :value="selectSignIndex"
                            :range="selectSignWay"
                            range-key="name"
                            :disabled="sendType === 'templateSend' && ((!templatePermissions.modifyReceiver && !templatePermissions.modifySignRequirement) || showCCBtn || recipient.mustCCUser)"
                        >
                            {{ selectSignWay[selectSignIndex].name }}
                        </picker>
                    </view>
                </view>
            </view>
            <view class="add-receiver__block">
                <!-- 开启/关闭个人/企业经办人实名 -->
                <view
                    class="add-receiver__item add-receiver__auth"
                    v-if="selectSignWay[selectSignIndex].value !== 'CC_USER'
                        && (sendType ==='localSend' || (sendType === 'templateSend' && recipient.realNameAuthentication.showRealNameAuthentication))"
                >
                    <view><text class="add-receiver__unnecessary">*</text>{{ recipient.userType === 'ENTERPRISE' ? (sendType === 'templateSend' ? '启用经办人身份校验' : '开启经办人实名') : '需要个人实名' }}</view>
                    <switch
                        :checked.sync="requireIdentity"
                        @change="switchChange(sendType === 'localSend' ? 'requireIdentityAssurance' : 'realNameAuthentication', $event)"
                        class="add-receiver__switch_btn"
                        color="#127FD2"
                    />
                </view>
                <!--模板发送——经办人姓名/身份证号-->
                <view
                    v-if="sendType === 'templateSend' && requireIdentity && selectSignWay[selectSignIndex].value !== 'CC_USER' && recipient.userType === 'ENTERPRISE'"
                >
                    <view class="add-receiver__item">
                        <view>
                            <text :class="recipient.realNameAuthentication.requireUserName ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>
                            经办人姓名
                        </view>
                        <view>
                            <input
                                type="text"
                                v-model="recipientUserName"
                                placeholder="请输入"
                                placeholder-class="add-receiver__input_placeholder"
                            />
                        </view>
                    </view>
                    <view class="add-receiver__item">
                        <view>
                            <text :class="recipient.realNameAuthentication.requireIdNumber ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>
                            经办人身份证号</view>
                        <view>
                            <input
                                type="text"
                                v-model="recipientIdNumber"
                                placeholder="请输入"
                                placeholder-class="add-receiver__input_placeholder"
                            />
                        </view>
                    </view>
                </view>
                <!-- 本地发送 -->
                <view v-if="sendType==='localSend'">
                    <!-- 刷脸签署 -->
                    <view class="add-receiver__item" v-if="canSetFaceSign">
                        <view><text class="add-receiver__unnecessary">*</text>必须刷脸签署</view>
                        <switch
                            :checked="recipient.faceVerify"
                            @change="switchChange('faceVerify', $event)"
                            class="add-receiver__switch_btn"
                            color="#127FD2"
                        />
                    </view>
                    <!-- 必须手写签署 -->
                    <view class="add-receiver__item" v-if="canMustWriteSign">
                        <view><text class="add-receiver__unnecessary">*</text>必须手写签署</view>
                        <switch
                            :checked="recipient.forceHandWrite"
                            @change="switchChange('forceHandWrite', $event)"
                            class="add-receiver__switch_btn"
                            color="#127FD2"
                        />
                    </view>
                    <!-- 开启手写识别 -->
                    <!-- <view class="add-receiver__item add-receiver__write" v-if="canHandWritingRecognition">
                        <view><text class="add-receiver__unnecessary">*</text>开启手写识别</view>
                        <switch
                            :checked="recipient.handWritingRecognition"
                            @change="switchChange('handWritingRecognition', $event)"
                            color="#127FD2"
                            class="add-receiver__switch_btn"
                        />
                    </view> -->
                </view>

                <!-- 模板发送 -->
                <view v-else>
                    <!--签署校验-->
                    <view v-for="(value, key) in recipient.signerConfig" :key="key">
                        <view v-if="value && signConfigMap.includes(key)" class="add-receiver__item">{{ receiverConfigMap[key] }}</view>
                        <span class="add-receiver__error" v-if="value && signFaceConfigMap.includes(key) && isLimitFaceConfig">(你的合同单价过低，该功能不可用，请联系上上签协商)</span>
                    </view>

                    <!--采集材料-->
                    <view v-for="(value, key) in recipient.signerConfig" :key="key">
                        <view v-if="value && attachmentConfigMap.includes(key)" class="add-receiver__item">{{ receiverConfigMap[key] }}</view>
                    </view>
                    <view v-if="recipient.signerConfig.attachmentRequired">
                        <view class="add-receiver__item"><text :class="item.necessary ? 'add-receiver__necessary' : 'add-receiver__unnecessary'">*</text>{{ '添加合同附属资料' }}</view>
                        <view v-for="(item, index) in recipient.attachRequires" :key="index" class="add-receiver__wrapper add-receiver__item add-receiver__item-child">
                            <view>{{ item.name }}</view>
                            <view>{{ item.necessary ? '必填': '选填' }}</view>
                        </view>
                    </view>

                    <!--其他-->
                    <view v-for="(value, key) in recipient.signerConfig" :key="key">
                        <view v-if="value && otherConfigMap.includes(key)" class="add-receiver__item">{{ receiverConfigMap[key] }}</view>
                    </view>
                    <view class="add-receiver__wrapper add-receiver__item" v-if="!recipient.noticeConfig.needNotice || recipient.noticeConfig.sendNoticeLanguage">
                        <view v-if="!recipient.noticeConfig.needNotice"><text class="add-receiver__unnecessary">*</text>{{ '关闭短信/邮件通知' }}</view>

                        <template v-if="recipient.noticeConfig.needNotice && recipient.noticeConfig.sendNoticeLanguage">
                            <view><text class="add-receiver__unnecessary">*</text>{{ '使用外文通知' }}</view>
                            <view><text class="add-receiver__unnecessary">*</text>{{ recipient.noticeConfig.sendNoticeLanguage === 'JA' ? '日语' : '英语' }}</view>
                        </template>
                    </view>
                </view>
            </view>

            <view class="add-receiver__block" v-if="sendType!=='localSend' && recipient.signerConfig.noticeSigning">
                <PrivateMessage></PrivateMessage>
            </view>
        </view>

        <view slot="footer" class="add-receiver__btn">
            <view class="add-receiver__btn-delete" @click="handleDelete" v-if="hasModifyReceiverRight">{{ ifAddReceiver ? '取消' : '删除' }}</view>
            <view class="add-receiver__btn-confirm" @click="handleSubmit">确定</view>
        </view>
    </xd-page>
</template>
<script>
import { getLocalSendReceiverInfo, getTemplateSendReceiverInfo } from 'const/receiver';
import { RECEIVER_CONFIG_MAP, SIGN_CONFIG, ATTACHMENT_CONFIG, OTHER_CONFIG, NO_SUPPORT_SIGN_TYPE, FACE_CONFIG } from 'const/receiverConfig';
import { mapState, mapMutations } from 'vuex';
import { getAccountEntList, getAccountPersonList } from 'api/address';
import xdPage from '@/components/xdPage/index.vue';
import regRules from 'utils/regs';
import { checkEntNameFormat } from 'utils/reg';
import { uniCombox } from '@dcloudio/uni-ui';
import PrivateMessage from 'src/components/privateMessage/index.vue';
const SIGN_TYPE = [{
    name: '盖章',
    value: 'SEAL',
}, {
    name: '签字',
    value: 'SIGNATURE',
}, {
    name: '企业签字',
    value: 'ENTERPRISE_SIGNATURE',
},  {
    name: '盖章并签字',
    value: 'SEAL_AND_SIGNATURE',
}, {
    name: '抄送',
    value: 'CC_USER',
}];
export default {
    components: {
        xdPage,
        uniCombox,
        PrivateMessage,
    },
    data() {
        return {
            recipient: {},
            selectSignIndex: 0,
            receiverLoading: false,
            receiverConfigMap: RECEIVER_CONFIG_MAP,
            signConfigMap: SIGN_CONFIG,
            signFaceConfigMap: FACE_CONFIG,
            attachmentConfigMap: ATTACHMENT_CONFIG,
            otherConfigMap: OTHER_CONFIG,
            noSupportSignType: NO_SUPPORT_SIGN_TYPE,
            noSupportMap: {
                'CONFIRMATION_REQUEST_SEAL': '业务核对章',
            },
            ifAddReceiver: true,
            showCCBtn: false,
            rawEmpty: {
                role: false,
                entUserName: false, // 企业经办人
            },
            canChoseEnterprises: [],
            selectSignWay: [],
        };
    },
    computed: {
        ...mapState('send', ['receiverList', 'fromAddress', 'sendType']),
        ...mapState('user', ['featureIds', 'commonHeaderInfo']),
        ...mapState('template', ['templatePermissions', 'isLimitFaceConfig']),
        hasModifyReceiverRight() {
            return this.sendType === 'localSend' || (this.sendType === 'templateSend' && this.templatePermissions.modifyReceiver);
        },
        fieldConditionEditable() {
            const templateRoleExistInitInfo = this.recipient.templateRoleExistInitInfo || {};
            return {
                enterpriseName: this.sendType === 'localSend' ? false : templateRoleExistInitInfo.enterpriseName,
                idNumber: this.sendType === 'localSend' ? false : templateRoleExistInitInfo.idNumber,
                userAccount: this.sendType === 'localSend' ? false : templateRoleExistInitInfo.userAccount,
                userName: this.sendType === 'localSend' ? false : templateRoleExistInitInfo.userName,
            };
        },
        entUserName: {
            get() {
                return this.sendType === 'localSend' ? this.recipient.userName : this.recipient?.userInfo?.userName;
            },
            set(val) {
                this.sendType === 'localSend' ? this.recipient.userName = val : this.recipient.userInfo.userName = val;
                console.log('entUserName = ', val);
                // CFD-22252: 经办人姓名与经办人名称同步entUserName
                this.recipient.realNameAuthentication.entUserName = val;
            },
        },
        // 模板发送-经办人姓名
        recipientUserName: {
            get() {
                return this.recipient?.realNameAuthentication?.entUserName;
            },
            set(val) {
                this.recipient.realNameAuthentication.entUserName = val;
                this.entUserName = val; // 经办人姓名与经办人名称同步entUserName
            },
        },
        // 模板发送-经办人身份证号
        recipientIdNumber: {
            get() {
                return this.recipient?.realNameAuthentication?.idNumber;
            },
            set(val) {
                this.recipient.realNameAuthentication.idNumber = val;
            },
        },
        idNumber: {
            get() {
                return this.sendType === 'localSend' ? this.recipient.idNumberForVerify : this.recipient?.realNameAuthentication?.idNumber;
            },
            set(val) {
                this.sendType === 'localSend' ? this.recipient.idNumberForVerify = val : this.recipient.realNameAuthentication.idNumber = val;
            },
        },
        receiverUserName: {
            get() {
                return this.sendType === 'localSend' ? this.recipient?.userName : this.recipient?.userInfo?.userName;
            },
            set(val) {
                if (this.sendType === 'localSend') {
                    this.recipient.userName = val;
                } else {
                    this.recipient.userInfo.userName = val;
                }
            },
        },
        receiverUserAccount: {
            get() {
                return this.sendType === 'localSend' ? this.recipient?.userAccount : this.recipient?.userInfo?.userAccount;
            },
            set(val) {
                if (this.sendType === 'localSend') {
                    this.recipient.userAccount = val;
                } else {
                    this.recipient.userInfo.userAccount = val;
                }
            },
        },
        receiverEnterpriseName: {
            get() {
                return this.sendType === 'localSend' ? this.recipient?.enterpriseName : this.recipient?.userInfo?.enterpriseName;
            },
            set(val) {
                if (this.sendType === 'localSend') {
                    this.recipient.enterpriseName = val;
                } else {
                    this.recipient.userInfo.enterpriseName = val;
                }
            },
        },
        // 企业经办人实名 requireEnterIdentityAssurance 个人实名 requireIdentityAssurance
        requireIdentity: {
            get() {
                const isEntSigner = this.recipient.userType === 'ENTERPRISE';
                const valueKey = isEntSigner ? 'requireEnterIdentityAssurance' : 'requireIdentityAssurance';
                if (this.sendType === 'localSend') {
                    return this.recipient[valueKey];
                }
                return this.recipient?.realNameAuthentication[valueKey];
            },
            set(val) {
                const isEntSigner = this.recipient.userType === 'ENTERPRISE';
                const valueKey = isEntSigner ? 'requireEnterIdentityAssurance' : 'requireIdentityAssurance';
                if (this.sendType === 'localSend') {
                    this.recipient[valueKey] = val;
                } else {
                    this.recipient.realNameAuthentication[valueKey] = val;
                }
            },
        },
        ifMulAccountProxy() {
            return  !this.isPerson && this.recipient.proxyClaimer?.ifProxyClaimer && (this.recipient.proxyClaimer.proxyClaimerAccounts || []).length;
        },
        ifProxy() {
            return !this.isPerson && this.recipient.proxyClaimer?.ifProxyClaimer && !(this.recipient.proxyClaimer.proxyClaimerAccounts || []).length;
        },
        canSetFaceSign() {
            return this.recipient.userType === 'PERSON' && this.recipient.requireIdentityAssurance &&
                this.recipient.receiverType === 'SIGNER' && this.featureIds.indexOf('15') > -1;
        },
        canHandWritingRecognition() {
            return this.recipient.userType === 'PERSON' && this.recipient.forceHandWrite;
        },
        canMustWriteSign() {
            return this.recipient.userType === 'PERSON' &&  this.recipient.receiverType === 'SIGNER';
        },
        isPerson() {
            return this.commonHeaderInfo.userType === 'Person';
        },
    },
    methods: {
        ...mapMutations('send', ['updateReceiverList', 'addReceiverList', 'deleteReceiverList', 'setFromAddress', 'setCurReceiver']),
        removeFile(index, type) {
            this.recipient.communicateInfo.signInstructionDocumentInfo[type].splice(index, 1);
        },
        initRawEmptyStatus() {
            this.rawEmpty.role = this.sendType === 'localSend' ? false : !!this.recipient.roleName;
            this.rawEmpty.entUserName = this.sendType === 'localSend' ? false : (!!(this.recipient.userType === 'ENTERPRISE' && this.recipient.realNameAuthentication.entUserName));
        },
        createBlankReceiver(userType) {
            const routeOrder = this.receiverList.length + 1;
            const defaultReceiverInfo = this.sendType === 'localSend' ? getLocalSendReceiverInfo({ userType, routeOrder }) : getTemplateSendReceiverInfo({ userType, routeOrder });
            this.addReceiverList(defaultReceiverInfo);
            return this.receiverList.length - 1;
        },
        noSupportTypeCheck() {
            if (this.recipient.userType === 'PERSON') {
                if (this.recipient.signerConfig.signType === 'SCAN_CODE_SIGNATURE') {
                    this.$toast.error('暂不支持扫码签字类型的模板发送');
                    setTimeout(() => {
                        uni.navigateBack({ delta: 3 });
                    }, 3000);
                }
            } else if (this.recipient.userType === 'ENTERPRISE') {
                if (this.recipient.receiverType === 'EDITOR') {
                    this.$toast.error('暂不支持配置了补全人的模板发送');
                    setTimeout(() => {
                        uni.navigateBack({ delta: 3 });
                    }, 3000);
                } else if (this.recipient.receiverType === 'SIGNER') {
                    if (this.noSupportSignType.includes(this.recipient.signerConfig.signType)) {
                        this.$toast.error(`暂不支持${this.noSupportMap[this.recipient.signerConfig.signType]}类型的模板发送`);
                        setTimeout(() => {
                            uni.navigateBack({ delta: 3 });
                        }, 3000);
                    } else if (this.recipient?.multiEmployeeSignaturesConfig?.useMultiEmployeeSignatures) {
                        this.$toast.error(`暂不支持开启了企业多人签字类型的模板发送`);
                        setTimeout(() => {
                            uni.navigateBack({ delta: 3 });
                        }, 3000);
                    }
                }
            }
        },
        initSelectSignWay() {
            if (this.sendType === 'localSend') {
                this.selectSignWay = this.recipient.userType === 'ENTERPRISE' ? SIGN_TYPE.filter((item) => ['SEAL', 'CC_USER'].includes(item.value)) : SIGN_TYPE.filter((item) => ['SIGNATURE', 'CC_USER'].includes(item.value));
            } else {
                if (this.showCCBtn || this.recipient.mustCCUser) {
                    this.selectSignWay =  SIGN_TYPE.filter((item) => ['CC_USER'].includes(item.value));
                } else {
                    this.selectSignWay =  this.recipient.userType === 'ENTERPRISE' ? SIGN_TYPE.filter((item) => ['SEAL', 'CC_USER', 'ENTERPRISE_SIGNATURE', 'SEAL_AND_SIGNATURE'].includes(item.value)) : SIGN_TYPE.filter((item) => ['SIGNATURE', 'CC_USER'].includes(item.value));
                }
            }
        },
        initSelectSignIndex() {
            if (this.sendType === 'templateSend') {
                const index = this.selectSignWay.findIndex(item => item.value === (this.recipient.signerConfig.signType || this.recipient.receiverType));
                this.selectSignIndex = (this.recipient.mustCCUser || index === -1) ? 0 : index;
            } else {
                const index = this.selectSignWay.findIndex(item => item.value === (this.recipient.signType || this.recipient.receiverType));
                this.selectSignIndex = index === -1 ? 0 : index;
            }
        },
        getCurReceiver(query) {
            if (query.createUserType) {
                this.receiverIndex = this.createBlankReceiver(query.createUserType);
            } else {
                this.receiverIndex = query.receiverIndex;
            }
            this.recipient = this.receiverList[this.receiverIndex];
            this.setCurReceiver(this.recipient);
            this.initSelectSignWay();
            this.initSelectSignIndex();
            this.updateCCUserConfig();
            if (this.sendType === 'templateSend') {
                this.noSupportTypeCheck();
            }
        },
        // 签约方式选择
        handleSelectSignWay(e) {
            this.selectSignIndex = e.target.value;
            this.updateCCUserConfig();
        },
        updateCCUserConfig() {
            const value = this.selectSignWay[this.selectSignIndex].value;
            this.recipient.receiverType = (value === 'CC_USER' ? 'CC_USER' : 'SIGNER');
            let updateData = {};
            if (this.sendType === 'localSend') {
                this.recipient.signType = (value === 'CC_USER' ? null : value);
                updateData = {
                    receiverType: this.recipient.receiverType,
                    signType: this.recipient.signType,
                    participationType: value === 'CC_USER' ? 'CC_USER' : this.recipient.signType,
                };
            } else {
                this.recipient.signerConfig.signType = (value === 'CC_USER' ? '' : value);
                updateData = {
                    receiverType: this.recipient.receiverType,
                    signerConfig: {
                        ...this.recipient.signerConfig,
                        signType: this.recipient.signerConfig.signType,
                    },
                };
            }
            this.updateReceiverList({
                receiverIndex: this.receiverIndex,
                updateData,
            });
        },
        // 输入企业名字更新
        handleEnterpriseNameUpdate(entName) {
            const entId = (this.recipient?.enterprises || []).find(item => item.entName === entName)?.entId || '';
            this.receiverEnterpriseName = entName;
            const param = {
                enterpriseName: entName,
                enterpriseId: entId,
            };
            this.updateReceiverList({
                receiverIndex: this.receiverIndex,
                updateData: this.sendType === 'localSend' ? param : { userInfo: { ...this.recipient.userInfo, ...param } },
            });
        },
        switchChange(key, e) {
            if (key === 'requireIdentityAssurance') {
                this.recipient.userType === 'ENTERPRISE' ? (this.recipient.requireEnterIdentityAssurance =
                    e.detail.value) : (this.recipient.requireIdentityAssurance = e.detail.value);
            } else if (this.sendType === 'localSend' ||
                (this.sendType !== 'localSend' && key !== 'realNameAuthentication')) {
                this.recipient[key] = e.detail.value;
            } else {
                this.recipient[key] = this.recipient.userType === 'ENTERPRISE' ? {
                    ...this.recipient.realNameAuthentication,
                    requireEnterIdentityAssurance: e.detail.value,
                } : {
                    ...this.recipient.realNameAuthentication,
                    requireIdentityAssurance: e.detail.value,
                };
            }
            this.updateReceiverList({
                receiverIndex: this.receiverIndex,
                updateData: {
                    [key]: this.recipient[key],
                },
            });
        },
        checkAccount() {
            // 先校验
            if (this.sendType === 'templateSend' && (this.ifProxy || this.ifMulAccountProxy)) {
                return true;
            }
            if (!this.receiverUserAccount) {
                this.$toast.error('接收手机号/邮箱不能为空');
                return false;
            }
            // 账号格式不对
            if (this.receiverUserAccount && !regRules.userAccount.test(this.receiverUserAccount)) {
                this.$toast.error('格式不正确，请输入正确的手机号/邮箱');
                return false;
            }
            return true;
        },
        checkInfoFormat() {
            const entName = this.receiverEnterpriseName;
            if (this.recipient.userType === 'ENTERPRISE' && !entName) {
                this.$toast.error('公司名称不能为空');
                return false;
            }
            // 签约角色不能为空
            if (this.sendType === 'templateSend' && !this.recipient.roleName) {
                this.$toast.error('签约角色不能为空');
                return false;
            }
            // 经办人姓名和身份证号不能为空
            if (this.sendType === 'templateSend') {
                const noUserName = this.recipient.realNameAuthentication.requireUserName && ((this.recipient.userType === 'ENTERPRISE' && !this.recipient.realNameAuthentication.entUserName) || (this.recipient.userType === 'PERSON' && !this.recipient?.userInfo?.userName));
                const noIdNumber = ((this.recipient.userType === 'ENTERPRISE' && this.recipient.realNameAuthentication.requireEnterIdentityAssurance) || this.recipient.userType === 'PERSON') && this.recipient.realNameAuthentication.requireIdNumber && !this.recipient.realNameAuthentication.idNumber;
                if (noUserName) {
                    this.$toast.error(this.recipient.userType === 'ENTERPRISE' ? '经办人姓名不能为空' : '姓名不能为空');
                    return false;
                } else if (noIdNumber) {
                    this.$toast.error(this.recipient.userType === 'ENTERPRISE' ? '经办人身份证号不能为空' : '身份证号不能为空');
                    return false;
                }
            }
            // 企业名称格式不对
            if (this.receiverEnterpriseName) {
                const errMsg = checkEntNameFormat(this.receiverEnterpriseName);
                if (errMsg) {
                    this.$toast.error(errMsg);
                    return false;
                }
            }

            // 姓名不对
            if (this.receiverUserName && !regRules.userName.test(this.receiverUserName)) {
                this.$toast.error('请输入正确的姓名');
                return false;
            }

            // 经办人姓名不对
            if (this.recipient?.realNameAuthentication?.entUserName && !regRules.userName.test(this.recipient.realNameAuthentication.entUserName)) {
                this.$toast.error('请输入正确的经办人姓名');
                return false;
            }

            // 身份证号不对
            if (this.recipient?.realNameAuthentication?.idNumber && !regRules.IDCardReg.test(this.recipient.realNameAuthentication.idNumber)) {
                this.$toast.error('请输入正确的身份证');
                return false;
            }

            // 本地发起的身份号不对
            if (this.sendType === 'localSend' && this.recipient?.idNumberForVerify && !regRules.IDCardReg.test(this.recipient.idNumberForVerify)) {
                this.$toast.error('请输入正确的身份证');
                return false;
            }
            return true;
        },
        checkPrivateLetter() {
            if (this.sendType !== 'localSend' && this.recipient.signerConfig.noticeSigning) {
                // #ifdef MP-WEIXIN
                const { signInstructionDocumentInfo, signInstructionOriginDocumentInfo, signInstructionZipInfo } = this.recipient.communicateInfo;
                const needChecksignInstructionDocumentInfo = (signInstructionDocumentInfo.ifDisplayConfig && signInstructionDocumentInfo.ifRequired);
                if (needChecksignInstructionDocumentInfo && signInstructionDocumentInfo.privateLetterFileInfos.length < 1) {
                    this.$toast.error('添加文档配置了必填,请点击添加文档上传');
                    return false;
                }
                const needChecksignInstructionOriginDocumentInfo = (signInstructionOriginDocumentInfo.ifDisplayConfig && signInstructionOriginDocumentInfo.ifRequired);
                if (needChecksignInstructionOriginDocumentInfo && signInstructionOriginDocumentInfo.privateLetterFileInfos.length < 1) {
                    this.$toast.error('添加源文件配置了必填,请点击添加源文件上传');
                    return false;
                }

                const needChecksignInstructionZipInfo = (signInstructionZipInfo.ifDisplayConfig && signInstructionZipInfo.ifRequired);
                if (needChecksignInstructionZipInfo && !signInstructionZipInfo.instructionsAppendixId) {
                    this.$toast.error('添加压缩文件配置了必填,请点击压缩文件上传');
                    return false;
                }
                // #endif
                // #ifdef MP-ALIPAY
                const { signInstructionDocumentInfo: { ifRequired: docIfRequired }, signInstructionOriginDocumentInfo: { ifRequired: originDocIfRequired }, signInstructionZipInfo: { ifRequired: zipIfRequired } } = this.recipient.communicateInfo;
                const ifRequired = docIfRequired || originDocIfRequired || zipIfRequired;
                if (ifRequired) {
                    this.$toast.error('暂不支持带必填项的签约须知的模板发送');
                    return false;
                }
                // #endif
                return true;
            }
            return true;
        },
        onBlurUserAccount() {
            if (!this.checkAccount()) {
                return;
            }
            this.handleAccount();
        },
        handleAccount() {
            const recipient = this.recipient;
            const isPerson = recipient.userType.toLowerCase() === 'person';
            // 特殊处理：清空头像，查到了再赋值
            recipient.photoHref = '';
            // 由于该联想接口是在input框blur时触发的，为了防止点击企业选择picker的时候接口还未返回数据导致弹出窗空白，加一个loading
            this.receiverLoading = true;
            this.$toast.loading({ mask: true });
            const getAccountFun = isPerson ? getAccountPersonList : getAccountEntList;
            getAccountFun({ userAccount: this.receiverUserAccount })
                .then((res) => {
                    const { data: data } = res;
                    // 未查询到账号相关信息
                    if (!data.account) {
                        this.receiverLoading = false;
                        if (!isPerson) {
                            this.recipient.enterprises = [];
                        }
                        this.$toast.hideLoading();
                        return;
                    }
                    let searchedData = null;
                    if (isPerson) {
                        if (this.sendType === 'localSend') {
                            searchedData = {
                                photoHref: data.portraitUrl,
                                userName: data.fullName,
                                userId: data.userId,
                                fullName: data.fullName,
                                userAccount: data.account,
                            };
                        } else {
                            searchedData = {
                                photoHref: data.portraitUrl,
                                userInfo: {
                                    ...this.recipient.userInfo,
                                    ...{
                                        userName: data.fullName,
                                        userId: data.userId,
                                        fullName: data.fullName,
                                        userAccount: data.account,
                                    },
                                },
                            };
                        }
                    } else {
                        if (this.sendType === 'localSend') {
                            searchedData = {
                                enterprises: data.enterprises,
                                userName: data.userName || data.fullName,
                                userId: data.userId,
                                fullName: data.fullName,
                                userAccount: data.account,
                            };
                        } else {
                            searchedData = {
                                enterprises: data.enterprises,
                                userInfo: {
                                    ...this.recipient.userInfo,
                                    ... {
                                        userName: data.userName || data.fullName,
                                        userId: data.userId,
                                        fullName: data.fullName,
                                        userAccount: data.account,
                                    },
                                },
                            };
                        }
                    }
                    const updateData = {
                        ...recipient,
                        ...searchedData,
                    };
                    Object.assign(recipient, updateData);
                    this.canChoseEnterprises =  (this.recipient?.enterprises || []).map(item =>  item.entName);
                    this.receiverLoading = false;
                    this.$toast.hideLoading();
                });
        },
        handleDelete() {
            if (!this.ifAddReceiver) {
                this.deleteReceiverList(this.receiverIndex);
            }
            uni.navigateBack({ delta: 1 });
        },
        handleSubmit() {
            if (!this.checkAccount() || !this.checkPrivateLetter() || !this.checkInfoFormat()) {
                return;
            }
            // 如果该recipient是 新建/初次填写 状态，则将isBlank 字段去除
            if (this.recipient.isBlank) {
                delete this.recipient['isBlank'];
            }
            this.updateReceiverList({
                receiverIndex: this.receiverIndex,
                updateData: this.recipient,
                rewrite: true,
                mustCCUser: this.showCCBtn, // 必须是抄送方，不能变更为其他
            });

            uni.navigateBack({
                delta: 1,
            });
        },
        goAddressBook() {
            uni.navigateTo({
                url: `/subSendViews/address/index?receiverIndex=${this.receiverIndex}&userType=${this.recipient.userType}`,
            });
        },
        async handleFromAddress() {
            this.recipient = this.receiverList[this.receiverIndex];
            this.setCurReceiver(this.recipient);
            if (this.fromAddress) {
                /* 如果判断为联系人地址簿跳转的，进行处理 */
                if (this.fromAddress === 'inner') {
                    /* 内部联系人跳转，则自动调用联想并填充 */
                    this.onBlurUserAccount();
                } else if (this.fromAddress === 'outer') {
                    /* 从外部联系人跳转 */
                    const { userType } = this.recipient;
                    const userAccount = this.sendType === 'localSend' ? this.recipient.userAccount : this.recipient.userInfo.userAccount;
                    if (userType === 'ENTERPRISE') {
                        /* 如果是企业，判断该企业名是否存在于 通过联想查到的总表 中；存在的话则改用picker */
                        const { data: enterprises } = await getAccountEntList({ userAccount });
                        this.recipient.enterprises = enterprises;
                        if (!this.receiverEnterpriseName) {
                            this.receiverEnterpriseName = enterprises && enterprises[0] ? enterprises[0].entName : '';
                        }
                    }
                }
                /* 处理完毕后，去掉地址簿来源标识 */
                this.setFromAddress(null);
            }
        },
    },
    onLoad(query) {
        this.ifAddReceiver = !!query.createUserType;
        if (this.sendType === 'templateSend') {
            this.showCCBtn = (query.showCCBtn === 'true' || query.showCCBtn === true);
        }
        this.getCurReceiver(query);
        this.initRawEmptyStatus();

        uni.setNavigationBarTitle({
            title: `添加签约${this.recipient.userType === 'ENTERPRISE' ? '企业' : '个人'}`,
        });
    },
    async onShow() {
        this.handleFromAddress();
    },
    onUnload() {
        /* 如果当前签署人为新建(首次填值)，则recipient含有isBlank字段；只有点击【保存】按钮，该isBlank才会被清除 */
        if (this.recipient?.isBlank) {
            this.deleteReceiverList(this.receiverIndex);
        }
    },
};
</script>

<style lang="scss">
.add-receiver {
    font-size: 30rpx;
    height: 100%;
    overflow-y: scroll;
    background-color: $--background-color-base;
    &__necessary {
        color: red;
        margin-right: 6rpx;
    }
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
    &__block{
        background-color: $--color-white;
        margin: 20rpx 0;
    }
    &__title {
        height: 90rpx;
        line-height: 90rpx;
        color: $--color-info;
        padding: 0 30rpx;
        border-top: 1rpx solid $--border-color-lighter;
    }
    &__wrapper {
        display: flex;
        justify-content: space-between;
    }
    &__error{
        padding-left: 30rpx;
        margin-top: -30rpx;
        display: block;
        font-size: 22rpx;
        color: #FF5500;
        padding-bottom: 36rpx;
    }
    &__item {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-top: 1rpx solid $--border-color-lighter;
        position: relative;
        &>view:nth-child(1) {
            width: 300rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &>view:nth-child(2) {
            height: 100rpx;
            line-height: 100rpx;
            width: calc(100% - 300rpx);
            text-align: right;
            input {
                height: 100%;
                width: 100%;
            }
        }
    }
    &__item-child {
        padding: 0 30rpx 0 80rpx;
        color: $--color-info;
    }
    &__no-wrap {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &__right {
        width: calc(100% - 285rpx);
        text-align: right;
    }
    &__input {
        display: flex;
        flex-direction: row;
        align-items: center;
        .icon-ic_contacts {
            color: $--color-primary;
            font-size: 35rpx;
            margin-left: 6rpx;
        }
    }
    &__item-info {
        height: 300rpx;
        line-height: 100rpx;
        padding: 0 30rpx 0 80rpx;
        border-top: 1rpx solid $--border-color-lighter;
        text-align: left !important;
    }
    &__way {
        margin: 20rpx 0;
        border: none;
    }
    &__first {
        border: none;
        margin-top: 20rpx;
    }
    &__auth {
        border-top: none;
    }
    input {
        text-align: right;
    }
    &__input_placeholder {
        color: $--color-text-placeholder;
    }
    &__switch_btn {
        transform: scale(0.5);
        position: absolute;
        right: 0;
    }
    &__btn {
        height: 90rpx;
        line-height: 90rpx;
        font-size: 32rpx;
        margin-top: 20rpx;
        display: flex;
        &-confirm {
            background: $--color-primary;
            color: $--color-white;
            text-align: center;
            flex: 2;
            border-radius: 8rpx;
            margin: 0 20rpx;
        }
        &-delete {
            background: #dfeeff;
            color: #1787dd;
            text-align: center;
            flex: 1;
            margin: 0 20rpx;
            border-radius: 8rpx;
        }
    }
    .uni-combox {
        padding: 0;
        border: none;
        .uni-combox__input-box {
            input {
                height: 100rpx !important;
                line-height: 100rpx !important;
            }
            .uni-combox__input-plac {
                color: $--color-text-placeholder;
                font-size: 30rpx;
            }
        }

        .uni-icons {
            visibility: hidden;
            margin-left: -30rpx;
        }
        .uni-popper__arrow {
            display: none !important;
        }
    }
}

</style>
