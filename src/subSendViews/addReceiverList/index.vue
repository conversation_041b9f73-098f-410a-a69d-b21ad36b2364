<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <view class="add-receiver-list">
            <view>
                <view class="add-receiver-list__title">合同标题</view>
                <view class="add-receiver-list__item">
                    <input
                        v-model="noticeContractName"
                        class="add-receiver-list__input"
                        placeholder-class="add-receiver-list__input_placeholder"
                        placeholder="请输入(必填)"
                        maxlength="100"
                        :disabled="templateDocList.length === 1"
                        @input="contractNameChange"
                    />
                </view>
            </view>
            <view v-if="senders.length>0">
                <view class="add-receiver-list__title">当前发件方</view>
                <picker
                    v-if="senders.length>1"
                    @change="handleSender"
                    :value="senderIndex"
                    range-key="enterpriseName"
                    :range="senders"
                >
                    <view class="add-receiver-list__input">{{ senders[senderIndex].enterpriseName }}<text class="icon-ic_forward"></text></view>
                </picker>
                <view v-else class="add-receiver-list__input">{{ senders[senderIndex].enterpriseName }}</view>

            </view>
            <view
                class="add-receiver-list__wrapper"
                :style="{'height': `calc(100% - ${(hasModifyReceiverRight ||hasAddCCReceiverRight) ? '455rpx' : '0rpx'})`}"
            >
                <view class="add-receiver-list__item">
                    <view class="add-receiver-list__title">签约方</view>
                </view>
                <view class="add-receiver-list__sign-order">
                    <view>顺序签署</view>
                    <switch :checked="signOrderly" @change="switchChange" class="switchBtn" color="#127fd2" />
                </view>
                <SignerList
                    :hasModifyReceiverRight="hasModifyReceiverRight"
                    :list="filterReceiverList"
                    :isMoveAble="signOrderly"
                    @change="onDragSortChange"
                    @delete="onDeleteReceiver"
                ></SignerList>
                <AddReceiverBtn
                    v-if="hasModifyReceiverRight || hasAddCCReceiverRight"
                    :hasModifyReceiverRight="hasModifyReceiverRight"
                    :hasAddCCReceiverRight="hasAddCCReceiverRight"
                ></AddReceiverBtn>
            </view>
        </view>
        <view v-if="!hideNextBtn" class="add-receiver-list__btn" slot="footer" @click="handleSubmit">
            下一步
        </view>
    </xd-page>
</template>

<script>
import { getDraftNoticeContractName, getDraftSenders, getDraftReceivers } from 'api/template.js';
import { mapState, mapMutations, mapActions } from 'vuex';
import _get from 'lodash/get';
import SignerList from 'components/signerList';
import AddReceiverBtn from 'components/addReceiverBtn';
import xdPage from 'components/xdPage/index.vue';
import { saveDraftReceiverInfo } from 'src/api/template.js';
import debounce from 'src/utils/debounce.js';
import { getTemplateSendReceiverInfo } from 'const/receiver';
export default {
    components: {
        SignerList,
        xdPage,
        AddReceiverBtn,
    },
    data() {
        return {
            noticeContractName: '',
            curSenderName: '',
            hideNextBtn: false,
            senderIndex: -1,
            senders: [],
            showGuideSender: false,
        };
    },
    computed: {
        ...mapState('template', ['draftId', 'templatePermissions', 'templateDocList']),
        ...mapState('send', ['curEntId', 'receiverList', 'draftInfo', 'signOrderly', 'sendType']),
        ...mapState('user', ['featureIds', 'commonHeaderInfo']),
        hasModifyReceiverRight() {
            return this.sendType === 'localSend' || (this.sendType === 'templateSend' && this.templatePermissions.modifyReceiver);
        },
        hasAddCCReceiverRight() {
            return this.sendType === 'localSend' || (this.sendType === 'templateSend' && this.templatePermissions.addCCReceiver);
        },
        // 若该签署人为新建状态则不显示
        filterReceiverList() {
            return this.receiverList.filter(item => {
                return !item.isBlank;
            });
        },
    },
    watch: {
        senderIndex(val) {
            this.draftInfo.senderInfo = this.senders[val];
        },
    },
    methods: {
        ...mapMutations('send', ['setReceiverList', 'setSignOrderly', 'setDraftInfo', 'addReceiverList', 'deleteReceiverList']),
        ...mapMutations(['setToastParam']),
        ...mapActions(['setLoading']),
        ...mapActions('template', ['getIsLimitFaceConfig']),
        handleSender(e) {
            this.senderIndex = e.target.value;
        },
        switchChange(e) {
            this.setSignOrderly(e.target.value);
        },
        getContractTitle() {
            getDraftNoticeContractName({ draftId: this.draftId })
                .then(({ data }) => {
                    this.noticeContractName = _get(data, 'templateName', '');
                });
        },
        contractNameChange(e) {
            const val = e.detail.value;
            if (/【|\[|】|\]]/.test(val)) {
                this.$toast.error('合同标题请不要包含特殊字符');
            } else if (!val.length) {
                this.$toast.error('合同标题不能为空');
            }
        },
        getSenderName() {
            getDraftSenders({ draftId: this.draftId }).then(({ data }) => {
                this.senders = _get(data, this.draftInfo.proxySend ? 'proxySenders' : 'senders', []).filter(item => item.allowAsSender);
                // 有多发件方可以选增加待确认选项
                if (this.senders.length > 1) {
                    this.senders.unshift({ enterpriseName: '待确认' });
                }
                this.senderIndex = this.senders.findIndex(item => item.enterpriseId === this.draftInfo.senderInfo.enterpriseId);
                // 未找到已选中的则默认第一条待确认，仅有1条则直接选中
                if (this.senderIndex < 0) {
                    this.senderIndex = 0;
                }
            });
        },
        noSupportTypeCheck(receiverInfos) {
            const noSupportSignType = ['SCAN_CODE_SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'];
            const signTypeMap = {
                'SCAN_CODE_SIGNATURE': '扫码签字',
                'CONFIRMATION_REQUEST_SEAL': '业务核对章',
            };
            receiverInfos.forEach(item => {
                if (noSupportSignType.includes(item.signerConfig.signType)) {
                    this.$toast.error(`暂不支持${signTypeMap[item.signerConfig.signType]}类型的模板发送`);
                    this.hideNextBtn = true;
                    setTimeout(() => {
                        uni.navigateBack({ delta: 2 });
                    }, 3000);
                } else if (item.receiverType === 'EDITOR') {
                    this.$toast.error('暂不支持配置了补全人的模板发送');
                    this.hideNextBtn = true;
                    setTimeout(() => {
                        uni.navigateBack({ delta: 2 });
                    }, 3000);
                }
            });
        },
        getReceivers() {
            return getDraftReceivers({ draftId: this.draftId }).then(({ data }) => {
                this.setSignOrderly(data.signOrderly);
                this.setDraftInfo(data);
                this.noSupportTypeCheck(data.receiverInfos);
                this.setReceiverList(data.receiverInfos.map(item => {
                    // 多账号
                    if (item.proxyClaimer && item.proxyClaimer.proxyClaimerAccounts.length >= 2) {
                        item.userInfo.userAccount = item.proxyClaimer.proxyClaimerAccounts.join(';');
                    }
                    // 个人签署必须实名
                    if (item.userType === 'PERSON' && this.featureIds.includes('171')) {
                        item.realNameAuthentication.requireIdentityAssurance = true;
                    }
                    return item;
                }));
                return Promise.resolve(data);
            });
        },
        checkTitleAndSigners() {
            if (!this.noticeContractName) {
                this.$toast.error('未设置“合同标题”');
                return false;
            }
            if (!this.draftInfo.receiverInfos.length || !this.draftInfo.receiverInfos.some(item => item.receiverType === 'SIGNER')) {
                this.$toast.error('请至少添加一个签署人');
                return false;
            }
            if (this.senders.length && !this.senders[this.senderIndex].enterpriseId) {
                this.$toast.error('请指定当前发件方');
                return false;
            }
            return true;
        },
        hasNotFilledNecessaryFields(item) {
            if (item.userType === 'ENTERPRISE') {
                return (!item?.proxyClaimer?.ifProxyClaimer && !item.userInfo.userAccount) || !item.userInfo.enterpriseName || (this.sendType === 'templateSend' && !item.roleName) ||
                    (item?.realNameAuthentication?.requireUserName && !item.realNameAuthentication.entUserName) || // 经办人
                    (item?.realNameAuthentication?.requireEnterIdentityAssurance && ((item.realNameAuthentication.requireUserName && !item.realNameAuthentication.entUserName) || (item.realNameAuthentication.requireIdNumber && !item.realNameAuthentication.idNumber))); // 开启经办人身份校验
            } else {
                return !item.userInfo.userAccount || (this.sendType === 'templateSend' && !item.roleName) ||
                    (item?.necessaryConfig.username && !item.userInfo.userName) ||
                    (item?.realNameAuthentication?.requireIdentityAssurance && item.realNameAuthentication.requireIdNumber && !item.realNameAuthentication.idNumber);
            }
        },
        checkNecessaryFields() {
            const errors = [];
            this.receiverList.forEach((item, index) => {
                if (this.hasNotFilledNecessaryFields(item)) {
                    !errors.includes(index + 1) && errors.push(index + 1);
                }
            });
            if (errors.length) {
                this.$toast.error(`请补全第${errors.join(',')}个签约方的必填字段`);
                return false;
            }
            return true;
        },
        checkAddSender() {
            const hasSender = this.receiverList.filter(recipient => !recipient.disabled).some(receiver => {
                return receiver.userInfo.enterpriseName === this.draftInfo.senderInfo.enterpriseName;
            });
            if (this.showGuideSender && this.templatePermissions.modifyReceiver && !hasSender) {
                this.showGuideSender = false;
                uni.showModal({
                    title: '提示',
                    content: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',
                    confirmText: '添加',
                    confirmColor: '#127fd2',
                    success: (res) =>  {
                        if (res.confirm) {
                            this.addSender();
                        } else {
                            this.handleSubmit();
                        }
                    },
                });
                return false;
            }
            return true;
        },
        addSender() {
            const defaultReceiverInfo = getTemplateSendReceiverInfo({
                userType: 'ENTERPRISE',
                routeOrder: this.receiverList.length + 1,
                userInfo: {
                    enterpriseName: this.draftInfo.senderInfo.enterpriseName,
                    userAccount: this.commonHeaderInfo?.platformUser?.account,
                    userName: '',
                    userId: '',
                    enterpriseId: '',
                    employeeId: '',
                },
                roleName: '本企业',
            });
            this.addReceiverList(defaultReceiverInfo);
            uni.navigateTo({
                url: `/subSendViews/addReceiver/index?receiverIndex=${this.receiverList.length - 1}`,
            });
        },
        checkHasStamp() {
            const entSignerReceivers = this.receiverList.filter(item => item.userType === 'ENTERPRISE' &&
                item.receiverType === 'SIGNER' && item.signerConfig?.signType === 'ENTERPRISE_SIGNATURE');
            const needStamp = entSignerReceivers.filter(item => {
                return !(this.receiverList.find(receiver => receiver.userInfo.enterpriseName ===
                    item.userInfo.enterpriseName && receiver.signerConfig?.signType === 'SEAL'));
            });
            if (needStamp.length) {
                const msg = needStamp[0].userInfo.enterpriseName;
                uni.showModal({
                    title: '提示',
                    content: `设置企业签字后，签约企业${msg}还需设置盖章方式的签约角色`,
                    showCancel: false,
                    success() {},
                });
                return false;
            }
            return true;
        },
        handleSubmit: debounce(function() {
            if (!this.checkTitleAndSigners() || !this.checkNecessaryFields() || !this.checkAddSender() ||
                !this.checkHasStamp()) {
                return;
            }
            this.setLoading(true);

            saveDraftReceiverInfo({
                draftId: this.draftId,
                senderInfo: this.draftInfo.senderInfo,
                receiverInfos: this.draftInfo.receiverInfos.map(item => {
                    return {
                        ...item,
                        // CFD-24498:保持和web相同逻辑 添加了一个新字段保存经办人实名认证时的经办人名称，如果需要认证，则此字段值和经办人值相同，否则为空字符串
                        realNameAuthentication: {
                            ...item.realNameAuthentication,
                            entUserName: item.realNameAuthentication.requireEnterIdentityAssurance ? item.userInfo.userName : '',
                        },
                    };
                }),
                proxySend: this.draftInfo.proxySend,
                contractName: this.noticeContractName,
                signOrderly: this.signOrderly,
            }).then(() => {
                uni.navigateTo({
                    url: `/subSendViews/sendPointPosition/index`,
                });
            }).catch((err) => {
                let data = err.response && err.response.data;
                try {
                    data = JSON.parse(data);
                } catch (err) {
                    console.log(err);
                }
                this.setToastParam({ show: true, message: data?.message });
                setTimeout(() => {
                    this.setToastParam({ show: false, message: '' });
                }, 2000);
            })
                .finally(() => {
                    this.setLoading(false);
                });
        }),
        // 是否第一次需要提醒添加发件方为签约方
        async handleGuideSender() {
            const  { data: { value } } = await this.$http.get('/users/configs/default/FIRST_AFTER_SEND_OPTIMIZATION');
            // true表示第一次
            if (value === 'true') {
                this.showGuideSender = true;
                this.$http.post('/users/configs/FIRST_AFTER_SEND_OPTIMIZATION', {
                    name: 'FIRST_AFTER_SEND_OPTIMIZATION',
                    value: false,
                });
            }
        },
        onDragSortChange(e) {
            const list = e.data;

            list.sort((item1, item2) => {
                return item1.index - item2.index;
            });
            this.setReceiverList(list);
            this.setDraftInfo({
                ...this.draftInfo,
                receiverInfos: list,
            });
        },
        onDeleteReceiver(index) {
            this.deleteReceiverList(index);
        },
    },
    async onLoad() {
        this.getContractTitle();
        await this.getReceivers();
        this.getSenderName();
        this.handleGuideSender();
        this.getIsLimitFaceConfig();
    },
};
</script>

<style lang="scss">
.add-receiver-list {
    height: 100%;
    background-color: $--background-color-base;
    color: $--color-text-primary;
    overflow-y: hidden;
    &__title {
        height: 90rpx;
        line-height: 90rpx;
        padding: 0 30rpx;
        color: $--color-info;
    }
    &__input {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        background-color: $--color-white;
        width: 100%;
        box-sizing: border-box;
        .icon-ic_forward{
            position: absolute;
            right: 34rpx;
            color: $--color-text-secondary;
            font-size: 36rpx;
            transform: rotate(90deg);
        }
    }
    &__tip {
        position: absolute;
        right: 30rpx;
        height: 100rpx;
        line-height: 100rpx;
        color: $--color-text-placeholder;
        font-size: 28rpx;
    }
    &__input_placeholder {
        color: $--color-text-placeholder;
    }
    &__item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
    &__btn {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
    &__add {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40rpx;
        height: 100rpx;
        line-height: 100rpx;
        color: $--color-primary;
        background-color: $--color-white;
        border-bottom: 1rpx solid $--border-color-lighter;
        margin: 15rpx 0;
    }
    &__line {
        color: $--border-color-lighter;
        transform:scaleY(1.2);
    }
    &__sign-order {
        height: 100rpx;
        line-height: 100rpx;
        background-color: $--color-white;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 30rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        position: relative;
        .switchBtn {
            transform:scale(0.5);
            position: absolute;
            right: 0;
        }
    }
    &__unnecessary {
        margin-right: 6rpx;
        visibility: hidden;
    }
}
</style>
