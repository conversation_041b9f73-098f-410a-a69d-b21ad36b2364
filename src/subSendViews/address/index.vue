<template>
    <scroll-view scroll-y class="scrollY">
        <view class="tab">
            <view class="tab__item" :class="{'tab__item--active': tabCur==='inner'}" @click="tabSwitch('inner')" v-if="!isPerson">企业内部成员</view>
            <view class="tab__item" :class="{'tab__item--active': tabCur==='outer'}" @click="tabSwitch('outer')">我的联系人</view>
        </view>
        <scroll-view class="navigation scroll-x" scroll-x>
            <view v-for="(item, index) in curNavigation" :key="index" class="navigation__item" @click="navigationSwitch(item, index)">
                <text class="navigation__item__text">{{ item.label }}</text>
                <text class="navigation__item__icon iconic_contacts_arrow icon" v-if="index !== curNavigation.length - 1"></text>
            </view>
        </scroll-view>
        <view class="depts-list">
            <view class="depts-list__item" v-for="(item, index) in curDepts" :key="index" @click="navigationSwitch(item)">
                <text class="depts-list__item__name">{{ item.label }}</text>
                <text v-if="item.children.length > 0" class="depts-list__item__icon iconic_contacts_nextlevel"></text>
            </view>
        </view>
        <view class="content-list">
            <template v-if="curContent.length > 0">
                <view class="content-list__item" v-for="(item, index) in curContent" :key="index" @click="contentSelect(item)">
                    <text class="content-list__item__name">{{ item.label }}</text>
                </view>
            </template>
            <template v-else>
                <view class="content-list__item content-list__item--blank">
                    <text class="content-list__item__name">未找到联系人</text>
                </view>
            </template>
        </view>
    </scroll-view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { getEntDept, getEmployees, getGroup, getGroupMember } from 'api/address';
export default {
    name: 'Address',
    components: {

    },
    data() {
        return {
            receiverIndex: 0,
            depts: {},
            tabCur: '',
            curNavigation: [],
            curDepts: [],
            curContent: [],
            isPerson: true,
        };
    },
    computed: {
        ...mapState('send', ['receiverList', 'fromAddress', 'sendType', 'curReceiver']),
    },
    methods: {
        ...mapMutations('send', ['updateReceiverList', 'setFromAddress']),
        getAddressMember(dept) {
            if (dept.hasOwnProperty('sharedGroup')) {
                return getGroupMember(dept);
            } else {
                return getEmployees(dept);
            }
        },
        /* 获取某一级的子级 */
        getChildren(value, depts) {
            let result = false;
            if (depts.value === value) {
                result = depts.children;
            } else {
                depts.children.some(item => {
                    return result = this.getChildren(value, item);
                });
            }
            return result;
        },
        /* 切换到某一级部门 */
        async navigationSwitch(depts, index = null) {
            const isOuter = depts.hasOwnProperty('sharedGroup');
            /* 处理navigation （进入下一级 或 回退上层任一级）*/
            if (index === null) {
                /* 进入下一级 */
                this.curNavigation.push(depts);
            } else {
                /* 回退到上层任一级 */
                this.curNavigation = this.curNavigation.slice(0, index + 1);
            }

            /* 处理depts 获取当前级的所有下级 */
            this.curDepts = this.getChildren(depts.value, JSON.parse(JSON.stringify(this.depts)));

            /* 处理content 获取当前级的所有成员 */
            let { data: employees } = await this.getAddressMember(depts);
            if (isOuter) {
                employees = employees.filter(item => this.isPerson ? !item.entName : !!item.entName);
            }
            this.curContent = employees.map(item => {
                return {
                    // 拥有该字段表示 该条目为外部联系人
                    label: isOuter
                        ? (`${item.contactAccount} ${item.contactName} ${item.entName || ''}`)
                        : (`${item.empName}(${item.account})` || `${item.account}`),
                    value: item,
                    chooseType: isOuter ? 'outer' : 'inner',
                };
            });
        },
        contentSelect({ chooseType, value: item }) {
            if (chooseType === 'inner') {
                /* 如果是内部联系人，则将account带回并调用其账号联想，自动补全 */
                const param = {
                    userAccount: item.account,
                };
                this.updateReceiverList({
                    receiverIndex: this.receiverIndex,
                    updateData: this.sendType === 'localSend' ? param : { userInfo: { ...this.curReceiver.userInfo, ...param } },
                });
                this.setFromAddress('inner');
            } else {
                const param = {
                    userId: item.userId || '',
                    userAccount: item.contactAccount,
                    userName: item.contactName,
                    enterpriseName: item.entName,
                };
                /* 如果是外部联系人，则直接更改相关信息，再跳回 */
                this.updateReceiverList({
                    receiverIndex: this.receiverIndex,
                    updateData: this.sendType === 'localSend' ? param : { userInfo: { ...this.curReceiver.userInfo, ...param } },
                });
                this.setFromAddress('outer');
            }
            uni.navigateBack({
                delta: 1,
            });
        },
        tabSwitch(type) {
            this.$toast.loading({ mask: true });
            this.tabCur = type;
            if (type === 'inner') {
                this.initInner();
            } else {
                this.initOuter();
            }
            this.$nextTick(() => {
                this.$toast.hideLoading();
            });
        },
        /* 将接口获取到的depts结构字段转换为统一的结构字段 */
        handleInnerData(data) {
            const dept = {
                label: data.deptName,
                value: data.deptId,
                children: [],
            };
            data.childDeptVOS && data.childDeptVOS.forEach(deptChild => {
                dept.children.push(this.handleInnerData(deptChild));
            });
            return dept;
        },
        async initInner() {
            const { data: innerData } = await getEntDept();
            this.depts = this.handleInnerData(innerData);
            this.curNavigation = [];
            this.navigationSwitch(this.depts);
        },
        /* 将接口获取到的depts结构字段转换为统一的结构字段 */
        /* 外部联系人只有一级，无需递归; sharedGroup 为请求某分类下联系人时需要携带的字段 */
        handleOuterData(data) {
            const dept = {
                label: '全部',
                value: '0',
                sharedGroup: true,
                children: [],
            };
            dept.children = data.map(item => ({
                label: item.groupName,
                value: item.groupId,
                sharedGroup: item.sharedGroup,
                children: [],
            }));
            return dept;
        },
        async initOuter() {
            const { data: outerData } = await getGroup();
            this.depts = this.handleOuterData(outerData);
            this.curNavigation = [];
            this.navigationSwitch(this.depts);
        },
    },
    async onLoad(query) {
        this.receiverIndex = query.receiverIndex;
        this.isPerson = query.userType === 'PERSON';
        uni.setNavigationBarTitle({
            title: `联系人-${this.isPerson ? '个人' : '企业'}`,
        });
        this.tabSwitch(this.isPerson ? 'outer' : 'inner');
    },
};
</script>

<style lang="scss">
    .scrollY {
        height: 100%;
    }
    .tab {
        display: flex;
        height: 100upx;
        background-color: #FFF;
        border-bottom: 1upx solid #DFE1E0 ;
        box-sizing: border-box;
        .tab__item {
            flex: 1;
            font-size: 32upx;
            color: #2B2F2E;
            text-align: center;
            line-height: 100upx;
            &.tab__item--active {
                color: #127FD2;
                &:after {
                    display: block;
                    content: '';
                    width: 100upx;
                    height: 0;
                    border-bottom: 4upx solid #127FD2;
                    transform: translate(-50%, -4upx);
                    position: relative;
                    left: 50%;
                }
            }
        }
    }
    .navigation {
        height: 100upx;
        background-color: #FFF;
        white-space: nowrap;
        .navigation__item {
            line-height: 100upx;
            color: #127FD2;
            display: inline-block;
            &:last-child {
                color: #A6A9A8;
                pointer-events: none;
                margin-right: 42upx;
            }
            &:first-child {
                margin-left: 42upx;
            }
            .navigation__item__text {
                font-size: 32upx;
                vertical-align: middle;
            }
            .navigation__item__icon {
                display: inline-block;
                transform: translateY(2upx);
                margin-left: 10upx;
                margin-right: 10upx;
                color: #B3B6B5;
                font-size: 26upx;
                vertical-align: middle;
            }
        }
    }
    .depts-list {
        margin-top: 20upx;
        background-color: #FFF;
        padding-left: 42upx;
        box-sizing: border-box;
        .depts-list__item {
            height: 100upx;
            line-height: 100upx;
            color: #2B2F2E;
            border-bottom: 1upx solid #DFE1E0;
            padding-right: 30upx;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            .depts-list__item__name {
                display: block;
                font-size: 32upx;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
                overflow: hidden;
            }
            .depts-list__item__icon {
                display: block;
                color: #B3B6B5;
                /*align-self: flex-end;*/
                font-size: 32upx;
                /*float: right;*/
            }
        }
    }
    .content-list {
        margin-top: 20upx;
        background-color: #FFF;
        padding-left: 42upx;
        box-sizing: border-box;
        .content-list__item {
            height: 100upx;
            line-height: 100upx;
            color: #2B2F2E;
            border-bottom: 1upx solid #DFE1E0;
            padding-right: 30upx;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            .content-list__item__name {
                display: block;
                font-size: 32upx;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
                overflow: hidden;
            }
            &.content-list__item--blank {
                border: none;
                height: 150upx;
                line-height: 150upx;
                justify-content: center;
                .content-list__item__name {
                    text-align: center;
                    font-size: 32upx;
                }
            }

        }
    }
    .scroll-x {
        white-space: nowrap;
    }
</style>
