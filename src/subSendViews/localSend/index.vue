<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <view class="local-send-page">
            <!--选择发件方-->
            <view class="local-send-page__title">选择发件方</view>
            <view class="local-send-page__picker">
                <SelectSender
                    class="local-send-page__picker-select"
                    :fromLocalSendPage="true"
                    :showAuthCancelBtn="false"
                    ref="selectSender"
                ></SelectSender>
            </view>
            <template v-if="ifShowLocalSend && hasLocalSendRight">
                <!--上传合同文件-->
                <view class="local-send-page__title">上传合同文件</view>
                <UploadFileList></UploadFileList>
                <SelectUploadType
                    uploadBtnText="+ 上传合同文件"
                    :uploadedFileList="uploadedFileList"
                    :changeBtnHeight="true"
                    @uploadFileList="handleUploadFileList"
                ></SelectUploadType>
                <!--填写合同描述信息-->
                <DescribeInfo></DescribeInfo>
                <!--添加签署人-->
                <SignerInfo></SignerInfo>
            </template>
            <template v-else>
                <view class="local-send-page__tip">
                    {{ ifShowLocalSend ? '该企业未授予您发送本地合同权限，请联系主管理员去企业控制台添加。' : '暂不支持该企业发送本地合同' }}
                </view>
            </template>
        </view>
        <view class="local-send-page__btn" slot="footer" @click="handleSubmit" v-if="ifShowLocalSend && hasLocalSendRight">
            下一步
        </view>
    </xd-page>
</template>

<script>
import xdPage from '@/components/xdPage/index.vue';
import { mapMutations, mapState } from 'vuex';
import SelectSender from 'components/selectSender';
import SelectUploadType from 'components/selectUploadType';
import DescribeInfo from 'components/describeInfo';
import SignerInfo from 'components/signerInfo';
import UploadFileList from 'components/uploadFileList';
import { createContractId, genDocument, postContractInfo } from 'api/send';
import debounce from 'src/utils/debounce.js';
import { getLocalSendReceiverInfo } from 'const/receiver';
import { authMixin } from 'mixins/auth';
export default {
    components: {
        SelectSender,
        SelectUploadType,
        DescribeInfo,
        SignerInfo,
        xdPage,
        UploadFileList,
    },
    mixins: [authMixin],
    data() {
        return {
            showGuideSender: false,
        };
    },
    computed: {
        ...mapState('send', ['uploadedFileList', 'contractDescribeInfo', 'signOrderly', 'receiverList', 'contractId', 'hasLocalSendRight', 'sendType']),
        ...mapState('user', ['commonHeaderInfo']),
        ...mapState(['isWeWork']),
        ifShowLocalSend() {
            return !this.commonHeaderInfo.openNewContractTemplate || this.commonHeaderInfo.userType === 'Person';
        },
    },
    methods: {
        ...mapMutations('send', ['setContractId', 'updateReceiverList', 'setReceiverList', 'setUploadedFileList', 'setContractDescribeInfo', 'addReceiverList']),
        ...mapMutations('user', ['setQwxUserName']),
        handleUploadFileList(fileList) {
            this.setUploadedFileList(fileList);
        },
        /* 非顺序签不能添加重复账号*/
        checkSameAccount() {
            const { signOrderly, receiverList } = this;
            if (!signOrderly) {
                let ieRepeat = false;
                const len = receiverList.length;
                for (let i = 0; i < len; i++) {
                    for (var j = i + 1; j < len; j++) {
                        const isSameType = receiverList[i].userType === receiverList[j].userType;
                        const isSameAccount = receiverList[i].userAccount && (receiverList[i].userAccount === receiverList[j].userAccount);
                        const isSameId = receiverList[i].enterpriseId && receiverList[i].enterpriseId !== '0' && (receiverList[i].enterpriseId === receiverList[j].enterpriseId) && (receiverList[i].userId === receiverList[j].userId);
                        // 个人账号 判断userAccount是否一致，企业账号判断enterpriseId\userId是否一致，要排除都不存在的情况
                        // 企业账号存在角色关联，存在userId不、enterpriseId相等的情况
                        if (isSameType && (receiverList[i].userType === 'ENTERPRISE' ? isSameId : isSameAccount)) {
                            ieRepeat = true;
                            break;
                        }
                    }
                    if (ieRepeat) {
                        break;
                    }
                }
                if (ieRepeat) {
                    return false;
                }
            }
            return true;
        },
        async handleSignInfo() {
            const { receiverList, contractDescribeInfo, signOrderly } = this;
            if (!contractDescribeInfo.contractName) {
                this.$toast.error('未填写合同名称，请填写后继续');
                return Promise.reject();
            }
            if (!this.checkSameAccount()) {
                this.$toast.error('非顺序签署时不能重复添加签约方');
                return Promise.reject();
            }
            // 如果所有收件方均是“抄送人”,提示：请至少添加一个签署人
            if (!receiverList.length || !receiverList.some(item => item.receiverType === 'SIGNER')) {
                this.$toast.error('请至少添加一个签署人');
                return Promise.reject();
            }

            if (this.showGuideSender && !this.checkAddSender()) {
                this.showGuideSender = false;
                uni.showModal({
                    title: '提示',
                    content: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',
                    confirmText: '添加',
                    confirmColor: '#127fd2',
                    success: (res) =>  {
                        if (res.confirm) {
                            this.addSender();
                        } else {
                            this.handleSubmit();
                        }
                    },
                });
                return Promise.reject();
            }
            const contractTypeId = contractDescribeInfo.curContractType && contractDescribeInfo.curContractType.folderId;

            this.$toast.loading({ mask: true });
            try {
                const receivers = this.receiverList.map(item => {
                    // 本地展示数据不需要传到后端
                    const newItem = {
                        ...item,
                        userAvatar: null,
                        enterprises: null,
                    };
                    return newItem;
                });
                const param = {
                    contractLifeEnd: contractDescribeInfo.contractDate ? new Date(contractDescribeInfo.contractDate).getTime() : null,
                    contractTitle: contractDescribeInfo.contractName,
                    contractTypeId,
                    expiredDate: new Date(contractDescribeInfo.signDate).getTime(),
                    receivers,
                    signOrdered: signOrderly,
                    viewedLetter: false,
                };
                const { data: { outerReceivers } } = await postContractInfo({ contractId: this.contractId, param: param });
                // 更新receiverId，问题 后端返回的outerReceivers数组和前端传递的receivers可能顺序会变化，所以已routeOrder为准
                const mapOrderObj = {};
                this.receiverList.map((item, index) => {
                    mapOrderObj[item.routeOrder] = index;
                });
                outerReceivers.map(receiver => {
                    const receiverIndex = mapOrderObj[receiver.routeOrder];
                    if (receiverIndex > -1) {
                        this.updateReceiverList({
                            receiverIndex,
                            updateData: { receiverId: receiver.receiverId },
                        });
                    }
                });
                this.$toast.hideLoading();
                return Promise.resolve();
            }  catch (e) {
                setTimeout(this.$toast.hideLoading, 2000);
                return Promise.reject();
            }
        },
        checkAddSender() {
            const hasSender =  this.receiverList.some(receiver => {
                return receiver.userType === 'PERSON' && receiver.userAccount === this.commonHeaderInfo.platformUser.account;
            });
            return !!hasSender;
        },
        addSender() {
            const defaultReceiverInfo = getLocalSendReceiverInfo({
                userType: 'PERSON',
                routeOrder: this.receiverList.length + 1,
                userAccount: this.commonHeaderInfo.platformUser.account,
            });
            this.addReceiverList(defaultReceiverInfo);
            uni.navigateTo({
                url: `/subSendViews/addReceiver/index?receiverIndex=${this.receiverList.length - 1}`,
            });
        },
        handleSubmit: debounce(function() {
            if (this.entList[this.curEntIndex].authStatus !== 2) {
                this.handleGoAuth({
                    curIndex: this.curEntIndex,
                    showCancel: false,
                });
                return;
            }
            if (!this.uploadedFileList.length) {
                return  this.$toast.error('请先上传合同文件');
            }
            this.$toast.loading({ mask: true });
            genDocument({ contractId: this.contractId, fileList: this.uploadedFileList }).then(() => {
                this.$toast.hideLoading();
                this.handleSignInfo().then(() => {
                    uni.navigateTo({
                        url: `/subSendViews/sendPointPosition/index`,
                    });
                });
            })
                .catch(() => {
                    this.$toast.hideLoading();
                });
        }),
        // 是否第一次需要提醒添加发件方为签约方
        async handleGuideSender() {
            const  { data: { value } } = await this.$http.get('/users/configs/default/FIRST_AFTER_SEND_OPTIMIZATION');
            // true表示第一次
            if (value === 'true') {
                this.showGuideSender = true;
                this.$http.post('/users/configs/FIRST_AFTER_SEND_OPTIMIZATION', {
                    name: 'FIRST_AFTER_SEND_OPTIMIZATION',
                    value: false,
                });
            }
        },
    },
    async onReady() {
        const _self  = this;
        if (this.isWeWork) {
            uni.qy.getEnterpriseUserInfo({
                success: function(res) {
                    const userInfo = res.userInfo;
                    _self.setQwxUserName(userInfo.name);
                },
            });
        }
    },
    async onLoad() {
        this.setReceiverList([]);
        this.setUploadedFileList([]);
        this.setContractDescribeInfo({});
        if (this.commonHeaderInfo.currentEntId === '0') {
            this.handleGuideSender();
        }
        createContractId().then((res) => {
            const { data: { contractId } } = res;
            this.setContractId(contractId);
        }).catch((err) => {
            this.setContractId(null);
            const { response: { data: { code } } } = err;
            if (code === '130151') {
                this.handleGoAuth({
                    curIndex: this.curEntIndex,
                    showCancel: false,
                });
            }
        });
    },
};
</script>
<style lang="scss">
.local-send-page {
    height: 100%;
    background-color: $--background-color-base;
    overflow: scroll;
    &__title {
        height: 90rpx;
        line-height: 90rpx;
        color: $--color-text-secondary;
        padding: 0 30rpx;
    }
    &__picker {
        width: calc(100% - 60rpx);
        height: 100rpx;
        line-height: 100rpx;
        background-color: $--color-white;
        padding: 0 30rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        &-select {
            width: 100%;
        }
    }
    .icon-ic_forward {
        color: $--color-text-secondary;
        font-size: 30rpx;
    }
    &__btn {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
    &__tip {
        padding: 20rpx 20rpx;
        color: $--color-info;
    }
}
</style>
