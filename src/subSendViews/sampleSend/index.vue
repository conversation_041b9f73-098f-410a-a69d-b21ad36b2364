/* eslint-disable vue/valid-template-root */
<!--
页面功能： 范本发起
路由参数：type，范本类型
-->
<template>
    <xd-page class="sample-send-page" ref="xdPage">
        <scroll-view
            :scroll-y="true"
            class="sample-send-page__content"
            @scroll="scroll"
            :scroll-top="scrollTop"
        >
            <view
                class="send-content-page"
                v-for="(page, pageIndex) in documentInfo.documentPages"
                :key="pageIndex"
                :style="{
                    position: 'relative',
                    width: `${page.width}px`,
                    height: `${page.height + 10}px`,
                }"
            >
                <!-- 合同文档 -->
                <image
                    mode="widthFix"
                    :src="page.previewUrl"
                    :style="{
                        position: 'absolute',
                        left: '0',
                        top: '0',
                        width: `200%`,
                        transform: `scale(0.5)`,
                        transformOrigin: `top left`,
                    }"
                ></image>
            </view>
            <!-- 标签 -->
            <SampleLabel
                v-for="mark in marks"
                :key="mark.labelId"
                :mark="mark"
                :active-label-id="activeLabelId"
            ></SampleLabel>
        </scroll-view>
        <KnewDetailPopup
            v-if="showKnewDetailPopup"
            :sampleId="sampleId"
            @knewDetailConfirm="handleKnewDetailConfirm"
        >

        </KnewDetailPopup>
        <InfoFillPopup
            v-if="showInfoFillPopup"
            :sampleId="sampleId"
            :labels="labelsNeedFill"
            :receivers="receivers"
            @updateLabelValue="handleLabelValueUpdate"
            @sendSample="sendSampleHandler"
            @labelClick="handleLabelFocus"
        >
        </InfoFillPopup>
    </xd-page>
</template>
<script>
import SampleLabel from 'components/sampleLabel/index.vue';
import xdPage from 'components/xdPage/index.vue';
import { getSampleDetailById, postSampleInfoAndSend } from 'api/sample';
import { coordinateReversal } from 'utils/contract';
import { mapState } from 'vuex';
import { SAMPLE_HISTORY_KEY } from 'utils/sample.js';
import { getUserConfigByKey, postUserConfig } from 'api/account';
import InfoFillPopup from 'components/infoFillPopup/index.vue';
import KnewDetailPopup from 'components/knewDetailPopup/index.vue';
import { getSampleLabelValidateResult } from 'utils/sample';
import Vue from 'vue';

export default {
    components: { KnewDetailPopup, InfoFillPopup, xdPage, SampleLabel },
    data() {
        return {
            sampleId: '',
            sampleInfo: {},
            marks: [],
            host: '',
            accessToken: '',
            activeLabelId: '',
            showInfoFillPopup: false,
            showKnewDetailPopup: false,
            knewDetailHistoryInfo: [],
            scrollTop: 0,
            old: {
                scrollTop: 0,
            },
        };
    },
    computed: {
        ...mapState('send', ['curEntIndex']),
        ...mapState('user', ['entList', 'commonHeaderInfo', 'currentEntName', 'userAccount']),
        documentInfo() {
            return this.sampleInfo.documents ? this.sampleInfo.documents[0] : [];
        },
        labelsNeedFill() {
            return this.marks.filter(a => !['SEAL', 'SIGNATURE', 'DATE'].includes(a.labelType));
        },
        receivers() {
            const receivers = this.sampleInfo.roles || [];
            if (this.commonHeaderInfo.currentEntId !== '0') { // 主体是企业时，将企业信息带入
                receivers.forEach((receiver) => {
                    if (receiver.roleName === '公司') {
                        receiver.enterpriseName = this.currentEntName;
                        const { isFailed } =
                            getSampleLabelValidateResult({
                                name: '手机号',
                                required: true,
                                value: this.userAccount,
                            });
                        if (!isFailed) {
                            receiver.userAccount = this.userAccount;
                        }
                    }
                });
            }
            return receivers;
        },
    },
    methods: {
        handleKnewDetailConfirm() {
            this.updateKnewHistory();
            this.showKnewDetailPopup = false;
            this.showInfoFillPopup = true;
        },
        updateKnewHistory() {
            this.knewDetailHistoryInfo.push(this.sampleId);
            postUserConfig(SAMPLE_HISTORY_KEY, JSON.stringify(this.knewDetailHistoryInfo));
        },
        handleLabelFocus(markI) {
            const { labelId } = this.marks[markI];
            this.activeLabelId = labelId;
            this.scrollToLabel(this.marks[markI]);
        },
        scrollToLabel(label) {
            // 单页无滚动条不进行滚动
            if (this.sampleInfo.documents[0].documentPages.length < 2) {
                return;
            }
            this.scrollTop = this.old.scrollTop;
            this.$nextTick(() => {
                this.scrollTop = label.labelPosition.y - 50; // 避免被遮挡
            });
        },
        // Update marks and sampleInfo data when fill label value changed
        handleLabelValueUpdate({ label }) {
            const { markI, labelId, value, disabled, defaultValue } = label;
            this.$set(this.marks[markI], 'value', value);
            this.$set(this.marks[markI], 'disabled', disabled);
            this.$set(this.marks[markI], 'defaultValue', defaultValue);
            const { documents } = this.sampleInfo;
            const currentDocument = documents[0];
            const labelIndex = currentDocument.sampleLabels.findIndex(a => a.labelId === labelId);
            this.$set(this.sampleInfo.documents[0].sampleLabels[labelIndex], 'value', value); // 更新存储数据用的sampleInfo
        },
        handleSendAlert() {
            return new Promise((resolve) => {
                if (Vue.$store.state.user.commonHeaderInfo.userType === 'Person') {
                    const content = `发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。`;
                    uni.showModal({
                        title: '是否确认发送',
                        content,
                        confirmText: '发送',
                        confirmColor: '#127fd2',
                        success: (res) => {
                            if (res.confirm) {
                                resolve();
                            }
                        },
                    });
                } else {
                    resolve();
                }
            });
        },
        // Send the sample after fill info
        async sendSampleHandler(receivers) {
            await this.handleSendAlert(); // 发前送alert提示
            // 发送接口
            uni.showLoading({
                title: '合同发送中',
                mask: true,
            });
            const that = this;
            postSampleInfoAndSend(this.sampleId, {
                ...this.sampleInfo,
                roles: [...receivers],
            }).then((res) => {
                uni.hideLoading();
                const contractId = res.data?.result;
                const contractName = that.sampleInfo.sampleBasic.sampleTitle;
                const sender = that.entList[that.curEntIndex]?.showName || '';
                uni.redirectTo({
                    url: `/views/shareContract/index?contractId=${contractId}&contractName=${encodeURIComponent(contractName)}&sender=${encodeURIComponent(sender)}`,
                });
            }).catch(({ response, status }) => {
                setTimeout(() => {
                    uni.hideLoading();
                    const message = response && response.data.message;
                    if (status === 409 && message && message.indexOf('可用份数不足')) { // 无合同份数时只能从message里判断，后端未提供对应code
                        uni.showModal({
                            title: '提示',
                            content: message,
                            cancelText: '取消',
                            confirmText: '去购买',
                            confirmColor: '#127fd2',
                            success(res) {
                                if (res.confirm) {
                                    uni.navigateTo({ url: '/views/charge/index' });
                                }
                            },
                        });
                    }
                }, 1500); // 异常情况，toast会被关掉。这里延时规避
            });
        },
        // 初始化范本数据：字段、签署人
        async initSampleData() {
            const { data } = await getSampleDetailById(this.sampleId);
            const { documents  } = data;
            const documentInfo = documents[0];
            this.sampleInfo = data;
            documentInfo.documentPages.forEach((page) => { // 参照模板预览处理，做宽高转换，拼接预览图地址
                const { width, height } = page;
                page.width = uni.upx2px(750);
                page.height = uni.upx2px(750 / width * height);
                page.previewUrl =
                    `${this.host}${page.previewUrl}?access_token=${this.accessToken}`;
            });
            documentInfo.sampleLabels.forEach((label, index) => {
                const { labelPosition: { pageNumber } } = label;
                const currentPage = documentInfo.documentPages[pageNumber - 1];
                const numberCoordinate = coordinateReversal(label.labelPosition, currentPage.width, currentPage.height);
                const height = (((pageNumber - 1 > 0) ? 10 : 0) + documentInfo.documentPages[0].height) *
                    (pageNumber - 1);
                // 老的坐标为数值，转换为百分比
                const tempMark = {
                    ...label,
                    markI: index,
                    valueStr: label.value,
                    labelPosition: {
                        ...label.labelPosition,
                        x: numberCoordinate.x,
                        y: numberCoordinate.y + height,
                        width: numberCoordinate.width,
                        height: numberCoordinate.height,
                    },
                };
                this.marks.push(tempMark); // 字段单独存储用作展示和处理
                return tempMark;
            });
            console.log('this.marks = ', this.marks);
        },
        async initConfig() {
            getUserConfigByKey(SAMPLE_HISTORY_KEY).then(res => {
                if (res.data) {
                    let cacheData = JSON.parse(res.data.value || '[]');
                    if (!Array.isArray(cacheData)) {
                        cacheData = [];
                    }
                    this.knewDetailHistoryInfo = cacheData;
                    if (this.knewDetailHistoryInfo.includes(this.sampleId)) {
                        this.showInfoFillPopup = true;
                        return;
                    }
                }
                this.showKnewDetailPopup = true;
            });
        },
        async init() {
            // init base info
            this.host = this.$http.baseURL;
            this.accessToken = uni.getStorageSync('accessToken');
            await this.initSampleData();
            await this.initConfig();
        },
        scroll(e) {
            this.old.scrollTop = e.detail.scrollTop;
        },
    },
    async onLoad(options) {
        this.sampleId = options.sampleId;
        await this.init();
    },
};
</script>

<style lang="scss">
.sample-send-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 750rpx;
    line-height: 1;
    padding-bottom: 50px;
    .xdpage {
        overflow: visible !important;
    }
    //&.limit-scroll {
    //    .xdpage {
    //        overflow: auto !important;
    //    }
    //}

    &__content {
        width: 100%;
        height: calc(100% - 1px);
        background: $--background-color-base;
        position: relative;
        &-page {
            width: 750rpx;
            position: relative;
            box-sizing: border-box;
        }
    }

}
</style>
