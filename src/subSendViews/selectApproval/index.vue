<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <view class="select-approval-page">
            <radio-group @change="selectChange">
                <label v-for="(item, index) in definesData" :key="index" class="select-approval-page__list">
                    <radio
                        class="select-approval-page__radio"
                        :value="index"
                        :checked="index == curIndex"
                        color="#127FD2"
                    />
                    <view>{{ item.name }}</view>
                </label>
            </radio-group>
        </view>
        <view class="select-approval-page__btn" slot="footer" @click="goToNext">下一步</view>
    </xd-page>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import xdPage from '@/components/xdPage/index.vue';
import debounce from 'src/utils/debounce.js';
export default {
    components: {
        xdPage,
    },
    data() {
        return {
            curIndex: 0,
        };
    },
    computed: {
        ...mapState('send', ['definesData', 'sendType']),
    },
    methods: {
        ...mapMutations('send', ['setDefineIndex']),
        goToNext: debounce(function() {
            this.setDefineIndex(this.curIndex);
            uni.navigateTo({
                url: '/subSendViews/sendApproval/index',
            });
        }),
        selectChange(e) {
            this.curIndex = e.detail.value;
        },
    },
};
</script>

<style lang="scss">
.select-approval-page {
    background: #F1F2F3;
    height: 100%;
    &__list {
        background-color: $--color-white;
        display: flex;
        flex-direction: row;
        height: 100rpx;
        line-height: 100rpx;
        border-top: 1rpx solid $--border-color-lighter;
        padding: 0 40rpx;
    }
    &__radio {
        transform: scale(0.8);
        color: $--color-primary!important;
    }
    &__btn {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
}
</style>

