<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10" class="send_approve">
        <view>
            <view class="send_approve-title">{{ definesData[currentSelectDefineIndex].name }}</view>
            <view class="send_approve-content uni-flex">
                <view v-for="(item, index) in flowStepList" :key="index" class="send_approve-item uni-flex uni-center-main">
                    <view class="uni-flex uni-column uni-center-main uni-center-sub">
                        <image v-if="item.portraitSrc" :src="item.portraitSrc" style="width: 40px; height: 40px" />
                        <view v-else class="send_approve-item-icon uni-flex uni-center-main uni-center-sub">
                            <text class="iconic_sign_head_portrait"></text>
                        </view>
                        <view class="send_approve-item-name">{{ item.defineApproverVOList[0].empName || item.empName }}</view>
                    </view>
                    <text class="iconic_approver_arrow" v-if="index + 1 < flowStepList.length"></text>
                </view>
            </view>
        </view>
        <view class="send_approve-footer" slot="footer" @click="getCharging">提交</view>
    </xd-page>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import xdPage from '@/components/xdPage/index.vue';
export default {
    components: {
        xdPage,
    },
    onLoad() {
        this.getApprovalFlowInfo();
    },
    computed: {
        ...mapState('send', ['flowStepList', 'definesData', 'currentSelectDefineIndex', 'sendType']),
    },
    methods: {
        ...mapActions('send', ['getApprovalFlowInfo', 'getCharging']),
    },
};
</script>

<style lang="scss">
$mainColor: #127fd2;
$bgColor: #F1F2F3;
.send_approve {
    background: $bgColor;
    &-title {
        width: 750upx;
        padding: 40upx;
        font-size: 28upx;
        line-height: 1;
        color: #979797;
    }
    &-content {
        background: #ffffff;
        padding: 32upx 40upx;
        width: 750upx;
    }
    &-item {
        text {
            color: #ffffff;
            font-size: 40upx;
            &.iconic_approver_arrow {
                color: #DCDCDC;
                font-size: 24upx;
                margin: 0 20upx;
                margin-top: -40upx;
            }
        }
        &-icon {
            width: 70upx;
            height: 70upx;
            border-radius: 70upx;
            background: #DCDCDC;
        }
        &-name {
            font-size: 24upx;
            line-height: 1;
            margin-top: 16upx;
        }
    }
    &-footer {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
}
</style>
