<!--
页面功能： 1）本地发起合同，为签署人指定位置
         2) 使用模板发起合同，预览发送
         支持触发审批流
控制参数  store参数sendType： 本地发起 localSend，使用模板发起 templateSend
-->
<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10" class="send" :className="'send'" @dragEnd="dragEnd" @dragMove="dragMove">
        <view class="send-title" v-if="sendType === 'templateSend'">
            <view class="send-title__tip">当前合同文档：</view>
            <uni-segmented-control
                :current="curDocIndex"
                :values="docTitles"
                @clickItem="onClickDocTab"
                styleType="text"
                activeColor="#127FD2"
            ></uni-segmented-control>
        </view>
        <!-- 合同内容 -->
        <scroll-view
            :scroll-y="isScroll"
            class="send-content"
            @scroll="handleScroll"
            :style="{
                height: `${(sendType === 'templateSend' ? 'calc(100% - 60px)' : '100%')}`
            }"
        >
            <view class="send-content-container"
                :style="{
                    'padding-top': `${paddingTop}px`,
                    'transform': `scale(${zoom})`,
                    'transform-origin': '0 0 0',

                }"
            >
                <view
                    class="send-content-doc"
                    v-for="(doc, docIndex) in docList"
                    :key="docIndex"
                >
                    <!-- 合同文档 -->
                    <view v-if="(sendType === 'templateSend' && docIndex === curDocIndex) || sendType === 'localSend'">
                        <view
                            class="send-content-page"
                            v-for="(page, pageIndex) in (sendType === 'templateSend' ? doc.documentPages : doc.page)"
                            :key="pageIndex"
                            :style="{
                                width: `${page.width}px`,
                                height: `${page.height + 10}px`,
                            }"
                        >
                            <!-- 合同文档 -->
                            <image
                                :src="`${host}${page.imagePreviewUrl}?access_token=${accessToken}`"
                                :style="{
                                    width: `${page.width}px`,
                                    height: `${page.height}px`
                                }"
                            ></image>
                            <!--水印-->
                            <view class="water-mark-back" :style="{background: waterMarkPng ? `url(${waterMarkPng}) repeat` : 'none'}"></view>
                            <!--骑缝章-->
                            <view class="riding-seals" v-if="currentDocRidingSeals.length">
                                <view class="riding-seals-bg"></view>
                                <RidingSeal
                                    v-for="ridingSeal in currentDocRidingSeals"
                                    :key="ridingSeal.receiverId"
                                    :ridingSeal="ridingSeal"
                                    :color="ridingSealBgColor(ridingSeal)"
                                />
                            </view>
                        </view>
                        <!-- 标签 -->
                        <label-mark
                            v-for="mark in marks"
                            :key="mark.labelId"
                            :mark="mark"
                            :receivers="receivers"
                            :pageHeight="sendType === 'templateSend' && doc.documentPages[0].height"
                            :activeLabelId="activeLabelId"
                            @updateMark="updateMark"
                            @dragStart="dragStart"
                        ></label-mark>
                    </view>
                </view>
                <view
                    v-if="switchHighQualityImageBtnShow"
                    @click="switchHighQualityImage"
                    class="send-content-hqi"
                >查看高清文件</view>
            </view>
        </scroll-view>
        <!-- 底部footer -->
        <send-footer
            slot="footer"
            class="send-footer-container"
            :receivers="receivers"
            :docList="docList"
            :marks="sendType === 'localSend' ? marks : allMarks"
            :isEnt="isEnt"
            :contractId="contractId"
            :receiverActiveIndex="receiverActiveIndex"
            @dragStart="dragStart"
            @clickAddMark="clickAddMark"
            @updateReceiverActiveId="i => receiverActiveId = i"
        ></send-footer>
    </xd-page>
</template>
<script>
import { mapState } from 'vuex';
import { coordinateReversal, markInfo, markCoordinateTransform, markDefault, templateMarkDefault, getValueByScope } from '@/utils/contract';
import labelMark from '@/components/labelMark';
import sendFooter from '@/components/sendFooter';
import xdPage from '@/components/xdPage';
import RidingSeal from '@/components/ridingSeal';
import { saveLabel, getDocListAndLabels, deleteLabel } from 'src/api/template.js';
import { getReceiverList, getDocList, deleteMark } from 'src/api/send.js';
import { getRandomInt } from '@/utils/send';
import { uniSegmentedControl } from '@dcloudio/uni-ui';
import { createWaterMark, initWatermark, initRidingSeal } from '@/utils/decorate.js';
import { colorInfo } from '@/utils/contract';
export default {
    components: {
        labelMark,
        sendFooter,
        xdPage,
        uniSegmentedControl,
        RidingSeal,
    },
    data() {
        return {
            paddingTop: 10, // 页面顶部的padding-top
            isScroll: true,
            scrollTop: 0,
            accessToken: '',
            receiverActiveId: null, // 当前选中签署方receiverId
            isDrag: false,
            dragStatus: 'end', // 拖拽签名状态 start/move/end
            dragIconPosition: {
                x: 0,
                y: 0,
            },
            dragType: 'SIGNATURE', // 拖拽签名类型，SEAL/SIGNATURE/DATE
            dragMark: {},
            height: {
                pageHeight: 0, // 合同滚动区域高度
            },
            switchHighQualityImageBtnShow: true, // 是否显示高清按钮
            allMarks: {}, // 所有文档的label列表,格式 {[documentId1]: [{}, {}], [documentId2]: [{}, {}]}
            marks: [], // 当前文档的label列表
            activeMarkI: -1, // 拖拽的mark对应Index，-1默认是底部按钮滑动过来的
            activeCenter: {
                x: 0,
                y: 0,
            }, // 拖拽中心点 与 标签左上角的距离
            originDragMark: {}, // 记录拖拽之前的位置
            allPageHeight: 0,
            host: '',
            docList: [],
            receivers: [],
            curDocIndex: 0,
            docTitles: [],
            watermarkList: [],
            ridingSealList: [],
        };
    },
    computed: {
        ...mapState('send', ['isIphoneX', 'sendType', 'contractId']),
        isEnt() {
            return this.receivers.length && this.receivers[this.receiverActiveIndex] && this.receivers[this.receiverActiveIndex].userType === 'ENTERPRISE';
        },
        receiverActiveIndex() {
            let receiverActiveIndex = 0;
            this.receivers.some((receiver, index) => {
                if (receiver.receiverId === this.receiverActiveId) {
                    receiverActiveIndex = index;
                    return true;
                }
                return false;
            });
            return receiverActiveIndex;
        },
        activeLabelId() {
            if (!(this.marks || []).length) {
                return;
            }
            const  filteredLabels = this.marks.filter(item => ['SEAL', 'SIGNATURE', 'DATE'].includes(this.sendType === 'localSend' ? item.type : item.labelType));
            const lastLabelIndex = this.marks.findIndex(item => item?.labelId === filteredLabels[filteredLabels.length - 1]?.labelId);
            return this.marks[this.activeMarkI > -1 ? this.activeMarkI : lastLabelIndex]?.labelId || '';
        },
        // 水印图片
        waterMarkPng() {
            const watermarkText = (this.watermarkList || []).map(item => item.watermarkText);
            if (!watermarkText.length) {
                return null;
            }
            return createWaterMark(watermarkText, 150, 200);
        },
        // 当前文档的骑缝章list
        currentDocRidingSeals() {
            return (this.ridingSealList || []).filter((rd, index) => {
                rd.originIndex = index;
                return rd.selectAll || (rd.selectDocumentIds || []).includes(this.docList[this.curDocIndex].documentId);
            });
        },
        ridingSealBgColor() {
            return (ridingSeal) => {
                const index = this.receivers.findIndex(item => item['receiverId'] === ridingSeal.receiverId);
                return colorInfo[index % 9] || 'transparent';
            };
        },
        ridingSealPadding() {
            return this.currentDocRidingSeals.length ? 75 : 0;
        },
        // 文档和骑缝章重叠的部分宽度
        docCoverRidingSealWidth() {
            return this.currentDocRidingSeals.length ? 15 : 0;
        },
        zoom() {
            const pageWidth = uni.upx2px(750);
            return (pageWidth - (this.ridingSealPadding - this.docCoverRidingSealWidth)) / pageWidth;
        },
        docTotalHeight() {
            const docPages = (this.sendType === 'localSend' ? this.docList[this.curDocIndex]?.page : this.docList[this.curDocIndex]?.documentPages) || [];
            return (docPages[0]?.height + this.paddingTop) * this.zoom * docPages.length;
        },
    },
    async onLoad() {
        uni.setNavigationBarTitle({
            title: '指定签署位置',
        });
        this.host = this.$http.baseURL;
        this.accessToken = uni.getStorageSync('accessToken');
        await this.initDocList();
        if (this.sendType === 'localSend') {
            await this.getReceivers();
            this.noticeBackForGenerateImages();
        }
    },
    async onReady() {
        const query = uni.createSelectorQuery();
        // 获取页面总高度，动态赋值
        query.select('.send').boundingClientRect(res => {
            console.log('send', res);
            this.height = {
                pageHeight: res.height  - uni.upx2px(220) - uni.upx2px(this.isIphoneX ? 68 : 0), // 合同滚动区域高度
            };
        }).exec();
    },
    methods: {
        onClickDocTab(e) {
            if (this.curDocIndex !== e.currentIndex) {
                this.curDocIndex = e.currentIndex;
                this.marks = this.allMarks[this.docList[this.curDocIndex].documentId] || [];
                this.activeMarkI = -1; // 切换文档后重新初始化activeMarkI
            }
        },
        async initDocList() {
            let docs;
            if (this.sendType === 'localSend') {
                const { data } = await getDocList({ contractId: this.contractId });
                docs = data;
            } else {
                try {
                    const { data: { documents, receivers, decorate } } = await getDocListAndLabels({ draftId: this.contractId });
                    docs = documents;
                    this.receivers = receivers;
                    this.watermarkList = initWatermark(decorate.watermarks, receivers);
                    this.ridingSealList = initRidingSeal(decorate.ridingSeals, receivers);
                } catch (err) {
                    setTimeout(() => {
                        uni.navigateBack({ delta: 1 });
                    }, 3000);
                }
            }
            let allPageHeight = 0;
            docs.forEach((doc, docIndex) => {
                this.docTitles.push(doc.documentName);
                (this.sendType === 'localSend' ? doc.page : doc.documentPages).forEach((page, pageIndex) => {
                    const { width, height, marks } = page;
                    page.width = uni.upx2px(750);
                    page.height =  uni.upx2px(750 / width * height);

                    if (this.sendType === 'localSend') {
                        allPageHeight += ((docIndex + pageIndex) ? 10 : 0); // 记录当前page距离文档最顶端的
                        // 初始化marks
                        page.marks = marks && marks.map((mark) => {
                            if (mark.type === 'QR_CODE') {
                                return null;
                            }
                            // 接口返回mark的x,y值为：以图片左下角为坐标原点 标签左下角的坐标 占图片宽高的 百分比，
                            // 转换以图片右上角为原点，标签左上角为坐标点 的数值形式，
                            const numberCoordinate = coordinateReversal(mark, page.width, page.height);
                            const tempMark = {
                                ...mark,
                                docI: docIndex, // 标签所在的文档顺序
                                pageI: pageIndex, // 标签页码顺序
                                markI: this.marks.length,
                                x: numberCoordinate.x,
                                y: numberCoordinate.y + allPageHeight,
                                width: numberCoordinate.width,
                                height: numberCoordinate.height,
                            };
                            this.marks.push(tempMark);
                            return tempMark;
                        });
                        allPageHeight += page.height;
                    }
                });
                this.sendType === 'templateSend' && doc.labels.map((label, index) => {
                    if (label.labelType === 'QR_CODE') {
                        return null;
                    }
                    const numberCoordinate = coordinateReversal(label.labelPosition, doc.documentPages[label.pageNumber - 1].width, doc.documentPages[label.pageNumber - 1].height);
                    // 此在n页的lable，前面需要加单宽度为n-1的页面高度以及n-1的10px页间高度
                    const height = (doc.documentPages[0].height + 10) * (label.pageNumber - 1);
                    // 老的坐标为数值，转换为百分比
                    const tempMark = {
                        ...label,
                        markI: index,
                        documentId: doc.documentId,
                        labelPosition: {
                            ...label.labelPosition,
                            x: numberCoordinate.x,
                            y: numberCoordinate.y + height,
                            width: numberCoordinate.width,
                            height: numberCoordinate.height,
                        },
                    };
                    if ((this.allMarks[doc.documentId] || []).length) {
                        this.allMarks[doc.documentId].push(tempMark);
                    } else {
                        this.allMarks[doc.documentId] = [];
                        this.allMarks[doc.documentId].push(tempMark);
                    }
                    return tempMark;
                });
            });
            this.docList = docs;
            this.allPageHeight = allPageHeight + 10; // 每一次页面都有一个marign-bottom 10px，所以最后一个页面的要加上

            if (this.sendType === 'templateSend') {
                this.marks = this.allMarks[this.docList[this.curDocIndex].documentId] || [];
            }
        },
        getReceivers() {
            return new Promise((resolve, reject) => {
                try {
                    getReceiverList({ contractId: this.contractId }).then((res) => {
                        this.receivers = res.data.map((item) => {
                            const labelName = item.userName;
                            return {
                                ...item,
                                labelName,
                            };
                        });
                        resolve(res.data);
                    });
                } catch (e) {
                    reject(e);
                }
            });
        },
        noticeBackForGenerateImages() {
            const url =  `/contract-api/contracts/${this.contractId}/generate-document-images`;
            return this.$http.get(url);
        },
        clickAddMark(type) {
            // 算出当前的标签的宽高
            const width = markInfo(type).width;
            const height = markInfo(type).height;
            const pageWidth = this.sendType === 'localSend' ? this.docList[0]?.page[0]?.width : this.docList[0]?.documentPages[0]?.width;
            const pageHeight = this.sendType === 'localSend' ? this.docList[0]?.page[0]?.height : this.docList[0]?.documentPages[0]?.height;
            // 随机生成的x轴范围,保证印章和签名都随机出现在页面的中间区域
            let rangeX;
            let rangeY;
            if (type === 'SEAL') {
                rangeX = getRandomInt(1 * width, pageWidth - 2 * width);
                rangeY = getRandomInt(3 * height, pageHeight - 4 * height);
            } else if (type === 'SIGNATURE') {
                rangeX = getRandomInt(1.5 * width, pageWidth - 2.5 * width);
                rangeY = getRandomInt(5 * height, pageHeight - 6 * height);
            } else {
                rangeX = getRandomInt(1.5 * width, pageWidth - 2.5 * width);
                rangeY = getRandomInt(8 * height, pageHeight - 9 * height);
            }

            if (this.sendType === 'localSend') {
                this.dragMark = {
                    ...markDefault,
                    type,
                    width,
                    height,
                    markI: this.marks.length, // 标签的索引
                    contractId: this.contractId, // 属于哪份合同
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId, // 该标签属于哪个签约方
                    x: rangeX, // 在合同中的x位置
                    y: rangeY, // 在合同中的y位置
                };
            } else {
                this.dragMark = {
                    ...templateMarkDefault,
                    labelType: type,
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId,
                    documentId: this.docList[this.curDocIndex].documentId,
                    markI: this.marks.length,
                    labelPosition: {
                        x: rangeX,
                        y: rangeY,
                        width,
                        height,
                    },
                };
            }
            // 将初始化的mark进行二次计算，计算出后端接口需要的数据
            const computeMark = this.computePosition('end');
            // 将该计算后的数据请求后端创建标签接口创建标签
            this.createNewMarks({ ...this.dragMark, ...computeMark });
        },
        dragStart(type, e, activeMarkI = -1) {
            const { clientX, clientY }  = e.touches[0] || e.changedTouches[0];
            this.dragIconPosition = {
                x: clientX + 'px',
                y: clientY + 'px',
            };
            this.dragType = type;
            this.dragStatus = 'start';
            this.isDrag = true;
            this.activeMarkI = activeMarkI;

            if (activeMarkI === -1) {
                this.originDragMark = {
                    ...markDefault,
                    markI: this.marks.length,
                    contractId: this.contractId,
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId,
                    x: clientX,
                };
                this.marks.push(this.originDragMark);
                this.activeCenter = {
                    x: 0,
                    y: 0,
                };
            } else {
                this.originDragMark = this.marks[this.activeMarkI];
                if (this.sendType === 'templateSend') {
                    const { labelPosition: { x, y } } = this.originDragMark;
                    this.activeCenter = {
                        x: clientX - x,
                        y: clientY + this.scrollTop - y,
                    };
                } else {
                    const { x, y } = this.originDragMark;
                    this.activeCenter = {
                        x: clientX - x,
                        y: clientY + this.scrollTop - y,
                    };
                }
            }
        },
        dragMove(e) {
            if (!this.isDrag) {
                return false;
            }
            this.isScroll = false;
            this.updateCurrent(e, 'move');
        },
        dragEnd(e) {
            this.isScroll = true;

            if (this.dragStatus === 'end') {
                return;
            }
            this.updateCurrent(e, 'end');
            const mark = this.marks[this.activeMarkI > -1 ? this.activeMarkI : this.marks.length - 1];
            if (mark.labelPosition?.width || mark.width) {
                this.createNewMarks(mark);
            } else {
                this.marks.pop();
            }
        },
        // dragStart/dragEnd更新当前临时位置
        updateCurrent(e, way) {
            console.log('update', way, e);
            const { clientX, clientY } = e.touches[0] || e.changedTouches[0];
            this.dragIconPosition = {
                x: clientX + 'px',
                y: clientY + 'px',
            };
            const maxWidth = uni.upx2px(750);
            const width = markInfo(this.dragType).width;
            const height = markInfo(this.dragType).height;
            let finallyClientX = clientX;
            let finallyClientY = clientY;
            // 修正边界场景
            // 右边
            if (clientX + (width - this.activeCenter.x) > maxWidth) {
                finallyClientX = maxWidth - (width - this.activeCenter.x);
            }
            // 左边
            if (clientX < this.activeCenter.x) {
                finallyClientX = this.activeCenter.x;
            }
            // 上边
            if (clientY < 0) {
                finallyClientY = 0;
            }
            // 拖动位置相对屏幕窗口的位置记录
            if (this.sendType === 'localSend') {
                this.dragMark = {
                    ...markDefault,
                    contractId: this.contractId,
                    x: finallyClientX,
                    y: finallyClientY,
                    type: this.dragType,
                    width,
                    height,
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId,
                };
            } else {
                this.dragMark = {
                    ...templateMarkDefault,
                    labelType: this.dragType,
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId,
                    documentId: this.docList[this.curDocIndex].documentId,
                    labelPosition: {
                        x: finallyClientX,
                        y: finallyClientY,
                        width,
                        height,
                    },
                };
            }
            this.dragStatus = clientY <= this.height.pageHeight ? 'move' : 'start';

            if (this.dragStatus === 'start') {
                if (way === 'end') {
                    // 如果是拖拽结束位置不对，释放掉拖拽手柄
                    this.dragStatus = 'end';
                }
                return false;
            }
            // 实时更新位置
            const position = this.computePosition(way);
            console.log('position', position);
            if (this.activeMarkI > -1) {
                // 移动
                this.$set(this.marks, this.activeMarkI, {
                    ...this.marks[this.activeMarkI],
                    ...position,
                });
            } else if (!this.marks[this.marks.length - 1].labelId) {
                // 底部按钮拖拽而来
                this.$set(this.marks, this.marks.length - 1, {
                    ...this.marks[this.marks.length - 1],
                    ...position,
                    type: this.dragType,
                    receiverId: this.receivers[this.receiverActiveIndex].receiverId,
                });
            }
            console.log('this.marks', this.marks);
        },
        // 创建新标签
        async createNewMarks(mark) {
            // 位置未发生改变。不请求接口
            const originMark = this.originDragMark;
            const originPosition = this.sendType === 'localSend' ? originMark : originMark.labelPosition;
            const markPosition = this.sendType === 'localSend' ? mark : mark.labelPosition;

            if (originPosition && markPosition && originPosition.x === markPosition.x && originPosition.y === markPosition.y) {
                return true;
            }

            let newMark;
            if (this.sendType === 'localSend') {
                newMark = {
                    ...mark,
                    x: mark.x_really,
                    y: mark.y_really,
                    width: mark.width_really,
                    height: mark.height_really,
                };
            } else {
                newMark = {
                    ...mark,
                    labelPosition: {
                        x: mark.labelPosition.x_really,
                        y: mark.labelPosition.y_really,
                        width: mark.labelPosition.width_really,
                        height: mark.labelPosition.height_really,
                    },
                };
            }

            const position = this.sendType === 'localSend' ? newMark : newMark.labelPosition;
            delete position.x_really;
            delete position.y_really;
            delete position.width_really;
            delete position.height_really;
            // 修正边界
            if (position.y + position.height > 1) {
                position.y = 1 - position.height;
            }
            position.x = getValueByScope(position.x, 0, 1);
            position.y = getValueByScope(position.y, 0, 1);
            if (position.y + position.height > 1) {
                position.y = 1 - position.height;
            }
            position.x = getValueByScope(position.x, 0, 1);
            position.y = getValueByScope(position.y, 0, 1);
            try {
                if (this.sendType === 'localSend') {
                    const { data } = await this.$http.post(`/contract-api/contracts/${this.contractId}/labels/create-and-modify/`, [newMark]);
                    await this.updateMark('add', {
                        ...mark,
                        labelId: data[0].labelId, // 只更新labelId
                        x_really: newMark.x,
                        y_really: newMark.y,
                    });
                } else {
                    if (newMark.labelType === 'DATE') {
                        newMark.labelExtends = {
                            ...newMark.labelExtends,
                            dateFieldFormat: 'yyyy年MM月dd日',
                        };
                    }
                    const { data } = await saveLabel({ draftId: this.contractId, label: newMark });
                    await this.updateMark('add', {
                        ...mark,
                        labelId: data[0].labelId, // 只更新labelId
                        labelPosition: {
                            ...mark.labelPosition,
                            x_really: newMark.labelPosition.x,
                            y_really: newMark.labelPosition.y,
                        },
                    });
                }
            } finally {
                // 接口请求回来之后再释放临时标签，优化更新时闪动
                this.dragStatus = 'end';
                this.isDrag = false;
            }
        },
        // 计算拖拽临时标签所在的文档位置以及返回给后端的x/y/width/height
        computePosition(way) {
            const y = this.scrollTop + (this.sendType === 'localSend' ? this.dragMark.y : this.dragMark.labelPosition.y);
            let accHeight = 0;
            let result = {};
            const SEND_TITLE_HEIGHT = this.sendType === 'localSend' ? 0 : uni.upx2px(144);
            this.docList.some((doc, docIndex) => {
                let has = false;
                (this.sendType === 'localSend' ? doc.page : doc.documentPages).some((page, pageIndex) => {
                    if (this.sendType === 'templateSend') {
                        if (pageIndex === 0) {
                            accHeight = 0; // 模板发送每份文档单独计算，不累加，本地发送文档页数是累加的
                        }
                    }
                    accHeight += page.height + ((docIndex + pageIndex) ? 10 : 0); // 第一个页面位置不能加上margin 10
                    if (y - SEND_TITLE_HEIGHT < accHeight) {
                        has = true;
                        const tempY = page.height - (accHeight - y);
                        // 不能直接复制this.dragMark.y，影响临时位置
                        let tempMask;
                        if (this.sendType === 'localSend') {
                            tempMask = {
                                ...this.dragMark,
                                y: tempY - this.activeCenter.y, // 标签距离当前页面顶部的距离
                                x: this.dragMark.x - this.activeCenter.x,
                            };
                        } else {
                            tempMask = {
                                ...this.dragMark,
                                labelPosition: {
                                    ...this.dragMark.labelPosition,
                                    y: tempY - this.activeCenter.y, // 标签距离当前页面顶部的距离
                                    x: this.dragMark.labelPosition.x - this.activeCenter.x,
                                },
                            };
                        }
                        const position = this.sendType === 'localSend' ? tempMask : tempMask.labelPosition;
                        const temp = markCoordinateTransform(position, page.width, page.height);
                        const x_page = (this.sendType === 'localSend' ? this.dragMark.x : this.dragMark.labelPosition.x) - this.activeCenter.x;
                        let y_page = (this.sendType === 'localSend' ? this.dragMark.y : this.dragMark.labelPosition.y) + this.scrollTop - this.activeCenter.y;

                        // 修正：上下拖动边界值限制
                        if (way === 'end' || way === 'move') {
                            // 最下边
                            const height = this.sendType === 'localSend' ? this.dragMark.height : this.dragMark.labelPosition.height;
                            if (y_page - accHeight < 0 && y_page - accHeight > -height) {
                                y_page = accHeight - height;
                            }
                            // 最上边
                            if (y_page < accHeight - page.height) {
                                y_page = accHeight - page.height;
                            }
                        }

                        if (this.sendType === 'localSend') {
                            result = {
                                docI: docIndex,
                                pageI: pageIndex,
                                pageNumber: pageIndex + 1,
                                documentId: doc.documentId,
                                ...temp,
                                x: x_page, // 页面展示标签的x值
                                y: y_page, // 页面展示标签的y值
                                width: this.dragMark.width,
                                height: this.dragMark.height,
                                y_really: temp.y, // 传给后端的数据, 为小数
                                x_really: temp.x, // 传给后端的数据, 为小数
                                width_really: temp.width, // 传给后端的数据, 为小数
                                height_really: temp.height, // 传给后端的数据, 为小数
                            };
                        } else {
                            result = {
                                pageNumber: pageIndex + 1,
                                documentId: this.docList[this.curDocIndex].documentId,
                                labelPosition: {
                                    ...this.dragMark.labelPosition,
                                    x: x_page, // 页面展示标签的x值
                                    y: y_page, // 页面展示标签的y值
                                    width: this.dragMark.labelPosition.width,
                                    height: this.dragMark.labelPosition.height,
                                    y_really: temp.y, // 传给后端的数据, 为小数
                                    x_really: temp.x, // 传给后端的数据, 为小数
                                    width_really: temp.width, // 传给后端的数据, 为小数
                                    height_really: temp.height, // 传给后端的数据, 为小数
                                },
                            };
                        }
                    }
                    return has;
                });
                return has;
            });
            return result;
        },
        // 计算当时显示的文档位置
        handleScroll(e) {
            this.scrollTop = e.detail.scrollTop;
        },
        /**
         * @description 更新mark
         * @param type=delete/add
         */
        async updateMark(type, mark) {
            if (type === 'delete' && this.marks[mark.markI] && this.marks[mark.markI].labelId) {
                if (this.sendType === 'localSend') {
                    await deleteMark({ draftId: this.contractId, labelId: this.marks[mark.markI].labelId });
                } else {
                    await deleteLabel({ draftId: this.contractId, labelId: this.marks[mark.markI].labelId });
                }
                this.marks.splice(mark.markI, 1);
                // 更新markI，fix连续删除问题
                this.marks.map((item, index) => {
                    item.markI = index;
                });
            } else {
                this.$set(this.marks, mark.markI, {
                    ...this.marks[mark.markI],
                    ...mark,
                });
            }
            this.allMarks[this.docList[this.curDocIndex].documentId] = this.marks;
        },
        // 文档切换成高清
        switchHighQualityImage() {
            this.docList.forEach((doc) => {
                (this.sendType === 'localSend' ? doc.page : doc.documentPages).forEach((page) => {
                    page.imagePreviewUrl = page.highQualityPreviewUrl;
                });
            });
            this.switchHighQualityImageBtnShow = false;
        },
    },
};
</script>

<style lang="scss">
// css单位使用rpx, 直接使用2倍尺寸【设计稿750】
// 动态绑定rpx不生效，可使用uni.upx2px(750) + px 计算
$mainColor: #127fd2;
$bgColor: #F1F2F3;
.send {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 750rpx;
    line-height: 1;
    background: $bgColor;
    &-title {
        height: 140rpx;
        background: $--color-white;
        border-top: 1rpx solid $--border-color-lighter;
        border-bottom: 1rpx solid $--border-color-lighter;
        box-sizing: border-box;
        &__tip {
            color: $--color-info;
            padding: 20rpx 30rpx 12rpx;
        }
    }
    &-content {
        background: $bgColor;
        &-container {
            position: relative;
        }
        &-hqi {
            position: fixed;
            left: 50%;
            transform: translateX(-50%);
            bottom: 180rpx;
            background: #7f7f7f;
            font-size: 24rpx;
            width: 200rpx;
            height: 50rpx;
            line-height: 50rpx;
            border-radius: 8rpx;
            text-align: center;
            color: #ffffff;
        }
        &-doc {
            width: 750rpx;
            position: relative;
            height: 100%;
        }
        &-page {
            width: 750rpx;
            position: relative;
            box-sizing: border-box;
        }
    }
    .drag-icon {
        width: 80rpx;
        height: 80rpx;
        text-align: center;
        border-radius: 70rpx;
        background: #666666;
        position: fixed;
        font-size: 38rpx;
        line-height: 80rpx;
        color: $mainColor;
    }
    .send-footer-container {
        width: 750rpx;
    }
    .water-mark-back {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
    .riding-seals {
        position: absolute;
        box-sizing: border-box;
        padding-top: 2rpx;
        width: 154rpx;
        border: 2rpx dashed #127FD2;
        background: rgba(18, 127, 210, 0.05);
        top: 0;
        bottom: 0;
        right: -120rpx;
        z-index: 1;
        height: calc(100% - 10px);
        .riding-seals-bg {
            width: 110rpx;
            margin-left: 40rpx;
            height: 100%;
            background: #FBFBFB;
            background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.50) 0%, rgba(217, 217, 217, 0.50) 100%);
            background-size: 28rpx;
        }
    }
    .segmented-control {
        width: 100%;
        overflow-x: scroll;
        // #ifdef MP-ALIPAY
        width: 100vw;
        overflow: auto;
        display: block;
        white-space: nowrap;
        &__item{
            display: inline-block;
            white-space: nowrap;
        }
        // #endif
    }
    .segmented-control__item {
        view {
            white-space: nowrap;
            margin: 0 20rpx;
        }
    }

}
</style>

