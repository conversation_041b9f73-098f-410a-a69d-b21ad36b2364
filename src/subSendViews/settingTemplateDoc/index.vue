<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <TemplateDocList
            :curDocIndex="curDocIndex"
            :draftId="draftId"
            @changeCurDocIndex="handleChangeDoc"
            @uploadFileSuccess="handleUploadFileSuccess"
            @uploadFileList="handleUploadFileList"
        ></TemplateDocList>
        <TemplateDocForm
            :curDocIndex="curDocIndex"
            :draftId="draftId"
            @deleteDoc="handleDeleteDoc"
        ></TemplateDocForm>
        <view class="setting-template__btn" slot="footer" @click="handleSubmit">
            下一步
        </view>
    </xd-page>
</template>
<script>
import TemplateDocList from 'components/templateDocList';
import TemplateDocForm from 'components/templateDocForm';
import xdPage from 'components/xdPage';
import { getDraftId, getDraftDocumentsInfo, getContractTypes, postDraftDocumentsInfo, deleteDraftDoc, getFdaIfOpened, getInternalSignIfOpened } from 'api/template.js';
import { mapState, mapMutations, mapActions } from 'vuex';
import cloneDeep from 'lodash/cloneDeep';
import _get from 'lodash/get';
import debounce from 'src/utils/debounce.js';
import { authMixin } from 'mixins/auth';
export default {
    components: {
        TemplateDocList,
        TemplateDocForm,
        xdPage,
    },
    mixins: [authMixin],
    data() {
        return {
            templateId: '',
            editDocumentIds: [],
            draftId: '',
            curDocIndex: 0,
            temporaryDocData: [], // 上传完文档后端返回的临时文档数据
        };
    },
    computed: {
        ...mapState('template', ['templateList', 'templateDocList']),
    },
    methods: {
        ...mapMutations('template', ['setTemplateList', 'setTemplateDocList', 'setContractTypes', 'deleteTemplateDoc', 'addTemplateDoc', 'setDraftId']),
        ...mapMutations('send', ['setContractId']),
        ...mapActions('template', ['getTemplatePermission']),
        ...mapActions(['setLoading']),
        ...mapMutations(['setToastParam']),
        // 获取草稿对应的文档信息（包含描述字段和内容字段等详细信息）
        getDraftDocsInfo() {
            return getDraftDocumentsInfo({ draftId: this.draftId }).then((res) => {
                this.setTemplateDocList(this.normalizeDocsData(res.data));
            });
        },
        async checkIfSupport() {
            const { data: { supportFDA } } = await getFdaIfOpened({ draftId: this.draftId });
            if (supportFDA) {
                this.$toast.error('暂不支持配置了FDA的模板发送');
                setTimeout(() => {
                    uni.navigateBack({ delta: 1 });
                }, 3000);
                return false;
            }
            const { data: { isInternalSignTemplate } } = await getInternalSignIfOpened({ draftId: this.draftId });
            if (isInternalSignTemplate) {
                this.$toast.error('暂不支持开启了对内文件签字的模板发送');
                setTimeout(() => {
                    uni.navigateBack({ delta: 1 });
                }, 3000);
                return false;
            }
            return true;
        },
        initDraft() {
            if (this.draftId) {
                this.setDraft();
                return;
            }
            const curTemplateDocList = this.templateList.find(item => item.templateId === this.templateId)?.documents || [];
            this.editDocumentIds = curTemplateDocList.map(item => item.documentId);
            return getDraftId({ templateId: this.templateId, editDocumentIds: this.editDocumentIds }).then(({ data }) => {
                this.draftId = _get(data, 'draftId', '');
                if (this.checkIfSupport()) {
                    this.setDraft();
                }
            });
        },
        setDraft() {
            this.setDraftId(this.draftId);
            this.setContractId(this.draftId);
            this.getTemplatePermission(this.draftId);
        },
        normalizeDocsData(data = []) {
            const cloneData = cloneDeep(data);
            return cloneData.map(doc => {
                const contentNecessaryField = doc.contentFieldLabels.filter(item => !!item.necessary) || [];
                const descriptionNecessaryField = doc.descriptionFieldConfigs.filter(item => !!item.necessary) || [];
                const contentFieldHasComplete = contentNecessaryField.every(item => item.labelValue) || !contentNecessaryField.length;
                const descriptionFieldHasComplete = descriptionNecessaryField.every(item => item.labelValue) || !descriptionNecessaryField.length;
                doc.fieldHasComplete = contentFieldHasComplete && descriptionFieldHasComplete;
                // 内容字段
                doc.contentFieldLabels = (doc.contentFieldLabels || []).map(label => {
                    // 复选框的返回值是String，要处理成Array
                    if (label.labelType === 'CHECKBOX' && label.labelValue) {
                        label.labelValue = label.labelValue.split(',');
                    }
                    return label;
                });
                return doc;
            });
        },
        handleUploadFileSuccess(data) {
            this.temporaryDocData = data;
        },
        handleUploadFileList(fileList) {
            const docTitle = fileList[fileList.length - 1].name;
            const curDocData = {
                ...this.temporaryDocData,
                contractConfig: {
                    ...this.temporaryDocData.contractConfig,
                    contractTitle: docTitle,
                },
                descriptionFieldConfigs: (this.temporaryDocData.descriptionFieldConfigs || []).map(field => {
                    if (field.fieldName === 'contractTitle' && docTitle) {
                        field.fieldValue = docTitle;
                    }
                    return field;
                }),
            };
            this.addTemplateDoc(this.normalizeDocsData([curDocData])[0]);
            this.curDocIndex = this.templateDocList.length - 1;
            this.getContractTypes();
        },
        handleChangeDoc(index) {
            this.curDocIndex = index;
        },
        getContractTypes() {
            getContractTypes().then((res) => {
                this.setContractTypes(res.data || []);
            });
        },
        validateInputInfo() {
            if (!this.templateDocList.length) { // 文档数验证
                return this.$toast.error('请上传合同文档');
            }

            // 空文档验证
            if (this.templateDocList.filter(contract => contract.documentType === 'BLANK').length > 0) {
                return this.$toast.error('存在空白文档，请补充上传合同内容');
            }
            let contentFieldName = '';
            let descriptionFieldName = '';
            let docName = '';
            for (const index in this.templateDocList) {
                const doc = this.templateDocList[index];
                const descriptionFieldNotFill = doc.descriptionFieldConfigs.filter(item => !!item.necessary && !item.fieldValue) || [];
                if (descriptionFieldNotFill.length) {
                    descriptionFieldName = descriptionFieldNotFill[0].fieldName;
                    docName = doc?.contractConfig?.contractTitle || '';
                    break;
                }
                const contentFieldNotFill = doc.contentFieldLabels.filter(item =>  !!item.necessary && !item.labelValue) || [];
                if (contentFieldNotFill.length) {
                    contentFieldName = contentFieldNotFill[0].labelName;
                    docName = doc?.contractConfig?.contractTitle || '';
                    break;
                }
            }
            if (descriptionFieldName) {
                this.setToastParam({ show: true, message: `请将《${docName}》的描述字段“${descriptionFieldName}”填写完整` });
                setTimeout(() => {
                    this.setToastParam({ show: false, message: '' });
                }, 2000);
                return;
            } else if (contentFieldName) {
                this.setToastParam({ show: true, message: `请将《${docName}》的内容字段“${contentFieldName}”填写完整` });
                setTimeout(() => {
                    this.setToastParam({ show: false, message: '' });
                }, 2000);
                return;
            }
            return true;
        },
        formatSubmitInfo(data = []) {
            // 深拷贝一下 防止操作原数据
            const cloneData = cloneDeep(data);
            return cloneData.map((item, index) => {
                item.order = index;
                if (item.descriptionFieldConfigs && item.descriptionFieldConfigs.length) {
                    item.descriptionFieldConfigs.forEach(field => {
                        // 当合同描述字段为字符串时 去除两端空格
                        if (typeof field.fieldValue === 'string') {
                            field.fieldValue = field.fieldValue.trim();
                        }
                    });
                }
                if (item.contentFieldLabels && item.contentFieldLabels.length) {
                    item.contentFieldLabels.forEach(label => {
                        // 复选框值要格式化为string提交
                        if (label.labelType === 'CHECKBOX' && label.labelValue) {
                            label.labelValue = label.labelValue.join(',');
                        }
                    });
                }
                return item;
            });
        },
        handleSubmit: debounce(function() {
            if (this.entList[this.curEntIndex].authStatus !== 2) {
                this.handleGoAuth({
                    curIndex: this.curEntIndex,
                    showCancel: false,
                });
                return;
            }
            if (!this.validateInputInfo()) {
                return;
            }
            this.setLoading(true);
            const documentBasicInfos = this.formatSubmitInfo(this.templateDocList);
            postDraftDocumentsInfo({ draftId: this.draftId, documentBasicInfos }).then(() => {
                uni.navigateTo({ url: '/subSendViews/addReceiverList/index' });
            }).finally(() => {
                this.setLoading(false);
            });
        }),
        handleDeleteDoc() {
            this.$toast.loading({ mask: true });
            deleteDraftDoc({ draftId: this.draftId, documentId: this.templateDocList[this.curDocIndex].documentId }).then(() => {
                this.deleteTemplateDoc(this.curDocIndex);
                this.curDocIndex = this.templateDocList.length - 1;
            }).finally(() => {
                this.$toast.hideLoading();
            });
        },
    },

    async onLoad(options) {
        const { templateId, draftId } = options;
        this.templateId = templateId;
        this.draftId = draftId;
        try {
            await this.initDraft();
            await this.getDraftDocsInfo();
            await this.getContractTypes();
            if (this.entList[this.curEntIndex].authStatus !== 2) {
                this.handleGoAuth({
                    curIndex: this.curEntIndex,
                    showCancel: false,
                });
            }
        } catch (err) {
            setTimeout(() => {
                uni.navigateBack({ delta: 1 });
            }, 2000);
        }
    },
};
</script>
<style lang="scss">
.setting-template__btn {
    width: 670rpx;
    height: 90rpx;
    line-height: 90rpx;
    background: $--color-primary;
    font-size: 32rpx;
    color: $--color-white;
    text-align: center;
    border-radius: 8rpx;
    margin-top: 20rpx;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}
</style>
