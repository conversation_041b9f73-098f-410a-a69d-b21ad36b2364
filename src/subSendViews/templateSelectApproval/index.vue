<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10">
        <view class="template-approval-page">
            <view class="template-approval-page__tip">请为不同的合同类型，设置相应审批流程：</view>
            <scroll-view :scroll-y="true" class="template-approval-page__scroll">
                <view v-for="contractType in groupedWorkFlows" :key="contractType.contractTypeId">
                    <view class="template-approval-page__title">{{ `为合同类型${contractType.contractTypeName}设置审批流` }}</view>
                    <radio-group @change="selectChange(contractType.contractTypeId, $event)">
                        <view v-for="entName in Object.keys(contractType.workflowVOList)" :key="entName">
                            <view class="template-approval-page__header">{{ `使用${entName}的审批流` }}</view>
                            <label
                                v-for="(workflow) in contractType.workflowVOList[entName]"
                                :key="workflow.workflowId"
                                class="template-approval-page__radios"
                            >
                                <view class="template-approval-page__text">
                                    <radio
                                        :value="workflow.workflowId"
                                        :checked="workflow.workflowId === selectedFlow[contractType.contractTypeId]"
                                        color="#127FD2"
                                    />
                                    <text>{{ workflow.name }}</text>
                                </view>
                                <view class="template-approval-page__tip2">{{ workFlowTypeMap[workflow.workflowType] }}</view>
                            </label>
                        </view>
                    </radio-group>
                </view>
            </scroll-view>
        </view>
        <view class="template-approval-page__btn" slot="footer" @click="goToNext">下一步</view>
    </xd-page>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import xdPage from '@/components/xdPage/index.vue';
import groupBy from 'lodash/groupBy';
import debounce from 'src/utils/debounce.js';
export default {
    components: {
        xdPage,
    },
    data() {
        return {
            curIndex: 0,
            selectedFlow: {},
            workFlowTypeMap: {
                'BEFORE_SEND': '发送前审批',
                'BEFORE_SIGN': '签署前审批',
            },
        };
    },
    computed: {
        ...mapState('template', ['templateFlowList']),
        // 根据合同类型分组之后，再根据entName进行二次分组
        groupedWorkFlows() {
            return this.templateFlowList.reduce((total, workflow) => {
                total.push({
                    ...workflow,
                    workflowVOList: groupBy(workflow.workflowVOList, 'entName'),
                });
                return total;
            }, []);
        },
    },
    methods: {
        ...mapMutations('send', ['setDefineIndex']),
        ...mapMutations('template', ['setTemplateSelectedFlow']),
        goToNext: debounce(function() {
            const workflowIdList = Object.values(this.selectedFlow).filter(workflowId => !!workflowId);
            if (workflowIdList.length < this.templateFlowList.length) {
                return this.$toast.error('请先选择审批流');
            }
            uni.navigateTo({
                url: '/subSendViews/templateSubmitApproval/index',
            });
        }),
        selectChange(contractTypeId, e) {
            const workflowId = e.detail.value;
            this.selectedFlow[contractTypeId] = workflowId;
            this.setTemplateSelectedFlow(this.selectedFlow);
        },
    },
};
</script>

<style lang="scss">
.template-approval-page {
    background: $--background-color-base;
    height: 100%;
    color: $--color-text-primary;
    &__scroll {
        height: calc(100% - 200rpx);
    }
    &__tip {
        background-color: $--color-white;
        color: $--color-info;
        height: 68rpx;
        line-height: 68rpx;
        border-top: 1rpx solid $--border-color-lighter;
        padding: 0 30rpx;
        font-size: 24rpx;
    }
    &__title {
        margin-top: 30rpx;
        padding: 0 30rpx;
    }
    &__header {
        padding: 0 30rpx;
        color: $--color-info;
        margin: 20rpx 0;
        font-size: 24rpx;
    }
    &__tip2 {
        color: $--color-info;
    }
    &__radios {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 30rpx;
        background-color: $--color-white;
        border-bottom: 1rpx solid $--border-color-lighter;
        view {
            height: 100rpx;
            line-height: 100rpx;
        }
    }
    &__text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 500rpx;
    }
    &__btn {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
}
</style>

