<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10" class="send_approve">
        <view class="submit-approve-page">
            <view class="submit-approve-page__tip">{{ '请为各审批流，填写相应的内容' }}</view>
            <scroll-view :scroll-y="true" class="submit-approve-page__content">
                <view v-for="(workflow, workflowIndex) in flowInfos" :key="workflowIndex">
                    <view class="submit-approve-page__title">{{ workFlowTypeMap[workflow.workflowType] }}</view>
                    <template v-if="workflow.customApproveInfo && workflow.customApproveInfo.byExternalSystem">
                        <view class="submit-approve-page__title">
                            <p>{{ `最终生效的${workFlowTypeMap[workflow.workflowType]}为自定义审批，需在其他系统中审批通过后，合同才能发送成功。` }}</p>
                            <p>{{ '注：贵司需对接使用上上签平台API，否则审批状态将无法消除。' }}</p>
                        </view>
                    </template>
                    <template v-else>
                        <view v-for="(node, nodeIndex) in workflow.flowStepList" :key="nodeIndex">
                            <view v-if="node.employeeName" class="submit-approve-page__item">
                                <image v-if="node.portraitSrc" :src="node.portraitSrc" style="width: 35px; height: 30px" />
                                <image v-else :src="personImg" style="width: 35px; height: 30px" />
                                <view class="submit-approve-page__name">{{ node.employeeName || '' }}</view>
                            </view>
                            <view v-else class="submit-approve-page__item">
                                <image :src="personImg" style="width: 30px; height: 30px" />
                                <view class="submit-approve-page__margin">{{ node.flowRoleName || '' }}</view>
                                <picker
                                    :value="selectedIndex"
                                    range-key="employeeName"
                                    :range="filterSearch(node.searchValue, node.defineApprovers)"
                                    @change="handleClickName(node, nodeIndex, workflowIndex, $event)"
                                >
                                    <span v-if="selectedIndex === -1" class="submit-approve-page__msg-placeholder">请选择</span>
                                    <span v-else>{{ (filterSearch(node.searchValue, node.defineApprovers)[selectedIndex] || {}).employeeName }}</span>
                                </picker>
                            </view>
                        </view>
                        <view class="submit-approve-page__msg">
                            <input
                                :value="privateMessage(workflowIndex)"
                                placeholder="请输入审批理由"
                                placeholder-class="submit-approve-page__msg-placeholder"
                                :maxlength="255"
                                @input="updatePrivateMessage(workflowIndex, $event)"
                            />
                        </view>
                    </template>
                </view>
            </scroll-view>
        </view>
        <view class="submit-approve-page__footer" slot="footer" @click="handleSubmit">提交审批</view>
    </xd-page>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import xdPage from '@/components/xdPage/index.vue';
import { submitWorkflow } from 'src/api/template.js';
import personImg from '@/assets/images/icon_person.png';
export default {
    components: {
        xdPage,
    },
    data() {
        return {
            flowInfos: [],
            workFlowTypeMap: {
                'BEFORE_SEND': '发送前审批',
                'BEFORE_SIGN': '签署前审批',
            },
            personImg: personImg,
            selectedIndex: -1,
        };
    },
    computed: {
        ...mapState('send', ['sendType', 'contractId']),
        ...mapState('template', ['templateFlowList', 'templateSelectedFlow']),
        filterSearch() {
            return (searchValue,  defineApprovers) => {
                if (!searchValue) {
                    return defineApprovers;
                }
                return defineApprovers.filter(approver => (approver.employeeName || '').toLowerCase().includes(searchValue.toLowerCase()));
            };
        },
        privateMessage() {
            return (workflowIndex) => {
                return ((this.flowInfos[workflowIndex]?.flowStepList || []).length && this.flowInfos[workflowIndex]?.flowStepList[0]?.privateLetter) || '';
            };
        },
    },
    methods: {
        ...mapActions('send', ['getCharging']),
        // 选择审批人
        handleClickName(node, nodeIndex, workflowIndex, e) {
            const member = this.filterSearch(node.searchValue, node.defineApprovers)[e.detail.value];
            node.employeeId = member.employeeId;
            node.employeeName = member.employeeName;
            node.userId = member.userId;
            node.portraitSrc = `${this.$http.baseURL}/ents/${node.enterpriseId}/employees/${node.employeeId}/portrait?access_token=${uni.getStorageSync('accessToken')}` || '';
            this.$set(this.flowInfos[workflowIndex].flowStepList, nodeIndex, node);
            this.selectApproverShow = false;
        },
        // 根据审批流id获取审批流
        getFlowSteps() {
            const allWorkflows = this.templateFlowList.reduce((total, workflow) => {
                return total.concat(workflow.workflowVOList);
            }, []);
            const workflowIdList = Object.values(this.templateSelectedFlow).filter(workflowId => !!workflowId);
            const beforeSignList = [];
            const beforeSendList = [];
            allWorkflows.forEach(workflow => {
                if (workflowIdList.includes(workflow.workflowId)) {
                    const flowStepList = (workflow.flowStepList || [])
                        .sort((a, b) => a.flowOrderNum > b.flowOrderNum)
                        .map(approver => {
                            return Object.assign({}, approver, {
                                portraitSrc: `${this.$http.baseURL}/ents/${approver.enterpriseId}/employees/${approver.employeeId}/portrait?access_token=${uni.getStorageSync('accessToken')}`,
                                workflowId: workflow.workflowId,
                            });
                        });
                    workflow.flowStepList = flowStepList;
                    workflow.workflowType === 'BEFORE_SEND' ? beforeSendList.push(workflow) : beforeSignList.push(workflow);
                }
            });

            const beforeSend = this.composeFlows(beforeSendList);
            const beforeSign = this.composeFlows(beforeSignList);

            const flowInfos = [];
            beforeSend && flowInfos.push(beforeSend);
            beforeSign && flowInfos.push(beforeSign);
            this.flowInfos = flowInfos;
        },
        composeFlows(flowList) {
            const byExternalSystemList = flowList.filter(item => item.byExternalSystem);
            if (byExternalSystemList && byExternalSystemList.length) {
                return {
                    workflowType: byExternalSystemList[0].workflowType,
                    customApproveInfo: {
                        workflowId: byExternalSystemList[0].workflowId,
                        byExternalSystem: byExternalSystemList[0].byExternalSystem,
                    },
                };
            }
            const initTotal = (flowList[0] || {}).flowStepList || [];
            const flowStepList = flowList.reduce((total, item, index) => {
                if (index === 0) {
                    return total;
                }
                const temp = item.flowStepList.filter(step => {
                    return !total.some(i => (step.userId && i.userId === step.userId) || (step.roleId && step.roleId === i.roleId));
                });
                return total.concat(temp);
            }, initTotal);
            if (!flowStepList.length) {
                return null;
            }
            return {
                workflowType: flowList[0].workflowType,
                flowStepList,
            };
        },
        // 提交审批流
        postFlowInfo() {
            const reject = this.flowInfos.some(flow => !(flow.customApproveInfo && flow.customApproveInfo.byExternalSystem) && flow.flowStepList.some(item => item.employeeName === null));
            if (reject) {
                this.$toast.error('您提交的审批流程不完整，请补全后重新提交');
                return Promise.reject();
            }

            return submitWorkflow({ draftId: this.contractId, workflows: this.flowInfos });
        },
        // 更新私信内容
        updatePrivateMessage(workflowIndex, e) {
            this.flowInfos[workflowIndex].flowStepList.forEach((recipient) => {
                recipient.privateLetter = e.detail.value;
            });
        },
        async handleSubmit() {
            await this.postFlowInfo();
            this.getCharging();
        },
    },
    onShow() {
        this.getFlowSteps();
    },
};
</script>

<style lang="scss">
.submit-approve-page {
    background: $--background-color-base;
    height: 100%;
    color: $--color-text-primary;
    &__content {
        height: calc(100% - 150rpx);
    }
    &__tip {
        background-color: $--color-white;
        color: $--color-info;
        height: 68rpx;
        line-height: 68rpx;
        border-top: 1rpx solid $--border-color-lighter;
        padding: 0 30rpx;
        font-size: 24rpx;
    }
    &__margin {
        margin: 0 20rpx 0 30rpx;
    }
    &__title {
        margin-top: 30rpx;
        padding: 0 30rpx;
    }
    &__name {
        width: 100%;
        margin-left: 30rpx;
    }
    &__msg {
        height: 100rpx;
        line-height: 100rpx;
        margin-top: 20rpx;
        background-color: $--color-white;
        padding: 0 30rpx;
        border-bottom: 1rpx solid $--border-color-lighter;
        input {
            height: 100rpx;
            line-height: 100rpx;
        }
    }
    &__msg-placeholder {
         color: $--color-text-placeholder;
    }
    &__item {
        height: 100rpx;
        line-height: 100rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 30rpx;
        background-color: $--color-white;
        border-bottom: 1rpx solid $--border-color-lighter;
    }
    &__footer {
        width: 670rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: $--color-primary;
        font-size: 32rpx;
        color: $--color-white;
        text-align: center;
        border-radius: 8rpx;
        margin-top: 20rpx;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
}
</style>
