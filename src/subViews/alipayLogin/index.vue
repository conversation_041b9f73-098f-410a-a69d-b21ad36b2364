<template>
    <view class="login">
        <!-- <official-account></official-account> -->
        <img class="login__logo" src="~img/logo.png" alt="">
        <div class="login__form">
            <h2>使用上上签账号登录</h2>
            <span class="login__form-tips">未注册用户将为您创建账号</span>
            <form class="login__form-content">
                <view class="login__form-items">
                    <input name="input" placeholder="请输入账号" :disabled="hasDefaultAccount" v-model="formData.account" @focus="handleClickEvent('account')" />
                </view>
                <aliyun-captcha
                    id="aliValidator"
                    v-if="showAdditionalAfs"
                    :props="pluginProps"
                />
                <view class="login__form-items">
                    <input name="input"
                        type="number"
                        placeholder="请输入验证码"
                        maxlength="6"
                        v-model="formData.verifyCode"
                        class="verify-code"
                        @focus="handleClickEvent('verifyCode')"
                    />
                    <view class="count-down" :class="{ 'disabled': time > 0 }" @click="validateBeforeSend">{{ codeText }}</view>
                </view>
                <button :loading="loginSubmitLoading" class="login__form-submit" :disabled="!agreeVisible || quickLoginLoading" type="primary" @click="toLogin">登录</button>
                <!-- #ifdef MP-WEIXIN -->
                <div class="btn-box" v-if="showQuickLogin">
                    <div class="btn-modal" v-show="!agreeVisible" @click="handleClickModal"></div>
                    <button class="quickLoginBtn"
                        :disabled="loginSubmitLoading"
                        :loading="quickLoginLoading"
                        open-type="getPhoneNumber"
                        type="primary"
                        @getphonenumber="getWxPhoneNumber"
                    >一键登录</button>
                </div>
                <!-- #endif -->
            </form>
        </div>
        <LoginAgreement v-model="agreeVisible"></LoginAgreement>
    </view>
</template>

<script>
import regRules from 'utils/regs';
import { mapState, mapGetters } from 'vuex';
import { getImageVerifyImg, getVerifyCode, ssqLogin, weWorkSsqLogin, wxQuickLogin } from 'api/account.js';
import LoginAgreement from 'components/loginAgreement';
import { myPatch } from 'src/common/patch';
import Vue from 'vue';
import store from 'store/index.js';
// eslint-disable-next-line no-undef
const AliyunCaptchaPluginInterface = requirePlugin('AliyunCaptcha');
export default {
    components: {
        LoginAgreement,
    },
    data() {
        return {
            agreeVisible: false,
            time: 0,
            showImgVerify: false,
            formData: {
                account: '',
                verifyCode: '',
                verifyKey: '',
            },
            imageVerifyCode: '',
            imageKey: '',
            imgSrc: '',
            loginSubmitLoading: false,
            quickLoginLoading: false,
            enterTime: 0,
            hasDefaultAccount: false,
            showAdditionalAfs: false,
            pluginProps: {},
            captchaButton: null,
        };
    },
    computed: {
        ...mapState(['weWorkSuiteId', 'isWeWork', 'captchaVerifyParam']),
        ...mapGetters(['mpSourceType']),
        codeText() {
            return this.time > 0 ? `重新发送(${this.time}s)` : '获取验证码';
        },
        showQuickLogin() {
            return this.$http.baseURL.includes('bestsign.cn');
        }
    },
    watch: {
        captchaVerifyParam(v) {
            // 智能验证通过发送验证码
            if (v) {
                this.send();
            }
        },
    },
    methods: {
        resetAliValidator() {
            store.state.captchaVerifyParam = '';
        },
        handleClickModal() {
            if (!this.agreeVisible) {
                return uni.showToast({
                    icon: 'none',
                    title: '请先阅读并同意相关协议',
                });
            }
        },
        getWxPhoneNumber({ detail }) {
            this.quickLoginLoading = true;
            const that = this;
            if (detail.errMsg === 'getPhoneNumber:ok') {
                const { encryptedData, iv } = detail;
                wx.login({
                    success({ code }) {
                        wxQuickLogin({ code, encryptedData, iv, sourceType: 101 }).then(({ data }) => {
                            that.handleLoginSuccess(data);
                        }).catch(err => {
                            that.handleLoginFail(err);
                            that.quickLoginLoading = false;
                        });
                    },
                });
                return;
            }
            this.quickLoginLoading = false;
        },
        handleClickEvent(icon) {
            const iconNameMap = {
                'account': '请输入账号',
                'verifyCode': '请输入验证码',
                'inputVerifyCode': this.codeText,
                'login': '登录',
                'imgVerifyCode': '请输入图形验证码',
            };
            this.$sensors.track({
                eventName: 'Mp_Login_BtnClick',
                eventProperty: {
                    icon_name: iconNameMap[icon],
                },
            });
        },
        async toLogin() {
            this.handleClickEvent('login');
            if (!this.agreeVisible) {
                return uni.showToast({
                    icon: 'none',
                    title: '请先阅读并同意相关协议',
                });
            }

            await this.checkAccount();
            await this.checkVerifyCode();

            this.login();
        },
        checkAccount() {
            return new Promise((resolve, reject) => {
                if (!this.formData.account) {
                    uni.showToast({
                        icon: 'none',
                        title: '请先输入账号',
                    });
                    reject();
                }

                if (this.formData.account && !regRules.userAccount.test(this.formData.account)) {
                    uni.showToast({
                        icon: 'none',
                        title: '请输入正确的手机号或邮箱',
                    });
                    reject();
                }

                resolve();
            });
        },
        checkVerifyCode() {
            return new Promise((resolve, reject) => {
                if (!this.formData.verifyKey) {
                    uni.showToast({
                        icon: 'none',
                        title: '请先获取验证码',
                    });
                    reject();
                }

                if (!this.formData.verifyCode) {
                    uni.showToast({
                        icon: 'none',
                        title: '请先输入验证码',
                    });
                    reject();
                }

                if (this.formData.verifyCode && !regRules.phoneVerifyCode.test(this.formData.verifyCode)) {
                    uni.showToast({
                        icon: 'none',
                        title: '验证码错误',
                    });
                    reject();
                }

                resolve();
            });
        },
        changeImg() {
            getImageVerifyImg().then((res) => {
                this.imageKey = res.data.imageKey;
                this.imgSrc = `data:image/jpeg;base64,${res.data.image}`;
            });
        },
        // 先智能验证再发验证码
        async validateBeforeSend() {
            // 弹出式下调用实例方法展示验证码
            this.handleClickEvent('inputVerifyCode');
            if (this.time > 0) {
                return;
            }
            await this.checkAccount();
            if (!store.state.captchaVerifyParam) {
                this.showAliCaptchaPlugin();
            }
        },
        showAliCaptchaPlugin() {
            AliyunCaptchaPluginInterface.show();
        },
        // 发送验证码
        send() {
            getVerifyCode(this.formData.account, this.imageVerifyCode, this.imageKey).then((res) => {
                uni.showToast({
                    icon: 'none',
                    title: '验证码发送成功',
                });
                this.formData.verifyKey = res.data.value;
                this.timer();
                this.resetAliValidator();
                if (res.data.msg) {
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        confirmText: '我知道了',
                        success: () => {
                        },
                    });
                }
            }).catch((err) => {
                if (err.response.data.code === '902' || err.response.data.code === '100006' || err.response.data.code === '100010') {
                    if (this.showAdditionalAfs) {
                        this.resetAliValidator();
                    } else {
                        this.showAliCaptchaPlugin();
                    }
                } else {
                    this.resetAliValidator();
                }
            });
        },
        timer() {
            this.time = 60;
            const inTimer = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    clearInterval(inTimer);
                }
            }, 1000);
        },
        async weWorkLogin() {
            return new Promise((resolve) => {
                const _formData = this.formData;
                const _mpSourceType = this.mpSourceType;

                console.log('init：' + this.weWorkSuiteId);

                wx.qy.login({
                    // suiteId: this.weWorkSuiteId, //非必填，第三方应用的suiteid，自建应用不填。若第三方小程序绑定多个第三方应用时，建议填上该字段
                    success: function(res) {
                        if (res.code) {
                            weWorkSsqLogin({
                                ..._formData,
                                sourceType: _mpSourceType,
                                code: res.code,
                            }).then(result => {
                                const _code = res.code;
                                const _resultData = result.data;
                                uni.setStorageSync('thirdCorpId', _resultData?.thirdCorpId || '');
                                // 等于2是因为没传thirdCorpId，后端认为是内部应用，需要再走内部应用关联的绑定接口
                                if (_resultData.code === 2) {
                                    resolve(ssqLogin({
                                        ..._formData,
                                        sourceType: _mpSourceType,
                                        code: _code,
                                        thirdCorpId: _resultData.thirdCorpId,
                                    }));
                                } else {
                                    resolve(result);
                                }
                            }).catch(err => {
                                resolve(false);
                                console.log('登录失败！' + err);
                            });
                        } else {
                            resolve(false);
                            console.log('登录失败！' + res.errMsg);
                        }
                    },
                });
            });
        },
        async wxLogin() {
            const { code } = await wx.login();
            return ssqLogin({
                ...this.formData,
                sourceType: this.mpSourceType,
                code,
            });
        },
        async login() {
            this.loginSubmitLoading = true;
            const loginPromise = this.isWeWork ? this.weWorkLogin() : this.wxLogin();

            loginPromise.then(({ data }) => {
                this.handleLoginSuccess(data);
            }).catch(err => {
                this.handleLoginFail(err);
            });
        },
        handleLoginFail(err) {
            this.loginSubmitLoading = false;
            const failCode = err.response?.data?.code || '';
            const failReason = err.response?.data?.message || '';
            this.$sensors.track({
                eventName: 'Mp_Login_Result',
                eventProperty: {
                    icon_name: '登录',
                    is_success: false,
                    fail_code: failCode,
                    fail_reason: failReason,
                },
            });
        },
        handleLoginSuccess(data) {
            const { access_token, refresh_token, userId, ssoUserRegister } = data;
            userId && this.$sensors.sensorsLogin(userId);
            this.$sensors.track({
                eventName: 'Mp_Login_Result',
                eventProperty: {
                    icon_name: '登录',
                    is_success: true,
                    is_first_time: ssoUserRegister || false,
                },
            });
            uni.setStorageSync('userId', userId);
            uni.setStorageSync('accessToken', access_token);
            uni.setStorageSync('refreshToken', refresh_token);
            const loginSuccessPage = uni.getStorageSync('loginSuccessPage');
            uni.reLaunch({
                url: loginSuccessPage ? decodeURIComponent(loginSuccessPage) : '/views/basic/index',
                success: () => {
                    loginSuccessPage && uni.removeStorageSync('loginSuccessPage');
                },
            });
        },
        initAliAutoValidator() {
            // uniapp对于vue2做了一层封装，其中一个限制是不允许object类型的props中有function，所以验证码组件无法获取并调用两个callback。
            this.originalPatch = Vue.prototype.__patch__; // 保存原有patch函数
            Vue.prototype.__patch__ = myPatch; // 替换patch函数

            const isPreEnv = this.$http.config.baseURL.includes('.info');
            const SceneId = isPreEnv ? 'da8li8ks' : 'da8li8ks'; // 小程序不区分环境
            const pluginProps = {
                SceneId,
                mode: 'popup',
                captchaVerifyCallback: this.captchaVerifyCallback, // 固定写法，需要绑定this
                onBizResultCallback: this.onBizResultCallback, // 固定写法，需要绑定this
                slideStyle: {
                    width: 540, // 宽度默认为540rpx
                    height: 90, // 高度默认为60rpx
                },
                language: 'cn',
                region: 'cn',
            };
            this.showAdditionalAfs = true;
            this.pluginProps = pluginProps;
        },
        async captchaVerifyCallback(captchaVerifyParam) {
            store.state.captchaVerifyParam = captchaVerifyParam;
            return {
                captchaResult: true,
                bizResult: true,
            };
        },
        // 验证通过后调用
        onBizResultCallback() {
            console.log('onBizResultCallback');
        },
    },
    onLoad(query) {
        const account = decodeURIComponent(query.account || '');
        this.hasDefaultAccount = !!account && account !== 'undefined';
        this.hasDefaultAccount && (this.formData.account = account);
        this.initAliAutoValidator();
    },
    onShow() {
        this.enterTime = new Date().getTime();
        this.$sensors.track({
            eventName: 'Mp_Login_PageView',
        });
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_Login_PageLeave',
            eventProperty: {
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    beforeDestroy() {
        Vue.prototype.__patch__ = this.originalPatch; // 还原patch函数
        this.captchaButton = null;
        // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
    },
};
</script>

<style lang="scss">
.login{
    padding-top: 160rpx;
    &__logo{
        display: block;
        width: 256rpx;
        height: 110rpx;
        margin: 0 auto 160rpx;
    }
	&__form{
		padding: 0 60rpx;
		&-tips{
			font-size: 26rpx;
			color: $--color-info;
		}
		&-content{
			margin-bottom: 20rpx;
		}
		&-items{
			margin-top: 20rpx;
			height: 100rpx;
			line-height: 100rpx;
			padding-left: 35rpx;
			background: #F8F8F8;
			border-radius: 8px;
			position: relative;
			overflow: hidden;
			border: 1px solid #eee;
			box-sizing: border-box;
			z-index: 9;
			input{
				display: block;
				height: 100rpx;
				&.verify-code{
					width: 300rpx;
				}
			}
			.count-down{
				position: absolute;
				top: 0;
				right: 35rpx;
				color: $--color-primary;
			}
			.image-verify-code{
				position: absolute;
				top: 0;
				right: 0;
				width: 200rpx;
				height: 100rpx;
			}
		}
		&-switch{
			float: right;
			color: $--color-primary;
		}
        .btn-box{
            position: relative;
            .btn-modal{
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                z-index: 9;
            }
            .quickLoginBtn{
                background: #1aad19;
            }
        }
	}
    .login-agreement{
        width: 640rpx;
        margin: 0 auto;
    }
}
</style>
