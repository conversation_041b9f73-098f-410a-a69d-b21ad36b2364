<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2022-03-23 15:34:44
 * @LastEditTime: 2022-03-23 17:40:45
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /delta-sign-applet/src/views/customizedRedirect/index.vue
-->
<template>
    <Webview :url="url"></Webview>
</template>

<script>
import Webview from 'components/webview';

export default {
    components: { Webview },
    data() {
        return {
            url: '',
        };
    },
    onLoad(query) {
        if (query.ssqBusinessUrl) {
            let url = decodeURIComponent(query.ssqBusinessUrl);
            url += `${url.includes('?') ? '&' : '?'}access_token=${uni.getStorageSync('accessToken')}&returnUrl=${encodeURIComponent('/utilityPackage/customizedRedirect/index?doJump=true')}`;
            this.url = url;
        } else if (query.doJump) {
            uni.navigateBackMiniProgram({
                extraData: {
                    businessResult: 'success',
                },
            });
        }
    },
};
</script>

<style>

</style>
