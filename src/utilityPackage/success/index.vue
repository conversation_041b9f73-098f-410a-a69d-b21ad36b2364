<template>
    <div class="success">
        <!-- #ifdef MP-WEIXIN -->
        <img :src="officialAccountImg" alt="" @click="preview" class="official-account">
        <h4>关注微信公众号</h4>
        <span>通过微信随时接收以下消息</span>
        <img src="~img/wx_notice.png" alt="" class="wx-notice">
        <!-- #endif -->
        <em @click="toHome">返回小程序首页 >></em>
        <!-- #ifdef MP-WEIXIN -->
        <official-account></official-account>
        <!-- #endif -->
    </div>
</template>

<script>
export default {
    data() {
        return {
            officialAccountImg: require('img/official_accounts.jpg'),
        };
    },
    methods: {
        toHome() {
            uni.switchTab({
                url: 'views/home/<USER>',
            });
        },
        preview() {
            uni.previewImage({
                current: '1',
                urls: [this.officialAccountImg],
            });
        },
    },
};
</script>

<style lang="scss">
.success{
    padding-top: 90rpx;
    text-align: center;
    .official-account{
        display: block;
        width: 344rpx;
        height: 344rpx;
        margin: 0 auto 70rpx;
    }
    h4{
        font-size: 40rpx;
        color: #333333;
        line-height: 50rpx;
        font-weight: 500;
    }
    span{
        display: inline-block;
        font-size: 30rpx;
        color: #999999;
        line-height: 30rpx;
        margin: 24rpx auto 70rpx;
    }
    em{
        display: inline-block;
        margin-top: 150rpx;
        font-size: 30rpx;
        color: $--color-primary;
        line-height: 40rpx;
    }
    .wx-notice{
        display: block;
        width: 510rpx;
        height: 170rpx;
        margin: 0 auto;
    }
}
</style>
