/**
 * 返回标签信息的常量
 * 指定位置页
 * @param  {[String]} type [description]
 * @return {[Object]}
 */
export function markInfo(type) {
    let style = null;
    switch (type) {
        case 'SEAL':
            style =  {
                width: uni.upx2px(105 * 2),
                height: uni.upx2px(96 * 2),
                name: '盖章',
            };
            break;
        case 'SIGNATURE':
            style = {
                width: uni.upx2px(63 * 2),
                height: uni.upx2px(34 * 2),
                name: '签名',
            };
            break;
        case 'DATE':
            style = {
                width: uni.upx2px(63 * 2),
                height: uni.upx2px(16 * 2),
                name: '签署日期',
            };
            break;
        default:
            style = {
                width: 0,
                height: 0,
                name: '',
            };
    }
    return style;
}

// 标签颜色转换， colorInfo[findReceiverIdx(mark.receiverId) % 9] || 'transparent'
export const colorInfo = [
    '#c9e7ff',
    '#ffd65b',
    '#add9e3',
    '#e7aecc',
    '#b2bff6',
    '#94d4ad',
    '#f9c2a1',
    '#f39898',
    '#9dd0c5',
    '#e0e199',
];

export const rgbColorInfo = [
    '201, 231, 255', // '#c9e7ff',
    '255, 214, 91', // '#ffd65b',
    '173, 217, 227', // '#add9e3',
    '231, 174, 204', // '#e7aecc',
    '178, 191, 246', // '#b2bff6',
    '148, 212, 173', // '#94d4ad',
    '249, 194, 161', // '#f9c2a1',
    '243, 152, 152', // '#f39898',
    '157, 208, 197', // '#9dd0c5',
    '224, 225, 153', // '#e0e199'
];

// 由百分比转为数值，与上面的 markCoordinateTransform 相反
export function coordinateReversal(mark, pageWidth, pageHeight) {
    // 标签宽高如果是百分比的转换为整数数值
    const width = mark.width < 1 ? Math.round(mark.width * pageWidth) : mark.width;
    const height = mark.height < 1 ?  Math.round(mark.height * pageHeight) : mark.height;
    // 旧数据坐标值还是数值，不做转换
    const x = mark.x < 1 ? mark.x * pageWidth : mark.x;
    const y = mark.y < 1 ? ((1 - mark.y) * pageHeight - height) : mark.y;
    return { x, y, width, height };
}

// 限定取值范围
export function getValueByScope(value, min, max) {
    if (value < min) {
        value = min;
    } else if (value > max) {
        value = max;
    }
    return value;
}

/**
 * @param  {Object} mark 标签对象
 * @param  {Number} pageWidth 页高
 * @param  {Number} pageHeight 页宽
 * @return {Object}  返回标签左下角以页面左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,标签宽高占页面宽高的百分比width,height
 * @desc  标签坐标转换，换成百分比传给服务端
 */
export function  markCoordinateTransform(mark, pageWidth, pageHeight) {
    const x = (mark.x) / pageWidth;
    const y = 1 - (mark.y + Math.round(mark.height)) / pageHeight;
    const width = Math.round(mark.width) / pageWidth;
    const height = Math.round(mark.height) / pageHeight;
    return { x, y, width, height };
}

// 等比转换图片大小，适应小程序屏幕
export function transformSizeByRadio({ width, height }, clientWidth = 750) {
    return {
        width: uni.upx2px(clientWidth),
        height: uni.upx2px(clientWidth / width * height),
    };
}

// 本地发送创建新标签， 默认参数
export const markDefault = {
    'labelId': '',
    'contractId': '',
    'documentId': '',
    'pageNumber': 0,
    'receiverId': '',
    'type': 'SEAL',
    'docI': '0',
    'pageI': '0',
    'x': 0,
    'y': 0,
    'width': 0,
    'height': 0,
};

// 模板发送创建新标签， 默认参数
export const templateMarkDefault = {
    description: '',
    pageType: '',
    saveLocation: '',
    valueList: [],
    valueStr: null,
    labelId: '', // 新建标签，labelId为空
    labelName: '',
    pageNumber: 0,
    labelExtends: {
        alignment: 'left',
        pxFontSize: 14,
        receiverFill: false,
        refBizFieldId: '0',
        required: true,
    },
    keywordPosition: {
        keyword: '',
        appearOrder: '',
        moveX: 0,
        moveY: 0,
    },
};
