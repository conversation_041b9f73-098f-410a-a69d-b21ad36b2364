// 通过canvas动态生成水印图片
export function createWaterMark(textList, width, height) {
    const canvas = uni.createOffscreenCanvas({ type: '2d', width: 300, height: 150 });
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#999';
    ctx.font = 'normal 10px Arial';
    ctx.translate(0, 100);
    ctx.rotate(-30 * Math.PI / 180);
    textList.forEach((text, index) => {
        ctx.fillText(text, 0, 20 * (index + 1));
    });
    return canvas.toDataURL();
}
export function findReceiver(receiverId, receivers) {
    return receivers.find(item => item.receiverId === receiverId);
}
// 编辑模板中需要根据角色信息决定水印的展示
export function factoryWatermarkText({ userType, userAccount, userName, roleName, enterpriseName }) {
    if (userType === 'PERSON') { // 个人账号
        if (userAccount) {
            return `${userAccount}${userName ? ('(' + userName + ')') : ''}`;
        } // userNameNoDesensitization：水印姓名不脱敏
        return `${roleName}(发送合同后自动替换为真实信息)`;
    }
    if (userType === 'ENTERPRISE') { // 企业账号
        return enterpriseName || userAccount || `${roleName}(发送合同后自动替换为真实信息)`;
    }
}
// 初始化水印
export function initWatermark(watermarkList, receivers) {
    return  watermarkList.map(watermark => {
        let watermarkText = watermark.watermarkText;
        const receiver = findReceiver(watermark.receiverId, receivers);
        if (!watermarkText) { // 编辑模板中，不记录watermarkText，需要前端特殊处理
            watermarkText = factoryWatermarkText(receiver);
        }
        return {
            ...watermark,
            watermarkText,
            showName: receiver.showName,
        };
    });
}

// 初始化骑缝章
export function initRidingSeal(ridingSeals, receivers) {
    return ridingSeals.map(ridingSeal => {
        return {
            ...ridingSeal,
            showName: findReceiver(ridingSeal.receiverId, receivers).showName,
            roleName: findReceiver(ridingSeal.receiverId, receivers).roleName,
        };
    });
}
