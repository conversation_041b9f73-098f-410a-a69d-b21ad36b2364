import Vue from 'vue';
// const Base64 = require('js-base64').Base64;

import store from 'store/index.js';

function convertString(inputString) {
    return inputString.replace(/_([a-z])/g, function(match, letter) {
        return letter.toUpperCase();
    });
}

// 事件上报白名单
const EVENT_WHITELIST = ['from_page_event'];
/**
 *
 * @param event 事件
 * @param config 配置
 */
export const sendPoint = (event, config) => {
    let isAlipay = false;
    // #ifdef MP-ALIPAY
    isAlipay = true;
    // #endif
    if (process.env.NODE_ENV !== 'production' || isAlipay) {
        return;
    }
    const { commonHeaderInfo } = store.state.user;
    const { curEntId } = store.state.send;
    const option = {
        ent_id: curEntId || '',
        user_id: commonHeaderInfo?.platformUser?.userId || '',
        ...config,
    };
    console.log('埋点:', event, option);
    if (EVENT_WHITELIST.includes(event)) {
        uni.reportEvent(event, option);
    }
    const reportData = {
        eventType: event,
    };
    Object.entries(option).forEach(item => {
        reportData[convertString(item[0])] = item[1];
    });
    // Vue.$http.post(`front/log/encode-record`, { content: Base64.encode(JSON.stringify(reportData)) });
};

Vue.prototype.$point = sendPoint;
