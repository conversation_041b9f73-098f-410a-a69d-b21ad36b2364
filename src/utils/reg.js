import regRules from './regs.js';
const {
    entName: entNameReg,
    companyName: companyNameReg,
} = regRules;

/**
 *
 * @desc 校验企业名称格式
 * @export
 * @param {string} [entName='']
 * @param {boolean} [isCheckEmpty=true]
 * @returns errMsg
 */
export function checkEntNameFormat(entName = '', isCheckEmpty = true/* 是否 校验不能为空的提示*/) {
    entName = entName.trim();
    if (!entName && isCheckEmpty) {
        return '企业名称不能为空';
    }
    if (!entNameReg.every(i => !i.test(entName))) {
        return '请输入正确的公司名称'; // 请填写正确的企业名称
    }
    if (entName && !companyNameReg.test(entName)) {
        return '企业名称不能超过60个字符';
    }
    return ''; // 通过格式校验
}
