import regRules from 'utils/regs.js';

export const PERSON_SAMPLES = ['借条', '房屋租赁合同'];
export const ENTERPRISE_SAMPLES = ['离职证明', '在职证明', '劳动合同'];

export const SAMPLE_HISTORY_KEY = 'SAMPLE_KNEW_DETAIL_HISTORY';

// 需要进行进一步校验的label，labelName 枚举
export function getValidRule(labelName, labels = []) {
    const rule = {};
    switch (labelName) {
        case '姓名':
        case '员工姓名':
        case '借款人姓名':
        case '出借人姓名':
        case '出租方姓名':
        case '承租方姓名':
            rule.validFun = (val) => regRules.inputUserNameReg.test(val);
            rule.errorMessage = `${labelName}格式不正确`;
            break;
        case '企业名称':
            rule.validFun = (val) => (regRules.entName.every(i => !i.test(val)) && regRules.companyName.test(val) && !regRules.hasEmptySpaceBetween.test(val));
            rule.errorMessage = `企业名称格式不正确`;
            break;
        case '身份证号':
        case '员工身份证号':
        case '借款人身份证号':
        case '出借人身份证号':
        case '出租方身份证号':
        case '承租方身份证号':
            rule.validFun = (val) => regRules.IDCardReg.test(val);
            rule.errorMessage = `请输入正确的身份证号`;
            break;
        case '借款金额':
        case '工作年限':
        case '续租提前':
        case '月租金':
        case '每期金额':
        case '首期付款额':
        case '试用期工资金额':
        case '月工资金额':
            rule.validFun = (val) => Number(val) === 0 || regRules.numberStrReg.test(val);
            rule.errorMessage = `请输入数字`;
            break;
        case '借款年利率':
        case '逾期利率':
        case '逾期年利率':
            rule.validFun = (val) => Number(val) === 0 || regRules.numberStrReg.test(val) && val >= 0 && val <= 12;
            rule.errorMessage = `请输入0-12范围内的利率值`;
            break;
        case '发薪日':
            rule.validFun = (val) => Number.isInteger(Number(val)) && val >= 1 && val <= 31;
            rule.errorMessage = `请输入1-31范围内的月内发薪日期`;
            break;
        case '手机号':
        case '员工联系电话':
        case '出租方联系电话':
        case '承租方联系电话':
        case '经办人手机号':
            rule.validFun = (val) => regRules.userPhone.test(val);
            rule.errorMessage = `请输入正确的手机号`;
            break;
        case '合同开始日期':
        case '合同结束日期':
            rule.validFun = () => checkDateSection(labels, '合同开始日期', '合同结束日期');
            rule.errorMessage = dateSectionError(labelName);
            break;
        case '试用开始日期':
        case '试用结束日期':
            rule.validFun = () => checkDateSection(labels, '试用开始日期', '试用结束日期');
            rule.errorMessage = dateSectionError(labelName);
            break;
        case '租赁开始日期':
        case '租赁结束日期':
            rule.validFun = () => checkDateSection(labels, '租赁开始日期', '租赁结束日期');
            rule.errorMessage = dateSectionError(labelName);
            break;
        case '入职日期':
        case '离职日期':
            rule.validFun = () => checkDateSection(labels, '入职日期', '离职日期');
            rule.errorMessage = dateSectionError(labelName);
            break;
        case '出借日期':
        case '全部还款日期':
            rule.validFun = () => checkDateSection(labels, '出借日期', '全部还款日期', false);
            rule.errorMessage = dateSectionError(labelName);
            break;
    }
    return rule;
}

/**
 * 删除合同标签
 * @param {...label} Object { labels, name, value, required}标签数据
 * @return validResult Object { isFailed, errorMessage  }
 */
export function getSampleLabelValidateResult({ labels, name, required, value }) {
    if (required && (value === null || value === '')) { // 必填未填写
        return {
            isFailed: true,
            errorMessage: '必填项未填写',
        };
    }
    if (value && getValidRule(name, labels).validFun) { // 需要校验格式项
        const { validFun, errorMessage } = getValidRule(name, labels);
        if (!validFun(value)) {
            return {
                isFailed: true,
                errorMessage,
            };
        }
    }
    return {
        isFailed: false,
        errorMessage: '',
    };
}

/**
 * 校验日期区间结束日期大于开始日期
 */
function checkDateSection(labels, startDate, endDate, equal = true) {
    const startValue = labels.find((label) => label.labelName === startDate)?.value;
    const endValue = labels.find((label) => label.labelName === endDate)?.value;
    if (equal && startValue && endValue && new Date(endValue) <= new Date(startValue) ||
        !equal && startValue && endValue && new Date(endValue) < new Date(startValue)
    ) { // end > start || end >= start
        return false;
    }
    return true;
}
/**
 * 时间区间errorMessage
 */
function dateSectionError(labelName) {
    if (labelName.indexOf('职') !== -1) {
        return labelName.indexOf('入职') === -1 ? '离职日期需要大于入职日期' : '入职日期需要小于离职日期';
    } else if (labelName === '出借日期' || labelName === '全部还款日期') {
        return labelName === '出借日期' ? '出借日期需要小于等于全部还款日期' : '全部还款日期需要大于等于出借日期';
    }
    return labelName.indexOf('开始') === -1 ? '结束日期需要大于开始日期' : '开始日期需要小于结束日期';
}
