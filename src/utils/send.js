/* 获取签约主体的展示名, 返回 '' 的情况为新建条目，此时条目无任何值 */
export function getUserNameShow(item = {}, roleName = '', userType = '') {
    if (userType === 'ENTERPRISE') {
        if (item.enterpriseName) {
            return item.enterpriseName;
        } else if (!!item.enterprises && item.enterprises.length > 0 && !!item.enterprises[0].entName) {
            return item.enterprises[0].entName;
        } else if (!!item.userAccount && !item.isBlank) {
            return `${item.userAccount}(企业账号)`;
        } else {
            return roleName;
        }
    } else {
        if (item.userName) {
            return item.userName;
        }
        if (!!item.userAccount  && !item.isBlank) {
            return `${item.userAccount}(个人账号)`;
        } else {
            return roleName;
        }
    }
}

/**
 * @desc 获取两个整数之间的随机整数
 * */
export function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min)) + min; // 不含最大值，含最小值
}
