// import sensors from 'sa-sdk-miniprogram/dist/wechat/sensorsdata.esm.js';
import Http from 'src/http/index.js';
import Vue from 'vue';
import Store from 'src/store/index.js';
const sensors = {
    setPara: () => {},
    registerApp: () => {},
    init: () => {},
    track: () => {},
    setProfile: () => {},
    login: () => {},
    identify: () => {},
    getAnonymousID: () => {},
};
const ssqSensors = {
    setPara({ openAutoTrack = true } = {}) {
        sensors.setPara({
            name: 'sensors',
            server_url: `https://sensors.bestsign.cn/sa?project=${Http.baseURL.includes('bestsign.cn') ? 'production' : 'default'}`,
            show_log: false,
            autoTrack: {
                appLaunch: openAutoTrack, // 默认为 true，false 则关闭 $MPLaunch 事件采集
                appShow: openAutoTrack, // 默认为 true，false 则关闭 $MPShow 事件采集
                appHide: openAutoTrack, // 默认为 true，false 则关闭 $MPHide 事件采集
                pageShow: openAutoTrack, // 默认为 true，false 则关闭 $MPViewScreen 事件采集
                pageShare: openAutoTrack, // 默认为 true，false 则关闭 $MPShare 事件采集
                mpClick: openAutoTrack, // 默认为 false，true 则开启 $MPClick 事件采集
                mpFavorite: openAutoTrack, // 默认为 true，false 则关闭 $MPAddFavorites 事件采集
                pageLeave: openAutoTrack, // 默认为 false， true 则开启 $MPPageLeave事件采集
            },
        });
    },
    // 注册公共属性
    registerCommonProperty() {
        sensors.registerApp({
            pub_platform_type: '上上签签署小程序',
            pub_platform_version: Store.state?.isWeWork ? '企业微信' : (uni.getSystemInfoSync().appName === 'APICloud' ? '支付宝' : '微信'), // 微信、企业微信、支付宝
            pub_source_platform: () => {
                return ''; // 后期统一处理
            },
            pub_is_login: () => {
                return !!uni.getStorageSync('accessToken');
            },
            pub_account_type: () => {
                return JSON.parse(uni.getStorageSync('sensorsUserInfo') || null)?.currentAccountType || '';
            },
            pub_company_name: () => {
                return JSON.parse(uni.getStorageSync('sensorsUserInfo') || null)?.currentEntName || '';
            },
            pub_company_id: () => {
                return JSON.parse(uni.getStorageSync('sensorsUserInfo') || null)?.currentEntId || '';
            },
            pub_role_name: () => {
                return JSON.parse(uni.getStorageSync('sensorsUserInfo') || null)?.currentRoleName || [];
            },
            pub_language: () => {
                return '中文';
            },
        });
    },
    // 初始化
    init() {
        sensors.init();
    },
    track({ eventName = '', eventProperty = {} } = {}) {
        sensors.track(eventName, { ...eventProperty });
    },
    // 设置用户属性
    setUserProfile(options = {}) {
        sensors.setProfile({ ...options });
    },
    // 用户登录
    sensorsLogin(id = '') {
        sensors.login(id);
    },
    // 修改匿名 ID
    setIdentifyId(id = '', ifSaveCookie = true) {
        sensors.identify(id, ifSaveCookie); // 替换神策默认分配的匿名 ID, 并把这个id保存在cookie中
    },
    // 获取匿名 ID
    getAnonymousID() {
        return sensors.getAnonymousID();
    },
};

ssqSensors.setPara();
ssqSensors.registerCommonProperty();
ssqSensors.init();

Vue.prototype.$sensors = ssqSensors;
Vue.$sensors = ssqSensors;

export default ssqSensors;
