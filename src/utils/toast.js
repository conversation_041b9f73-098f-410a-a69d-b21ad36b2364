export const toast = (options, type) => {
    if (type === 'loading') {
        uni.showLoading(options);
    } else if (type === 'hideLoading') {
        uni.hideLoading();
    } else {
        options.icon = (type === 'err') ? 'none' : type;
        uni.showToast(options);
    }
};

['err', 'success', 'loading', 'hideLoading'].forEach(type => {
    toast[type] = options => {
        if (typeof options === 'string') {
            options = {
                title: options,
                duration: 2000,
            };
        }

        return toast(options, type);
    };
});
