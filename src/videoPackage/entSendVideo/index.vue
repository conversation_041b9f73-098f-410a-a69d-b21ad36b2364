<template>
    <view class="video-container">
        <video class="video-wrapper half-screen"
            src="https://static.bestsign.cn:443/220a400f4f6bf7f7a48a1c1601a782d48138e7ca.mp4"
            :controls="true"
            :direction="90"
            :show-fullscreen-btn="true"
            object-fit="fill"
        ></video>
    </view>
</template>

<script>
export default {
    name: 'EntSendVideo',
};
</script>

<style lang="scss">
.video-container {
    width: 100vw;
    height: 100vh;
    background: $--color-black;
    .video-wrapper {
        width: 100%;
        height: 100%;
        background: $--color-black;
        box-sizing: content-box;
        &.half-screen {
            // #ifdef MP-WEIXIN
            margin-top: 100px;
            // #endif
            height: 200px;
        }
    }
    .play-btn {
        background: red;
    }
}
</style>
