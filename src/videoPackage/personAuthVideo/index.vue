<template>
    <view class="video-container">
        <video class="video-wrapper"
            src="https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/cddfc045-8e5e-41e0-ba2d-06112ace91bd.mp4"
            :controls="true"
            :direction="90"
            :show-fullscreen-btn="false"
            object-fit="fill"
        ></video>
    </view>
</template>

<script>
export default {
    name: 'PersonAuthVideo',
};
</script>

<style lang="scss">
.video-container {
    width: 100vw;
    height: 100vh;
    background: $--color-black;
    .video-wrapper {
        width: 100%;
        height: 100%;
        background: $--color-black;
        box-sizing: content-box;
        &.half-screen {
            // #ifdef MP-WEIXIN
            margin-top: 100px;
            // #endif
            height: 200px;
        }
    }
    .play-btn {
        background: red;
    }
}
</style>
