<template>
    <xd-page class="account-page" :bottom="0" :noCalIphoneXBottom="true">
        <div class="account">
            <div class="account-header">
                <img :src="avatarSrc" alt="" class="avatar">
                <div class="info">
                    <div class="name">{{ hasLogin ? accountName : '姓名' }}</div>
                    <div class="acccount-value">
                        <div>{{ hasLogin ? account : '未登录' }}</div>
                        <image
                            v-if="hasLogin"
                            class="auth-img"
                            @click="handleAuthImgClick"
                            :src="accountAuthImg"
                        ></image>
                    </div>
                </div>
                <template v-if="hasLogin">
                    <view v-if="showAddEnt" @click="handleOpenCreateEntDialog" class="create-ent-btn">创建企业</view>
                    <SwitchEnt :fromAccountPage="true" v-else class="switch-item"></SwitchEnt>
                </template>
            </div>
            <div class="account-content" v-if="hasLogin">
                <div class="account-info">
                    <span class="account-info-label">通知手机</span>
                    <span class="account-info-value">{{ noticePhone }}</span>
                </div>
                <div class="account-info">
                    <span class="account-info-label">签约密码管理</span>
                    <view class="account-info-value account-info-arrow" @click="toConfigSignPwd">
                        <text class="icon-ic_forward"></text>
                    </view>
                </div>
                <template v-if="showChargeEntry">
                    <div class="account-info">
                        <span class="account-info-label">合同余额</span>
                        <span class="account-info-value">{{ '对私' + useable2cSum + '份' }}&nbsp;|&nbsp;{{ '对公' + useable2bSum + '份' }}</span>
                    </div>
                    <div class="account-info" v-if="!isWeWork">
                        <span class="account-info-label">
                            购买
                            <span class="time-offer">限时优惠</span>
                        </span>
                        <view class="account-info-value account-info-btn" @click="toCharge">立即购买</view>
                    </div>
                    <div class="account-info" v-if="!isWeWork">
                        <span class="account-info-label">已购订单管理</span>
                        <view class="account-info-value account-info-arrow" @click="toChargeOrder">
                            <text class="icon-ic_forward"></text>
                        </view>
                    </div>
                </template>
            </div>
            <button class="account-logout" :class="hasLogin ? 'login-btn' : '' " :type="hasLogin ? 'plain':'primary'" @click="handleLogoutOrLogin">{{ hasLogin ? '退出登录': loginText }}</button>

            <uni-popup ref="createEntPopup" type="dialog" class="create-ent-dialog">
                <uni-popup-dialog ref="createEntDialog"
                    mode="input"
                    class="ent-name-input"
                    title="创建企业"
                    :before-close="true"
                    :placeholder="newEntNamePlaceholder"
                    @confirm="createEntConfirm"
                    @close="handleCloseCreateEnt(true)"
                ></uni-popup-dialog>
            </uni-popup>
            <ActivityLogo />
            <!-- #ifdef MP-WEIXIN -->
            <BottomTab :current-index="4"></BottomTab>
            <!-- #endif -->
        </div>
    </xd-page>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import _get from 'lodash/get';
import { getAccountInfo, logout } from 'api/account.js';
import { getWallet } from 'api/charge.js';
import SwitchEnt from 'components/selectSender';
import xdPage from 'components/xdPage/index.vue';
import account_auth from '@/assets/images/account_auth.png';
import account_no_auth from '@/assets/images/account_no_auth.png';
import avatar_default from '@/assets/images/avatar_default.png';
import BottomTab from 'components/bottomTab';
import ActivityLogo from 'components/activityLogo';
export default {
    components: {
        SwitchEnt,
        xdPage,
        BottomTab,
        ActivityLogo,
    },
    data() {
        return {
            account: '',
            userName: '',
            avatarFileId: '',
            noticePhone: '',
            useable2bSum: 0,
            useable2cSum: 0,
            showChargeEntry: true,
            hasLogin: false,
            newEntNamePlaceholder: ' ',
            authImg: account_auth,
            noAuthImg: account_no_auth,
            avatarImg: avatar_default,
            enterTime: 0,
            shareThumbnail: require('img/shareImg.png'),
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo', 'entList']),
        ...mapState('send', ['curEntIndex', 'sendType']),
        ...mapState(['isWeWork', 'loginText']),
        avatarSrc() {
            if (!this.avatarFileId) {
                return this.avatarImg;
            }
            return `${this.$http.baseURL}/users/0/portrait/${this.avatarFileId}?access_token=${uni.getStorageSync('accessToken')}&t=${+new Date()}`;
        },
        currentEnt() {
            return this.entList[this.curEntIndex] || {};
        },
        authStatus() {
            return this.currentEnt.authStatus || 0;
        },
        authStatusText() {
            return this.authStatus > 1 ? '已认证' : '未认证';
        },
        isPerson() {
            return this.currentEnt.entId === '0';
        },
        accountAuthImg() {
            return this.authStatus > 1 ? this.authImg : this.noAuthImg;
        },
        accountName() {
            return this.isPerson ? `个人账号-${this.userName}` : this.currentEnt.showName;
        },
        showAddEnt() {
            return this.entList.length === 1 && this.entList[0].entId === '0';
        },
    },
    watch: {
        curEntIndex: {
            handler() {
                this.initChargeInfo();
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions(['loginPromise', 'oauthLogin']),
        ...mapActions('user', ['getFeatures', 'getHeadInfo', 'handleEntList']),
        createEntConfirm(val) {
            const entName = val.replace(/\s+/g, '');
            if (entName.length === 0) {
                this.$toast.error('请输入正确的企业名称');
                return;
            }

            this.$sensors.track({
                eventName: 'Mp_PersonalCenterWindow_BtnClick',
                eventProperty: {
                    window_name: '创建企业弹窗',
                    company_name: entName,
                    icon_name: '确定',
                },
            });
            this.$toast.loading({ mask: true });
            this.$http.post('/authenticated/enterprise-register', {
                entName,
            }).then(async({ data }) => {
                uni.setStorageSync('accessToken', data.access_token);
                uni.setStorageSync('refreshToken', data.refresh_token);
                this.$toast.hideLoading();
                await this.getFeatures();
                await this.getHeadInfo(); // roleDetails 权限 和主体有关系，此处需重新请求head-info
                this.handleGoAuthTip(data.access_token, data.refresh_token);
            });
        },
        handleGoAuthTip(accessToken, refreshToken) {
            this.handleCloseCreateEnt();
            uni.showModal({
                title: '实名认证后发起签约更有保障',
                content: '该企业还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证',
                cancelText: '取消',
                confirmText: '去认证',
                confirmColor: '#127fd2',
                showCancel: true,
                success: (res) => {
                    if (res.confirm) {
                        const authUrl =
                            encodeURIComponent(`${this.$http.baseURL}/auth-p/enterprise?isBestSignApplet=true&access_token=${accessToken}&refresh_token=${refreshToken}&returnUrl=/views/account/index`);

                        uni.navigateTo({
                            url: `/views/webviewRedirect/index?url=${authUrl}`,
                        });
                    }
                },
            });
        },
        handleCloseCreateEnt(isBtnClick) {
            if (isBtnClick) {
                this.$sensors.track({
                    eventName: 'Mp_PersonalCenterWindow_BtnClick',
                    eventProperty: {
                        window_name: '创建企业弹窗',
                        icon_name: '关闭',
                    },
                });
            }
            this.newEntNamePlaceholder = ''; // 解决打开弹窗时，输入框placeholder闪烁问题
            this.$refs.createEntPopup.close();
        },
        handleOpenCreateEntDialog() {
            this.$sensors.track({
                eventName: 'Mp_PersonalCenter_BtnClick',
                eventProperty: {
                    icon_name: '创建企业',
                },
            });
            this.$sensors.track({
                eventName: 'Mp_PersonalCenterWindow_PopUp',
                eventProperty: {
                    window_name: '创建企业弹窗',
                },
            });
            this.$refs.createEntPopup.open();
            setTimeout(() => {
                this.newEntNamePlaceholder = '请输入企业名称';  // 解决打开弹窗时，输入框placeholder闪烁问题
            }, 500);
        },
        initAccountInfo() {
            getAccountInfo().then(({ data }) => {
                this.account = _get(data, 'platformUser.account', '无');
                this.userName = _get(data, 'platformUser.fullName', '无');
                this.avatarFileId = _get(data, 'platformUser.photoFileId', '');
                const noticeItem = (data.notifications || []).filter(el => el.type === 2);
                this.noticePhone = noticeItem.length ? noticeItem[0].code : '无';
            });
        },
        initChargeInfo() {
            getWallet({ isNewGroup: this.commonHeaderInfo.hasGroupConsole })
                .then(({ data }) => {
                    this.useable2bSum = _get(data, 'useable2bSum', 0);
                    this.useable2cSum = _get(data, 'useable2cSum', 0);
                    this.showChargeEntry = true;
                })
                .catch(() => {
                    this.showChargeEntry = false;
                });
        },
        toCharge() {
            this.$sensors.track({
                eventName: 'Mp_PersonalCenter_BtnClick',
                eventProperty: {
                    icon_name: '立即充值',
                },
            });
            this.$point('click_top_up', {
                biz_id: '',
            });
            if (this.entList[this.curEntIndex].authStatus !== 2) {
                return  this.$toast.error(`请完成${this.isPerson ? '个人' : '企业'}实名认证后再购买。`);
            }
            uni.navigateTo({
                url: `/views/charge/index`,
            });
        },
        toChargeOrder() {
            uni.navigateTo({
                url: '/views/chargeOrder/index',
            });
        },
        toConfigSignPwd() {
            this.$sensors.track({
                eventName: 'Mp_PersonalCenter_BtnClick',
                eventProperty: {
                    icon_name: '签约密码管理',
                },
            });
            uni.navigateTo({
                url: `/views/signPassword/index`,
            });
        },
        handleAuthImgClick() {
            this.$sensors.track({
                eventName: 'Mp_PersonalCenter_BtnClick',
                eventProperty: {
                    icon_name: this.authStatusText,
                },
            });
            let authUrl;
            const returnUrl = '/views/account/index';
            // 未实名
            if (this.entList[this.curEntIndex].authStatus !== 2) {
                if (this.isPerson) {
                    authUrl = encodeURIComponent(`${this.$http.baseURL}/mp/auth-m/individual/auth?isBestSignApplet=true&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&returnUrl=${returnUrl}`);
                } else {
                    authUrl = encodeURIComponent(`${this.$http.baseURL}/auth-p/enterprise?isBestSignApplet=true&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}&returnUrl=${returnUrl}`);
                }
                uni.navigateTo({
                    url: `/views/webviewRedirect/index?url=${authUrl}`,
                });
            } else {
                // 已实名
                uni.navigateTo({
                    url: `/views/account/authCert`,
                });
            }
        },
        handleContact() {
            uni.openCustomerServiceChat({
                extInfo: { url: 'https://work.weixin.qq.com/kfid/kfc99a8ee972094f3df' },
                corpId: 'ww7a945850e47ad591',
                success(res) {
                    console.log(res);
                },
            });
        },
        handleLogoutOrLogin() {
            this.$sensors.track({
                eventName: 'Mp_PersonalCenter_BtnClick',
                eventProperty: {
                    icon_name: this.hasLogin ? '退出登录' : this.loginText,
                },
            });
            if (this.hasLogin) {
                logout().then(() => {
                    uni.setStorageSync('accessToken', '');
                    uni.setStorageSync('refreshToken', '');
                    uni.setStorageSync('thirdCorpId', '');
                    uni.setStorageSync('sensorsUserInfo', null);
                    uni.reLaunch({
                        url: '/views/basic/index',
                    });
                });
            } else {
                // #ifdef MP-WEIXIN
                uni.reLaunch({
                    url: '/subViews/login/index',
                });
                // #endif
                // #ifdef MP-ALIPAY
                this.oauthLogin();
                // #endif
            }
        },
    },
    async onShow() {
        this.$sensors.track({
            eventName: 'Mp_Common_BtnClick',
            eventProperty: {
                icon_name: '账号管理',
            },
        });
        this.enterTime = new Date().getTime();
        this.$sensors.track({
            eventName: 'Mp_PersonalCenter_PageView',
        });
        await this.loginPromise();
        this.hasLogin = Boolean(uni.getStorageSync('accessToken'));
        this.initChargeInfo();
        this.initAccountInfo();
        await this.getHeadInfo();
        await this.getFeatures();
        this.handleEntList();
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_PersonalCenter_PageLeave',
            eventProperty: {
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    onShareAppMessage() {
        return {
            imageUrl: this.shareThumbnail,
        };
    },
};
</script>

<style lang="scss">
.account-page {
    // #ifdef MP-WEIXIN
    height: calc(100vh - 70px);
    // #endif
}
.account{
    height: 100%;
    padding: 0;
    margin: 0;
    background-color: $--background-color-regular;
    &-header{
        display: flex;
        align-items: center;
        padding: 60rpx 40rpx;
        background-color: $--color-white;
        .avatar{
            display: block;
            width: 85rpx;
            height: 85rpx;
            border: 1px solid $--color-info-lighter;
            border-radius: 8rpx;
            box-sizing: border-box;
        }
        .info{
            flex-grow: 1;
            width: calc(100% - 270rpx);
            margin: 0 20rpx 0 30rpx;
            .name{
                font-size: 30rpx;
                font-weight: 600;
                color: $--color-text-primary;
                white-space: nowrap; /* 禁止文本换行 */
                overflow: hidden; /* 隐藏超出容器的内容 */
                text-overflow: ellipsis; /* 当文本超出容器时显示省略号 */
            }
            .acccount-value{
                display: flex;
                align-items: center;
                font-size: 28rpx;
                line-height: 50rpx;
                font-weight: 400;
                color: $--color-text-secondary;
                .auth-img{
                    width: 96rpx;
                    height: 32rpx;
                    padding-left: 12rpx;
                }
            }
        }
        .create-ent-btn {
            width: 120rpx;
            height: 40rpx;
            font-size: 22rpx;
            background-color: $--color-primary;
            border-radius: 20rpx;
            text-align: center;
            color: $--color-white;
            padding-top: 2rpx;
            line-height: 36rpx;
        }
    }
    &-content {
        padding: 0 20rpx;
        height: calc(100% - 170px);
        overflow: scroll;
    }

    &-info{
        height: 100rpx;
        line-height: 100rpx;
        background-color: $--color-white;
        border-radius: 10rpx;
        margin: 20rpx 0;
        padding: 0 30rpx;
        font-size: 28rpx;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        &-label{
            color: $--color-text-primary;
            .time-offer {
                height: 20px;
                line-height: 20px;
                margin-left: 10px;
                padding: 1px 8px;
                font-size: 12px;
                color: $--color-white;
                vertical-align: middle;
                background: linear-gradient(270deg, #FF9E3E 0%, #FFBA75 100%);;
                border-radius: 14px;
            }
        }
        &-value{
            text-align: right;
            color: $--color-text-secondary;
            .contact-service__button{
                width: unset;
                height: unset;
                vertical-align: middle;
            }
        }
        &-btn {
            color: $--color-primary;
        }
        &-arrow {
            width: 40px;
        }
    }
    &-logout{
        position: absolute;
        width: calc(100% - 80rpx);
        height: 80rpx;
        line-height: 80rpx;
        bottom: 0rpx;
        // #ifdef MP-WEIXIN
        bottom: 90px;
        // #endif
        left: 50%;
        transform: translateX(-50%);
        font-size: 30rpx;
    }
    .login-btn{
        color: $--color-primary;
        background-color: $--color-white;
        border: 1px solid $--color-primary;
    }
    .ent-name-input {
        line-height: 40px;
    }
}
.create-ent-dialog {
    .ent-name-input {
        .uni-dialog-title {
            padding-top: 15px;
            &-text {
                color: $--color-text-primary;
            }
        }
        .uni-dialog-content {
            padding: 12px 20px;
        }
        .uni-button-color {
            color: #576B95;
        }
    }
}
</style>
