<template>
    <view class="aggregate-page">
        <ul class="aggregate-page__list">
            <!-- #ifdef MP-ALIPAY -->
            <li class="empty">敬请期待</li>
            <!-- #endif -->
            <li class="aggregate-page__list-navigate" v-for="(navigate, i) in navigateList" :key="i">
                <div :class="activeNavigate(navigate) ? 'active' : ''" class="icon-box" @click="toMiniProgram(navigate)">
                    <img :src="navigate.icon" alt="">
                    <span v-if="activeNavigate(navigate)">正在使用</span>
                </div>
                <p class="explain">{{ navigate.explain }}</p>
            </li>
        </ul>

        <ActivityLogo />
        <!-- #ifdef MP-WEIXIN -->
        <BottomTab :current-index="3"></BottomTab>
        <!-- #endif -->
    </view>
</template>

<script>
import { envVersion, isProduction } from '@/envVersion.js';
import BottomTab from 'components/bottomTab';
import ActivityLogo from 'components/activityLogo';
export default {
    components: {
        BottomTab,
        ActivityLogo,
    },
    data() {
        return {
            navigateList: [
                {
                    icon: require('./img/sign.png'),
                    path: 'views/home/<USER>',
                    explain: '上上签签署小程序主要用于合同签署、发送和合同购买等业务。',
                    type: 'sign',
                    appId: isProduction ? 'wx54f87909fcf8e390' : 'wx211c5f8feae8d939',
                },
                {
                    icon: require('./img/contract.png'),
                    path: 'pages/Home/Home',
                    explain: '查验码小程序主要用于合同查询、分享和下载。',
                    type: 'contract',
                    appId: isProduction ? 'wx553933abaef192da' : 'wxc4daaf1963462a4e',
                },
                {
                    icon: require('./img/seal.png'),
                    path: 'views/sealManage/index',
                    explain: '印章管理小程序主要用于企业印章的添加、修改、查询以及权限分配。',
                    type: 'seal',
                    appId: isProduction ? 'wx46389af1e5d8f701' : 'wx042543eebf7052fe',
                },
                {
                    icon: require('./img/archive.png'),
                    path: 'pages/userCenter/index',
                    explain: '档案＋小程序主要用于填写甲方要求的采集信息。',
                    type: 'archive',
                    appId: isProduction ? 'wxf8f794f9221c6c3d' : 'wxef48db00f9db8779',
                },
            ],
            currentType: 'sign',
            shareThumbnail: require('img/shareImg.png'),
        };
    },
    methods: {
        activeNavigate(navigate) {
            return navigate.type === this.currentType;
        },
        toMiniProgram(navigate) {
            const iconNameMap = {
                'sign': '上上签签署小程序',
                'contract': '查验码小程序',
                'seal': '印章管理小程序',
                'archive': '档案+小程序',
            };
            this.$sensors.track({
                eventName: 'Mp_AllApp_BtnClick',
                eventProperty: {
                    icon_name: iconNameMap[navigate.type],
                },
            });
            if (this.activeNavigate(navigate)) {
                return;
            }
            console.log('isProduction', isProduction);
            console.log('envVersion', envVersion);
            uni.navigateToMiniProgram({
                appId: navigate.appId,
                path: `${navigate.path}?chosenEntId=${uni.getStorageSync('chosenEntId') || ''}`,
                envVersion: envVersion,
            });
        },
    },
    created() {
        // #ifdef MP-ALIPAY
        this.navigateList = [];
        // #endif
    },
    onShow() {
        this.$sensors.track({
            eventName: 'Mp_Common_BtnClick',
            eventProperty: {
                icon_name: '全部应用',
            },
        });
    },
    onShareAppMessage() {
        return {
            imageUrl: this.shareThumbnail,
        };
    },
};
</script>

<style lang="scss">
.aggregate-page{
    height: 100vh;
    // #ifdef MP-WEIXIN
    height: calc(100vh - 70px);
    // #endif
    font-size: 24rpx;
    box-sizing: border-box;
    &__list {
        height: 100vh;
        padding: 60rpx 40rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        box-sizing: border-box;
        .empty{
            width: 100%;
            text-align: center;
            margin-top: 100rpx;
        }
        &-navigate{
            width: 310rpx;
            .icon-box{
                height: 360rpx;
                background: #e7f3fb;
                border-radius: 16rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                margin-bottom: 30rpx;
                overflow: hidden;
                &.active{
                    background: #f6f6f6;
                }
                img{
                    width: 160rpx;
                    height: 160rpx;
                    border-radius: 50%;
                }
                span{
                    display: block;
                    color: #fff;
                    background: #41a4ff;
                    border-radius: 0 0 0 10rpx;
                    padding: 0 20rpx;
                    line-height: 48rpx;
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
        }
    }
}
</style>
