<template>
    <div class="alipay-login" :class="isDifferent ? 'diff' : ''">
        <img class="alipay-login__logo" src="~img/logo.png" alt="">
        <template v-if="hasAuthorized">
            <div class="alipay-login__content">
                <template v-if="isDifferent">
                    <div class="alipay-login__diff">
                        <p class="diff-tip">检测到您在“上上签”已有实名账号如下，与您支付宝实名信息不一致，你可以选择用支付宝信息覆盖掉您在上上签平台的实名信息，或者退出登录修改支付宝实名信息后重新授权登录。</p>
                        <div class="alipay-login__diff-label">上上签账号信息：</div>
                        <div class="alipay-login__auth">
                            <div><b>姓名</b>：{{ userAuth.realName }}</div>
                            <div><b>手机号</b>：{{ userAuth.mobile }}</div>
                            <div><b>证件号码</b>：{{ userAuth.idNumber }}</div>
                        </div>
                        <div class="alipay-login__diff-label">支付宝实名信息：</div>
                    </div>
                </template>
                <p class="ssq-tip" v-else>您将使用您以下信息完成在上上签平台的账号注册登录与实名，以方便您后续签署合同：</p>
                <div class="alipay-login__auth">
                    <div><b>姓名</b>：{{ supplierAuthInfo.realName }}</div>
                    <div><b>手机号</b>：{{ supplierAuthInfo.mobile }}</div>
                    <div><b>证件号码</b>：{{ supplierAuthInfo.idNumber }}</div>
                </div>
            </div>
            <div class="alipay-login__btn" :class="isDifferent ? 'diff' : ''">
                <template v-if="isDifferent">
                    <button @click="goBack">退出</button>
                    <button type="primary" @click="handleAuth(false)">覆盖并登录</button>
                </template>
                <button v-else type="primary" @click="handleAuth(false)">实名登录</button>
            </div>
            <LoginAgreement v-model="agreeAuthorized"></LoginAgreement>
        </template>
    </div>
</template>

<script>
import { getAlipayUserInfo, ssqLogin } from 'api/account.js';
import LoginAgreement from 'components/loginAgreement';
import { targetMixin } from 'mixins/target';
export default {
    components: {
        LoginAgreement,
    },
    mixins: [targetMixin],
    data() {
        return {
            isDifferent: false,
            hasAuthorized: false,
            supplierAuthInfo: {},
            userAuth: {},
            agreeAuthorized: false,
            query: null,
        };
    },
    methods: {
        goBack() {
            uni.reLaunch({
                url: '/views/home/<USER>',
            });
        },
        async handleAuth(withoutAgree = false) {
            if (!this.agreeAuthorized && !withoutAgree) {
                return uni.showToast({
                    icon: 'none',
                    title: '请先阅读并同意相关协议',
                });
            }
            uni.showLoading();
            uni.login({
                scopes: 'auth_user',
                success: (res) => {
                    ssqLogin({
                        account: this.supplierAuthInfo.mobile,
                        verifyKey: 'alipay',
                        verifyCode: 666666,
                        sourceType: this.$store.getters.mpSourceType,
                        code: res.code,
                    }).then(({ data }) => {
                        const { access_token, refresh_token } = data;
                        uni.setStorageSync('accessToken', access_token);
                        uni.setStorageSync('refreshToken', refresh_token);
                        if (this.query?.MPTargetPage) {
                            this.goToTarget();
                        } else {
                            const loginSuccessPage = uni.getStorageSync('loginSuccessPage');
                            uni.reLaunch({
                                url: loginSuccessPage ? decodeURIComponent(loginSuccessPage) : '/views/home/<USER>',
                                success: () => {
                                    loginSuccessPage && uni.removeStorage('loginSuccessPage');
                                },
                            });
                            uni.hideLoading();
                        }
                    });
                },
                fail(err) {
                    console.log(err);
                },
            });
        },
        init() {
            uni.login({
                scopes: 'auth_user',
                success: (res) => {
                    console.log(res);
                    if (res.code) {
                        uni.showLoading();
                        getAlipayUserInfo(res.code).then(({ data }) => {
                            this.supplierAuthInfo = data.supplierAuthInfo || {};
                            if (data.isConsistent) {
                                return this.handleAuth(true);
                            }
                            this.userAuth = data.userAuth || {};
                            this.isDifferent = data.isDifferent || false;
                            this.hasAuthorized = true;
                            uni.hideLoading();
                        });
                    }
                },
                fail: () => {
                    uni.showToast({
                        title: '授权失败',
                        icon: 'none',
                        complete: () => {
                            this.goBack();
                        },
                    });
                },
            });
        },
    },
    onLoad(query) {
        this.query = query;
        this.init();
    },
};
</script>

<style lang="scss">
.alipay-login{
    height: 100vh;
    padding: 140rpx 40rpx 0;
    font-size: 28rpx;
    font-weight: 400;
    &.diff{
        padding-top: 60rpx;
    }
    &__logo{
        display: block;
        width: 280rpx;
        height: 120rpx;
        margin: 0 auto;
    }
    &__diff{
        margin-top: 60rpx;
        &-label{
            font-weight: 500;
            margin: 30rpx 0;
        }
    }
    &__auth{
        background: #f6f6f6;
        line-height: 60rpx;
        padding: 30rpx 50rpx;
        b{
            font-weight: 500;
            display: inline-block;
            width: 120rpx;
            text-align: justify;
            text-align-last: justify;
        }
    }
    &__btn{
        margin: 40rpx 0 20rpx;
        display: flex;
        justify-content: space-between;
        button{
            margin: 0;
            width: 100%;
        }
        &.diff button{
            width: calc(50% - 10rpx);
        }
    }
    .ssq-tip{
        margin: 140rpx 0 40rpx;
    }
}
</style>
