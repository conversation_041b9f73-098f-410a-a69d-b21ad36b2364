<template>
    <view class="ssq-home">
        <div class="ssq-home__banner ssq-home-part">
            <!-- #ifdef MP-WEIXIN -->
            <view>
                <swiper
                    class="swiper"
                    indicator-dots="true"
                    autoplay="true"
                    interval="3000"
                    duration="500"
                    circular="true"
                >
                    <swiper-item v-for="(item, index) in swiperList" :key="index">
                        <img :src="item.imageUrl" class="slide-image" @click="handleWXBannerClick(item)" />
                    </swiper-item>
                </swiper>
            </view>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY-->
            <img src="~img/basic/contractBanner.png" class="logo" alt="" @click="handleJump('send', 'banner点击')">
            <!-- #endif -->
            <div class="ssq-home__banner-access">
                <div
                    class="access-item"
                    v-for="(access, index) in accessList"
                    :key="index"
                    @click="handleJump(access.type, access.text)"
                >
                    <text class="access-item__icon" :class="access.icon"></text>
                    <div class="access-item__name">
                        {{ access.text }}
                    </div>
                    <i class="access-item__count" v-show="access.count !== 0">{{ access.count > 99 ? '99+' : access.count || '' }}</i>
                </div>
            </div>
        </div>
        <div class="ssq-home__sum ssq-home-part">
            <p class="ssq-home-part__title">当前持有的合同份数 <span @click="handleContractNumClick('')">去购买</span></p>
            <div class="ssq-home-part__content" v-if="!onlyUnlimited">
                <div class="count-item" @click="handleContractNumClick('对公余额')">
                    <p class="count-item__total">
                        {{ accountBalanceData.useable2bSum || 0 }}
                        <span class="unit">份</span>
                        <span class="time-offer">限时优惠</span>
                    </p>
                    <p class="count-item__text">对公合同</p>
                    <p class="count-item__note">合同中至少有一个非发件方的企业参与签署</p>
                    <span class="count-item__buy">去购买</span>
                    <img class="count-item__bg-img" src="~img/basic/toB.png">
                </div>
                <div class="count-item" @click="handleContractNumClick('对私余额')">
                    <p class="count-item__total">
                        {{ accountBalanceData.useable2cSum || 0 }}
                        <span class="unit">份</span>
                        <span class="time-offer">限时优惠</span>
                    </p>
                    <p class="count-item__text">对私合同</p>
                    <p class="count-item__note">合同中没有企业参与签署，企业发件方自身不计入</p>
                    <span class="count-item__buy">去购买</span>
                    <img class="count-item__bg-img" src="~img/basic/toC.png">
                </div>
            </div>
            <div v-else class="contract-num" v-html="unlimitedText"></div>
        </div>
        <div class="ssq-home__video ssq-home-part" v-if="hasLogin">
            <p class="ssq-home-part__title">新手必看</p>
            <div class="ssq-home-part__content">
                <div class="video-access-item"
                    v-for="(item, index) in videoList"
                    :key="index"
                    @click="openVideo(item.videoPath, item.videoName)"
                >
                    <text class="icon" :class="item.iconClass"></text>
                    <text class="text">{{ item.text }}</text>
                    <text class="play icon-H5shouye_shipinicon"></text>
                </div>
            </div>
        </div>
        <CollectTipTop storageKey="collectTipHasShow" />
        <ActivityLogo />
        <PopupAndIconAd v-if="ifShowPopupOrIconAd" :adList="adList" />
        <!-- #ifdef MP-WEIXIN -->
        <BottomTab :current-index="2"></BottomTab>
        <!-- #endif -->
    </view>
</template>

<script>
import { mapActions } from 'vuex';
import BottomTab from 'components/bottomTab';
import ActivityLogo from 'components/activityLogo';
import PopupAndIconAd from 'components/popupAndIconAd';
import CollectTipTop from 'components/collectTipTop';
import dayjs from 'dayjs';
import { getUserConfigByKey } from 'api/account';
import { getContractCount } from 'api/doc.js';
import { getAdInfo, checkBlackAd } from 'api/ad';

export default {
    name: 'HomePage',
    components: {
        BottomTab,
        ActivityLogo,
        PopupAndIconAd,
        CollectTipTop,
    },
    data() {
        return {
            enterTime: 0,
            accountBalanceData: {},
            onlyUnlimited: false,
            unlimitedText: '',
            hasChargeAuth: false,
            hasKnewActivity: true, // 记录是否查看活动页
            accessList: [{
                type: 'sign',
                text: '待签署合同',
                icon: 'icon-daishenpihetong',
                count: '', // 待签署默认展示角标
            }, {
                type: 'approve',
                text: '待审批合同',
                icon: 'icon-daiqianshuhetong',
                count: 0,
            }, {
                isApplet: true,
                type: 'todo',
                text: '我的待办',
                icon: 'icon-daichulixiaoxi',
                count: 0,
            }],
            videoList: [{
                text: '电子签介绍',
                videoName: '电子签介绍视频',
                iconClass: 'icon-H5shouye_qiyeshiming',
                type: 'desc',
                videoPath: '/views/descVideo/index',
            }, {
                text: '企业实名',
                videoName: '企业实名视频',
                iconClass: 'icon-H5shouye_shangshangqiandianziqianjieshao',
                type: 'entAuth',
                videoPath: '/views/entAuthVideo/index',
            }, {
                text: '个人实名',
                videoName: '个人实名视频',
                iconClass: 'icon-H5shouye_gerenshiming',
                type: 'personAuth',
                videoPath: '/views/personAuthVideo/index',
            }, {
                text: '企业发送合同',
                videoName: '企业发送合同视频',
                iconClass: 'icon-H5shouye_fasong',
                type: 'entSend',
                videoPath: '/views/entSendVideo/index',
            }, {
                text: '个人发送合同',
                videoName: '个人发送合同视频',
                iconClass: 'icon-H5shouye_fasong',
                type: 'personSend',
                videoPath: '/views/personSendVideo/index',
            }, {
                text: '如何签署合同',
                videoName: '签署合同视频',
                iconClass: 'icon-H5shouye_qianshuhetong',
                type: 'sign',
                videoPath: '/views/signVideo/index',
            }],
            shareThumbnail: require('img/shareImg.png'),
            wxBannerImage: null,
            showActivityPop: true,
            ifCloseAd: false,
            ifBlackUser: false,
            adList: [],
            swiperList: [{
                imageUrl: require('img/basic/bankBanner.png'),
                jumpUrl: `/views/webviewRedirect/index?url=${encodeURIComponent('https://aapho.nbcb.com.cn/blcs/mgm/#/MarketingPoster?id=1')}`,
                type: 'bank',
            }, {
                imageUrl: require('img/basic/hubbleBanner.png'),
                jumpUrl: '/views/riskJudge/index',
                type: 'hubble',
            }, {
                imageUrl: require('img/basic/contractBanner.png'),
                jumpUrl: '/views/sendGuide/index',
                type: 'contract',
            }],
        };
    },
    computed: {
        // 是否展示投放广告
        ifShowConfigAd() {
            return (!this.ifBlackUser && this.adList.length)  || false;
        },
        // 是否展示首页弹窗或者悬浮按钮广告
        ifShowPopupOrIconAd() {
            return this.ifShowConfigAd && this.adList.some(item => item.showcase && item.showcase !== 0);
        },
        hasLogin() {
            const isLogined = Boolean(uni.getStorageSync('accessToken'));
            return isLogined;
        },
    },
    methods: {
        ...mapActions(['loginPromise']),
        initAdInfo() {
            const scene = 'homePage'; // 三个值：homePage; signComplete; signReject
            return getAdInfo({ scene, deliveryChannel: 1 }).then((res) => {   // deliveryChannel : 0 全部 1 移动端 2 pc端
                this.adList = res.data || [];
            });
        },
        // 校验用户是否被加入黑名单，加入黑名单的不展示广告
        checkUserIfBlack() {
            checkBlackAd().then((res) => {
                if (res && res.data) {
                    this.ifBlackUser = res.data.value || false;
                    if (!this.ifBlackUser) {
                        this.initAdInfo();
                    }
                }
            });
        },
        handleWXBannerClick(item) {
            if (item.type === 'bank') {
                uni.navigateTo({
                    url: item.jumpUrl,
                });
            } else {
                uni.switchTab({
                    url: item.jumpUrl,
                });
            }
        },
        goActivity() {
            uni.navigateTo({
                url: `views/charge/index?isFromH5Home=1`,
            });
        },
        handleJump(type, eventName) {
            const pageMap = {
                'sign': '/views/doc/index?tabType=1',
                'approve': '/views/doc/index?tabType=2',
                'todo': '/views/todoList/index',
            };
            const url = pageMap[type];
            const hasLogin = Boolean(uni.getStorageSync('accessToken'));
            // SAAS-41258：未登录引导至账户中心页面
            if (!hasLogin) {
                uni.setStorageSync('loginSuccessPage', url);
                uni.navigateTo({
                    url: 'subViews/login/index',
                });
                return;
            }
            this.handleBtnClickSensor(eventName);
            uni.navigateTo({
                url: url,
            });
        },
        handleRecharge(path) {
            // 有权限直接跳转，无权限提示
            if (!this.hasChargeAuth) {
                this.$toast.error(`您没有操作权限，请联系管理员添加，或拨打客服热线400-993-666`);
                return;
            }
            uni.navigateTo({
                url: path,
            });
        },
        openVideo(url, videoName) {
            this.handleBtnClickSensor(videoName);
            uni.navigateTo({
                url,
            });
        },
        checkWallet() {
            this.$http.get(`/ents/charging/wallet`, {}, {
                noToast: 1,
            }).then(res => {
                this.hasChargeAuth = true;
                this.accountBalanceData = res.data;
                this.onlyUnlimited = res.data.unlimited;
                res.data.unlimited && (this.unlimitedText = `不限量使用 于${dayjs(res.data.unlimitedEndWorkTime).format('YYYY-MM-DD')}到期`);
                // `不限量使用 于${dayjs(res.data.unlimitedEndWorkTime).format('YYYY-MM-DD')}到期`
            }).catch(() => {
                // 切主体后无权限时，充值数量
                this.accountBalanceData = {};
            });
        },
        handleJumpTimeOffer() {
            this.handleRecharge('/views/charge/index');
        },
        handleContractNumClick(btnName) {
            btnName && this.handleBtnClickSensor(btnName);
            this.handleRecharge('/views/charge/index');
        },
        handleBtnClickSensor(iconName, firstCategory) {
            this.$sensors.track({
                eventName: 'Mp_Home_BtnClick',
                eventProperty: {
                    icon_name: iconName,
                    first_category: firstCategory || '',
                },
            });
        },
        getUserConfig() {
            getUserConfigByKey('YEAR_END_COUNT_DOWN').then(res => {
                this.hasKnewActivity = res.data.value === 'true';
            }).catch(() => {
                this.hasKnewActivity = false;
            });
        },
        getNoticeCount() {
            getContractCount().then(res => {
                const countMap = res.data.result;
                this.accessList[0].count = countMap.needMeSign;
                this.accessList[1].count = countMap.needMeApproval;
            });
            this.$http.get(`/ents/message/centre/list?shouldReadAllSubject=true&noticeType=NOTICE,APPROVAL&pageNum=5&pageSize=1`).then(res => {
                this.accessList[2].count = +res.data.total;
            });
        }
    },
    onShow() {
        this.$sensors.track({
            eventName: 'Mp_Home_PageView',
        });
        this.enterTime = new Date().getTime();
        this.loginPromise()
            .then(() => {
                this.checkWallet();
                this.getUserConfig();
                this.checkUserIfBlack();
                this.getNoticeCount();
            });
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_Home_PageLeave',
            eventProperty: {
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    onShareAppMessage() {
        return {
            imageUrl: this.shareThumbnail,
        };
    },
};
</script>

<style lang="scss">
.ssq-home{
    position: relative;
    height: 100vh;
    // #ifdef MP-WEIXIN
    height: calc(100vh - 70px);
    // #endif
    overflow-y: scroll;
    background: $--background-color-regular;
    &-part {
        margin-bottom: 10px;
        padding: 15px;
        background: $--color-white;
        &__title {
            margin-bottom: 15px;
            padding-left: 5px;
            height: 14px;
            line-height: 14px;
            font-size: 14px;
            color: $--color-text-primary;
            font-weight: bold;
            border-left: 3px solid $--color-primary;
        }
        &__content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
    }
    &__banner{
        background: $--color-white;
        padding: 5px 15px 15px;
        img {
            width: 100%;
            height: 120px;
        }
        &-access {
            margin-top: 14px;
            display: flex;
            justify-content: space-between;
            padding: 0 32px;
            .access-item {
                text-align: center;
                position: relative;
                width: 60px;
                &__icon{
                    padding: 14px;
                    background: #E6F4FF;
                    font-size: 14px;
                    color: $--color-primary;
                    border-radius: 12px;
                }
                &__name {
                    margin-top: 20px;
                    font-size: 12px;
                    text-align: center;
                }
                &__count {
                    position: absolute;
                    top: -13px;
                    right: 0px;
                    width: 18px;
                    height: 18px;
                    font-size: 9px;
                    line-height: 18px;
                    color: $--color-white;
                    background: #FC0333;
                    border-radius: 18px;
                    border: 2px solid $--color-white;
                }
            }
        }
    }
    &-part__title span{
        color: $--color-primary;
        background: $--color-white;
        border: 1px solid $--color-primary;
        padding: 2px 5px;
        border-radius: 15px;
        float: right;
    }
    .count-item {
        position: relative;
        padding: 10px;
        width: calc(50vw - 20px);
        box-sizing: border-box;
        &__total {
            height: 20px;
            line-height: 20px;
            font-size: 16px;
            font-weight: bold;
            .unit {
                font-size: 12px;
                padding-left: 5px;
            }
            .time-offer {
                float: right;
                font-size: 10px;
                color: $--color-white;
                padding: 0px 5px;
                background: linear-gradient(270deg, #FF9E3E 0%, #FFBA75 100%);
                border-radius: 14px;
            }
        }
        &__buy {
            font-size: 12px;
            color: $--color-white;
            background: $--color-primary;
            padding: 2px 5px;
            border-radius: 15px;
        }
        &__text {
            font-size: 12px;
            line-height: 24px;
        }
        &__note {
            transform: scale(0.8, 0.8);
            transform-origin: left;
            line-height: 16px;
            font-size: 12px;
            color: $--color-text-secondary;
        }
        &__bg-img {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 168px;
            height: 100px;
        }
        &:first-of-type {
            background: #F2F9FE;
        }
        &:last-of-type {
            background: #FEFAF2;
        }
    }
    .video-access-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding: 10px;
        width: calc(50vw - 20px);
        box-sizing: border-box;
        font-size: 14px;
        color: $--color-text-primary;
        background: $--background-color-regular;
        .icon {
            padding-right: 5px;
            font-size: 16px;
        }
        .text {
            flex-grow: 1;
        }
        .play {
            padding-left: 5px;
            color: $--color-text-secondary;
            font-size: 16px;
        }
    }
}
</style>
