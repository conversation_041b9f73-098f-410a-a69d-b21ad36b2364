<template>
    <xd-page :bottom="sendType === 'localSend' ? 0 : 10" class="charge-page">
        <view class="charge-detail">
            <radio-group @change="selectChange">
                <!-- ai套餐 -->
                <view class="charge-detail__title" v-if="aiList.length">
                    <span class="charge-detail__title-ai">
                        <image
                            class="hubble-img"
                            :src="hubbleImg"
                        ></image>
                    </span>
                    <span>合同风控AI顾问</span>
                </view>
                <view v-for="(item, index) in aiList" :key="index">
                    <label class="charge-detail__list">
                        <radio
                            class="charge-detail__radio"
                            :value="item.productPackageId"
                            :checked="item.productPackageId == curPackageId"
                            color="#127FD2"
                        />
                        <view class="charge-detail__item">
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="black-color">套餐包含：{{ item.name === '智签无忧套餐A' ? '1次风险判断+1份对私合同' : '1次风险判断+1份对公合同' }}</span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">{{ '¥' + item.price }}</view>
                        </view>
                    </label>
                </view>
                <!-- 对私合同套餐 -->
                <view class="charge-detail__title" v-if="toCList.length">
                    <span class="charge-detail__title-personal">
                        <text class="icon-a-ic_recharge_personalcontract"></text>
                    </span>
                    <span>对私合同（我要给个人发送合同）</span>
                </view>
                <view v-for="(item, index) in toCList" :key="index">
                    <label class="charge-detail__list">
                        <radio
                            class="charge-detail__radio"
                            :value="item.productPackageId"
                            :checked="item.productPackageId == curPackageId"
                            color="#127FD2"
                        />
                        <view class="charge-detail__item" v-if="item.isNumChangeable && item.tieredRules && item.tieredRules.length > 0">
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="charge-detail__item-tip-left">{{ ('份数：' + wrapPackage(item).contractNum + '份') + (item.presentNum ? '+赠送'+ item.presentNum + '份': '' ) }}</span>
                                    <span class="division">|</span>
                                    <span class="black-color">{{ '￥' + (wrapPackage(item).price / wrapPackage(item).contractNum).toFixed(2) + '/份' }}</span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">{{ '¥' + wrapPackage(item).price }}</view>
                        </view>
                        <view class="charge-detail__item" v-else>
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="charge-detail__item-tip-left">{{ ('份数：' + item.contractNum + '份') + (item.presentNum ? '+赠送'+ item.presentNum + '份': '' ) }}</span>
                                    <span class="division">|</span>
                                    <span class="black-color">{{ '￥' + (item.price/(item.contractNum+item.presentNum)).toFixed(2) + '/份' }}</span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">
                                <img class="charge-detail__price-icon" src="~img/activityHuiIcon.png" alt="" v-if="showPriceIcon(item)">
                                {{ '¥' + item.price }}
                            </view>
                        </view>
                    </label>
                </view>
                <view class="charge-detail__title" v-if="toBList.length">
                    <span class="charge-detail__title-company">
                        <text class="icon-a-ic_recharge_companycontract"></text>
                    </span>
                    <span>对公合同（我要给企业发送合同）</span>
                </view>
                <view v-for="(item, index) in toBList" :key="index">
                    <label class="charge-detail__list">
                        <radio
                            class="charge-detail__radio"
                            :value="item.productPackageId"
                            :checked="item.productPackageId == curPackageId"
                            color="#127FD2"
                        />
                        <view class="charge-detail__item" v-if="item.isNumChangeable && item.tieredRules && item.tieredRules.length > 0">
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="charge-detail__item-tip-left">{{ ('份数：' + wrapPackage(item).contractNum + '份') + (item.presentNum ? '+赠送'+ item.presentNum + '份': '' ) }}</span>
                                    <span class="division">|</span>
                                    <span class="black-color">
                                        {{ '￥' + (wrapPackage(item).price / wrapPackage(item).contractNum).toFixed(2) + '/份' }}
                                    </span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">{{ '¥' + wrapPackage(item).price }}</view>
                        </view>
                        <view class="charge-detail__item" v-else>
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="charge-detail__item-tip-left">{{ ('份数：' + item.contractNum + '份') + (item.presentNum ? '+赠送'+ item.presentNum + '份': '' ) }}</span>
                                    <span class="division">|</span>
                                    <span class="black-color">{{ '￥' + (item.price/(item.contractNum+item.presentNum)).toFixed(2) + '/份' }}</span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">
                                <img class="charge-detail__price-icon" src="~img/activityHuiIcon.png" alt="" v-if="showPriceIcon(item)">
                                {{ '¥' + item.price }}
                            </view>
                        </view>
                    </label>
                </view>
                <view class="charge-detail__title" v-if="advancedFeatureList.length">
                    <span class="charge-detail__title-company">
                    </span>
                    <span>高级功能购买</span>
                </view>
                <view v-for="(item, index) in advancedFeatureList" :key="index">
                    <label class="charge-detail__list">
                        <div class="charge-detail__activity-icon" v-if="showActivityIcon(item)"><div>送</div><span>15份合同</span></div>
                        <radio
                            class="charge-detail__radio"
                            :value="item.productPackageId"
                            :checked="item.productPackageId == curPackageId"
                            color="#127FD2"
                        />
                        <view class="charge-detail__item">
                            <view class="charge-detail__item-left">
                                <view>
                                    {{ item.name }}
                                    <span class="first-buy" v-if="item.isFirstPurchase">限购一次</span>
                                    <span class="first-buy activity" v-else-if="item.activity">{{ item.activity }}</span>
                                </view>
                                <view class="charge-detail__item-tip">
                                    <span class="black-color">{{ '￥' + (item.price).toFixed(2) + '/年' }}</span>
                                </view>
                            </view>
                            <view class="charge-detail__item-right">{{ '¥' + item.price }}</view>
                        </view>
                    </label>
                </view>
            </radio-group>
        </view>
        <view slot="footer" class="charge-detail__footer">
            <view class="charge-detail__footer-total">
                <view class="ent-name"
                    v-if="commonHeaderInfo.enterprises && commonHeaderInfo.enterprises.length ===
                        1"
                >{{ `当前主体：${currentEntName}`
                }} </view>
                <SwitchEnt
                    v-else
                    :fromChargingPage="true"
                    :identityName="`当前主体：${currentEntName}`"
                    class="switch-item"
                    @switchEntSucces="handleSwitchEntSuccess"
                ></SwitchEnt>

                <view>{{ '合计：' + (wrapPackage(curPackage).price || 0) + '元' }}</view>
                <view class="gary-color valid-duration">{{ '有效期：' + (curPackage.validityMonth || '') + '个月' }}</view>
            </view>
            <button :loading="payLoading"
                :disabled="!curPackageId"
                size="default"
                type="primary"
                class="charge-detail__footer-btn"
                @click="goToPay(wrapPackage(curPackage))"
            >去支付</button>
        </view>
        <CancelPayPopup
            v-if="showCancelPayPopup"
            pageName="商品列表页"
            :orderInfo="orderInfo"
            @continue="handleContinuePayer"
            @close="handleCloseCancelPayPopup"
        />
    </xd-page>
</template>

<script>
import {
    checkEntChannel,
    getChargePackage,
    getPackageChannel,
    getLimitPackageChargeInfo,
    getAdvancedFeatures,
    getAiFeatures,
} from 'api/charge';
import xdPage from '@/components/xdPage/index.vue';
import CancelPayPopup from 'components/cancelPayPopup';
import SwitchEnt from 'components/selectSender';
import { mapState, mapGetters, mapActions } from 'vuex';
// import cloneDeep from 'lodash/cloneDeep';
import { jumpAcccountCompareMixin } from 'mixins/jumpAccountCompare';
import { payMixin } from 'mixins/pay';
import iconHubble from '@/assets/images/hubble.png';
export default {
    components: {
        xdPage,
        CancelPayPopup,
        SwitchEnt,
    },
    mixins: [jumpAcccountCompareMixin, payMixin],
    data() {
        return {
            chargeList: [],
            curPackageId: 0,
            toCList: [],
            toBList: [],
            advancedFeatureList: [],
            aiList: [],
            enterTime: 0,
            cacheCurrentName: '',
            activityExpired: +new Date() > +new Date('2025-01-20T18:00:00'), // 活动过期时间
            hubbleImg: iconHubble,
        };
    },
    computed: {
        ...mapState('send', ['sendType']),
        ...mapState('user', ['userType', 'commonHeaderInfo', 'currentEntName', 'entList']),
        ...mapGetters(['mpSourceType']),
        curPackage() {
            return [...this.chargeList, ...this.advancedFeatureList, ...this.aiList].find((item) => {
                return item.productPackageId === this.curPackageId;
            }) || {};
        },
    },
    methods: {
        ...mapActions(['loginPromise', 'switchEntId', 'oauthLogin']),
        ...mapActions('user', ['getHeadInfo', 'handleEntList']),
        showPriceIcon(item) {
            return !this.activityExpired && ['10981', '10985', '10002', '10008'].includes(item.productPackageId);
        },
        showActivityIcon(item) {
            return !this.activityExpired && (item.name === '骑缝章+水印' || item.name === '模板授权');
        },
        handleDefaultPackage() {
            const { scene, packageId: queryPackageId } = this.routeQuery;
            let packageId = scene || queryPackageId;
            if (!packageId) {
                return;
            }
            const isPackageSupported = [...this.chargeList, ...this.advancedFeatureList, ...this.aiList].some(el => el.productPackageId === packageId);
            if (!isPackageSupported) {
                uni.showToast({
                    title: '没有此套餐',
                    icon: 'none',
                });
                packageId = '';
            }
            this.curPackageId = packageId;
        },
        initAdvancedFeatures() {
            getAdvancedFeatures().then(({ data }) => {
                this.advancedFeatureList = data.filter((item) => item.featureId === '68');
            });
        },
        initAiFeaturesList() {
            getAiFeatures().then(({ data }) => {
                this.aiList = data || [];
            });
        },
        handleSwitchEntSuccess() {
            this.$sensors.track({
                eventName: 'Mp_CommodityList_BtnClick',
                eventProperty: {
                    identity_name: this.cacheCurrentEntName,
                    new_identity_name: this.currentEntName,
                    icon_name: '切换主体',
                },
            });
            this.initChargeInfo();
        },
        async initChargeInfo() {
            try {
                this.cacheCurrentEntName = this.currentEntName; // 缓存当前企业名称，后续埋点要用
                this.curPackageId = 0;
                this.chargeList = [];
                this.toCList = [];
                this.toBList = [];
                this.aiList = [];
                this.advancedFeatureList = [];
                this.userType === 'Enterprise' && this.initAdvancedFeatures();
                this.userType === 'Person' && this.initAiFeaturesList();
                const { data: { entChannel, registerChannel } } = await checkEntChannel();
                const funName = (!entChannel && !registerChannel) ? getChargePackage : getPackageChannel;
                const { data: chargeList } = await funName({
                    entChannel,
                    registerChannel,
                    contractId: this.contractId,
                });
                const { data: { hasPurchaseFirstPackageToC,  hasPurchaseFirstPackageToB } } = await
                    getLimitPackageChargeInfo();
                const isPerson = uni.getStorageSync('chosenEntId') === '0';

                // SAAS-38629 支持1000份的套餐购买
                const isProductEnv = this.$http.config.baseURL.includes('bestsign.cn');
                const  toC_productPackageId_1000 = isProductEnv ? '10004' : '10983';
                const  toB_productPackageId_1000 = isProductEnv ? '10010' : '10987';
                (chargeList || []).forEach((item) => {
                    if (item.productType === 2) {
                        // 个人或者已购买过的企业限购特惠套餐
                        if ((item.isFirstPurchase && (isPerson || hasPurchaseFirstPackageToC)) || (item.tieredRules.length > 0 && item.productPackageId !== toC_productPackageId_1000)) {
                            return;
                        }
                        this.toCList.push(item);
                    } else {
                        // 个人或者已购买过的企业限购特惠套餐
                        if ((item.isFirstPurchase && (isPerson || hasPurchaseFirstPackageToB)) || (item.tieredRules.length > 0 && item.productPackageId !== toB_productPackageId_1000)) {
                            return;
                        }
                        this.toBList.push(item);
                    }
                });
                this.chargeList = this.toCList.concat(this.toBList);
                const tenSizeToC = this.chargeList.find((item) => {
                    return item.productPackageId === '10000';
                });
                this.curPackageId = tenSizeToC ? '10000' : this.chargeList[0].productPackageId;
                this.handleDefaultPackage();
            } catch (err) {
                console.log(err);
                setTimeout(() => {
                    uni.navigateBack({ delta: 1 });
                }, 2000);
            }
        },
        selectChange(e) {
            this.curPackageId = e.detail.value;
            this.$point('click_top_up_package', {
                biz_id: e.detail.value,
            });
        },
        // 处理阶梯套餐逻辑
        handleChangeablePackage(changeablePack) {
            const newPack = changeablePack;
            if (newPack.tieredRules && newPack.tieredRules.length) {
                const rule = newPack.tieredRules.find(e => newPack.contractNum >= e.minNum && newPack.contractNum <= e.maxNum);
                newPack.price = newPack.contractNum * rule.price;
                newPack.originPrice = newPack.contractNum * newPack.originPrice;
            }
            newPack.wrapped = true;
            return newPack;
        },
        // 包装套餐包逻辑
        wrapPackage(pack) {
            if (pack?.isNumChangeable && !pack.wrapped) {
                return this.handleChangeablePackage(pack);
            }
            return pack;
        },
    },
    onShow() {
        this.enterTime = new Date().getTime();
        this.$sensors.track({
            eventName: 'Mp_CommodityList_PageView',
        });
    },
    async onLoad(query) {
        this.routeQuery = query;
        this.contractId = query.contractId || '';
        try {
            await this.loginPromise();
            if (query.chosenEntId) {
                await this.switchEntId(query.chosenEntId);
            }

            await this.getHeadInfo(); // 已登录时需要获取当前账号数据进行account对比
            this.handleEntList(); // 获取企业列表以供主体切换
            this.resolveHomeAccountCompare('/views/charge/index?contractId' + query.contractId, this.initChargeInfo);
        } catch (e) {
            const isLogined = Boolean(uni.getStorageSync('accessToken'));
            if (query.isFromH5Home === '1' && !isLogined) {
                // H5跳转过来未登录的话要跳转到登录页
                this.redirectToLogin('/views/charge/index', true);
            } else if (!isLogined) {
                uni.setStorageSync('loginSuccessPage', `/views/charge/index?packageId=${this.routeQuery.packageId}`);
                uni.reLaunch({
                    url: `/subViews/login/index`,
                });
            }
        }
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_CommodityList_PageLeave',
            eventProperty: {
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
};
</script>
<style lang="scss">
.charge-page{
    .switch-ent__account-page{
        margin-left: -10rpx;
        border: 1rpx solid #ddd;
        width: 100%;
        max-width: 400rpx;
        padding: 0rpx 10rpx;
        background-color: #f6f6f6;
        color: black;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.charge-detail {
    background-color: $--background-color-base;
    color: $--color-text-primary;
    height: 100%;
    overflow: scroll;
    &__title {
        height: 100rpx;
        display: flex;
        align-items: center;
        &-company {
            width: 60rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            border-radius: 50%;
            background-color: $--color-primary;
            display: inline-block;
            margin: 0 30rpx;
            .icon-a-ic_recharge_companycontract {
                color: $--color-white;
            }
        }
        &-personal {
            width: 60rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            border-radius: 50%;
            background-color: $--color-success;
            display: inline-block;
            margin: 0 30rpx;
            .icon-a-ic_recharge_personalcontract {
                color: $--color-white;
            }
        }
        &-ai {
            width: 60rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            border-radius: 50%;
            background-color: white;
            display: inline-block;
            margin: 0 30rpx;
            .icon-a-ic_recharge_personalcontract {
                color: $--color-white;
            }
            .hubble-img{
                width:31rpx;
                height: 34rpx;
                padding-top: 16rpx;
            }
        }
    }
    &__list {
        background-color: $--color-white;
        display: flex;
        flex-direction: row;
        height: 120rpx;
        line-height: 120rpx;
        border-top: 1rpx solid $--border-color-lighter;
        padding: 0 30rpx;
        position: relative;
    }
    &__activity-icon{
        position: absolute;
        right: 0;
        background: #f1478e;
        color: #fff;
        font-size: 12px;
        line-height: 16px;
        padding: 3px 8px;
        border-radius: 0 0 0 5px;
        display: flex;
        div{
            font-size: 10px;
            width: 12px;
            height: 12px;
            text-align: center;
            line-height: 12px;
            padding: 2px;
            background: #fff;
            color: #f1478e;
            border-radius: 50%;
            margin-right: 3px;
        }
    }
    &__radio {
        transform: scale(0.8);
        color: $--color-primary!important;
        /* #ifdef MP-ALIPAY */
        // ifdef MP-ALIPAY
        margin-top: 32rpx;
        /* #endif */
    }
    &__item {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        &-left {
            display: flex;
            flex-direction: column;
            line-height: 50rpx;
            justify-content: center;
        }
        &-tip {
            color:$--color-info;
            font-size: 24rpx;
            .division{
                padding: 0 5rpx;
            }
            .black-color{
                color: $--color-text-primary;
            }
        }
        .first-buy {
            border-radius: 6rpx;
            background: linear-gradient(58.49deg, #ff2e4a 34.16%, #ffae00 133.44%);
            color: $--color-white;
            font-size: 12px;
            padding: 0px 6px;
            margin-left: 6px;
        }
        .activity{
            background: linear-gradient(to right, #ff0083, #fd0e56) !important;
        }
    }
    &__footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 20rpx 40rpx 0 40rpx;

        &-total {
            height: 90rpx;
            line-height: 40rpx;
            width: 420rpx;
            .gary-color{
                color:$--color-info;
            }
            .valid-duration {
                /* #ifdef MP-ALIPAY */
                font-size: 12px;
                margin-top: -5px;
                /* #endif */
            }
        }
        &-btn {
            margin: 0;
            width: 240rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: $--color-primary;
            font-size: 32rpx;
            color: $--color-white;
            text-align: center;
            border-radius: 8rpx;
        }
        .ent-name{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    &__price-icon{
        width: 16px;
        height: 20px;
        position: relative;
        right: 4px;
        top: 3px;
    }
}
</style>
