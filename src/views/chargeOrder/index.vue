<template>
    <ul class="charge-order">
        <li class="charge-order__item" v-for="order in orderList" :key="order.orderId">
            <div class="charge-order__title">
                <div>
                    <i class="charge-order__icon" :class="order.icon"></i>
                    <span class="charge-order__name">{{ order.packageName }}</span>
                </div>
                <span class="charge-order__cost"><span>￥</span>{{ order.rechargeMoney }}</span>
            </div>
            <div class="charge-order__content">
                <div class="charge-order__content-item">
                    <span>订单编号：</span>
                    <span>{{ order.orderId }}</span>
                </div>
                <div class="charge-order__content-item">
                    <span>份数及类别：</span>
                    <span>{{ order.orderType }}</span>
                </div>
                <div class="charge-order__content-item">
                    <span>主体：</span>
                    <span>{{ currentEntName }}</span>
                </div>
                <div class="charge-order__content-item">
                    <span>创建时间：</span>
                    <span>{{ order.createTime }}</span>
                </div>
            </div>
        </li>
        <li class="charge-order__empty" v-if="orderList.length === 0">
            <img :src="emptySrc" alt="">
            <p>暂无订单</p>
        </li>
    </ul>
</template>

<script>
import { mapActions, mapState } from 'vuex';
export default {
    data() {
        return {
            currentPage: 1,
            currentPageSize: 10,
            total: 0,
            orderList: [],
            emptySrc: require('img/empty.png'),
        };
    },
    computed: {
        ...mapState('user', ['currentEntName']),
    },
    methods: {
        ...mapActions('user', ['getHeadInfo']),
        getChargeOrderList() {
            this.$http.get('/ents/charging/rechargeRecords', {
                platformType: 5,
                pageNumber: this.currentPage,
                pageSize: this.currentPageSize,
            }).then(res => {
                this.orderList = res.data.list.filter(el => el.payTime).map(item => {
                    const { productNum, productTypeName, productType } = item.products[0];
                    let orderType = `${productNum}份${productTypeName}`;
                    if (productType === 14) {
                        orderType = '';
                        item.products.forEach((element, index) => {
                            orderType += `${index > 0 ? '、' : ''}${element.productName}`;
                        });
                    } else if (productType === 22) { // ai套餐
                        orderType = item.packageName === '智签无忧套餐A' ? '1次风险判断+1份对私合同' : '1次风险判断+1份对公合同';
                    }
                    return {
                        ...item,
                        icon: `icon-a-ic_recharge_${productTypeName === '对私合同' ? 'personalcontract' : 'companycontract'}`,
                        orderType,
                    };
                });
            });
        },
    },
    created() {
        this.getHeadInfo();
        this.getChargeOrderList();
    },
};
</script>
<style lang="scss">
.charge-order{
    &__item{
        padding: 0 40rpx;
        border-top: 20rpx solid #f8f8f8;
    }
    &__empty{
            position: absolute;
            width: 100%;
            top: 50%;
            margin-top: -300rpx;
            img{
                width: 100%;
            }
            p{
                text-align: center;
                margin-top: -120rpx;
                color: $--color-info;
            }
        }
    &__title{
        font-size: 28rpx;
        line-height: 105rpx;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #ddd;
    }
    &__icon{
        font-size: 24rpx;
        line-height: 1;
        padding: 8rpx;
        color: #fff;
        background: #00AA64;
        border-radius: 50%;
        &.icon-a-ic_recharge_companycontract{
            background: #127FD2;
        }
    }
    &__name{
        margin-left: 10px;
    }
    &__cost{
        font-size: 40rpx;
        span{
            font-size: 24rpx;
        }
    }
    &__content{
        font-size: 28rpx;
        line-height: 60rpx;
        color: #999;
        padding: 25rpx 0;
        &-item{
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
