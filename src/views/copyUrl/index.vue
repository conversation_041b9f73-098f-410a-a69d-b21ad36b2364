<template>
    <view class="content">
        <image
            :src="unLookImg"
            mode="widthFix"
            style="width: 100%;"
        />
        <text class="tip1">
            因合同发件方企业的安全策略原因，无法在小程序查看此合同。您需要在浏览器查看。
        </text>
        <text class="tip2">
            请复制合同网址至浏览器中查看合同原文
        </text>
        <view
            class="button"
            @click="clickCopyUrl"
        >
            <text>复制合同网址</text>
        </view>
    </view>
</template>
<script>
import unLookImg from 'src/static/unLook.png';
export default {
    data() {
        return {
            contractId: '',
            source: '',
            shortUrl: '',
            unLookImg: unLookImg,
        };
    },
    onLoad(options) {
        this.initParams(options);
        this.getShortUrl();
    },
    methods: {
        initParams(options) {
            this.contractId = options.contractId;
            this.source = options.source;
            this.shortUrl = options.shortUrl;
        },
        getShortUrl() {
            if (this.source === '3') {
                // 去详情页下载
                this.copyUrl = `${this.$http.baseURL}/mobile/doc/detail?contractId=${this.contractId}`;
            } else if (this.source === '2') {
                // 去签署
                this.copyUrl = decodeURIComponent(this.shortUrl);
            } else {
                this.copyUrl = `${this.$http.baseURL}/mobile/checkCode/hybridPreview/HybridPreview?contractId=${this.contractId}`;
            }
        },
        clickCopyUrl() {
            uni.setClipboardData({
                data: this.copyUrl,
                success() {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                    });
                },
                fail(err) {
                    console.log('pay fail:' + JSON.stringify(err));
                },
            });
        },
    },
};
</script>
<style lang="scss">
page{
    height: 100%;
    display: flex;
}
.content{
    width: 100%;
    overflow: hidden;
    flex-direction: column;
    display: flex;
    text-align: center;
    padding: 15px;
    .tip1{
        color: #333333;
        font-size: 15px;
        margin-top: 30px;
    }
    .tip2{
        margin-top: 40px;
        color: #999999;
        font-size: 15px;
    }
    .button{
        height: 50px;
        background-color: $--color-primary;
        border-radius: 4px;
        align-items: center;
        display: flex;
        justify-content: center;
        text{
            font-size: 17px;
            color: $uni-bg-color;
        }
    }
}
</style>
