<template>
    <view class="video-container">
        <video class="video-wrapper half-screen"
            src="https://static.bestsign.cn:443/e94883dbc261086627ee00ee4d51393a2d5bba43.mp4"
            :controls="true"
            :direction="90"
            :show-fullscreen-btn="true"
            object-fit="fill"
        ></video>
    </view>
</template>

<script>
export default {
    name: 'DescVideo',
};
</script>

<style lang="scss">
.video-container {
    width: 100vw;
    height: 100vh;
    background: $--color-black;
    .video-wrapper {
        width: 100%;
        height: 100%;
        background: $--color-black;
        box-sizing: content-box;
        &.half-screen {
            // #ifdef MP-WEIXIN
            margin-top: 100px;
            // #endif
            height: 200px;
        }
    }
    .play-btn {
        background: red;
    }
}
</style>
