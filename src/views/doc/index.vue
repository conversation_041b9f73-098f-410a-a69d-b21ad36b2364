<template>
    <div class="doc-list">
        <ContractList
            v-if="tabType > 0"
            :searchParams="{ tabType }"
        ></ContractList>
    </div>
</template>

<script>
import ContractList from 'components/contractList';
const pageName = {
    1: '待我签署',
    2: '待我审批',
    3: '他人签署',
    4: '签约完成',
};
export default {
    components: { ContractList },
    data() {
        return {
            tabType: 0,
            enterTime: 0,
        };
    },
    onLoad(options) {
        this.enterTime = new Date().getTime();
        const { tabType } = options;
        this.tabType = tabType;
        uni.setNavigationBarTitle({
            title: ['', '待我签署', '待我审批', '他人签署', '签约完成'][tabType],
        });
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_ContractManageList_PageLeave',
            eventProperty: {
                page_name: pageName[this.tabType],
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    async onShow() {
        this.$sensors.track({
            eventName: 'Mp_ContractManageList_PageView',
            eventProperty: {
                page_name: pageName[this.tabType],
            },
        });
    },
};
</script>

<style>

</style>
