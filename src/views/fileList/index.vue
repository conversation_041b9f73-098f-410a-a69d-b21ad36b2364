<template>
    <view class="content">
        <scroll-view
            scroll-y
            class="scroll"
        >
            <view v-if="fileList && fileList.length">
                <view
                    v-for="(item,index) in fileList"
                    :key="index"
                >
                    <view class="itemcontent">
                        <text>{{ decodeURI(item) }}</text>
                        <button
                            class="button"
                            @click="previewFile(item)"
                        >
                            预览
                        </button>
                        <view class="line" />
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            fileList: [],
        };
    },
    onLoad(options) {
        this.initPageData(options);
    },
    methods: {
        initPageData(options) {
            if (options.fileList) {
                this.fileList = options.fileList.split(',');
            }
        },
        previewFile(item) {
            const index = item.toString().lastIndexOf('.');
            const type = item.toString().substring(index + 1, item.length);
            uni.openDocument({
                fileType: type,
                filePath: `${wx.env.USER_DATA_PATH}/${decodeURI(item)}`,
                showMenu: true,
            });
        },
    },

};
</script>
<style lang="scss">
page{
    height: 100%;
    display: flex;
}
.line{
    background-color: #CCCCCC;
    height: 1px;
    width: 100%;
    margin-top: 10px;
}
.button{
    height: 40px;
    background-color: #127FD2;
    border-radius: 4px;
    align-items: center;
    display: flex;
    justify-content: center;
    flex: 1;
    font-size: 17px;
    color: #FFFFFF;
    margin-top: 10px;
}
.content{
    width: 100%;
    overflow: hidden;
    flex-direction: column;
    display: flex;
    padding: 10px;
    .scroll{
        flex-direction: column;
        overflow: hidden;
        display: flex;
        box-sizing: border-box;
        width: 100%;
        flex: 1;
        .itemcontent{
            margin-top: 10px;
            text{
                size: 18px;
            }
        }
    }
}

</style>
