<template>
    <div class="home" v-show="showPage">
        <ul class="home__nav">
            <li class="home__nav-item" v-for="(nav, index) in navList" :key="nav.name" @click="toContractList(index)">
                <div class="home__nav-item-imgbox">
                    <img :src="nav.img" alt="" class="home__nav-item-img">
                </div>
                <span class="home__nav-item-name">{{ nav.name }}</span>
                <i class="home__nav-item-count" v-show="nav.count > 0">{{ nav.count }}</i>
            </li>
        </ul>
        <SearchBar @search="handleSearchContract"></SearchBar>
        <ContractList
            v-if="allContractShotrcuts.shortcutId"
            ref="contract-list"
            :isAllContract="true"
            :searchParams="{
                name: allContractShotrcuts.name,
                shortcutId: allContractShotrcuts.shortcutId,
            }"
            :extraParams="extraParams"
            :isCustomSearch="isCustomSearch"
        ></ContractList>
        <div class="home__no-login" v-if="!hasLogin">
            <img :src="lockImg" alt="">
            <span class="home__no-login-tip">未登录，暂无数据</span>
            <view class="home__no-login-button" @click="goToLogin">
                {{ loginText }} >>
            </view>
        </div>
        <ActivityLogo />
        <!-- #ifdef MP-WEIXIN -->
        <BottomTab :current-index="0"></BottomTab>
        <!-- #endif -->
    </div>
</template>

<script>
import { mapActions, mapState, mapMutations } from 'vuex';
import SearchBar from 'components/searchBar';
import ContractList from 'components/contractList';
import BottomTab from 'components/bottomTab';
import ActivityLogo from 'components/activityLogo';
import { getContractCount, getShortcuts } from 'api/doc.js';
import { logout } from 'api/account.js';
export default {
    components: { ContractList, SearchBar, BottomTab, ActivityLogo },
    data() {
        return {
            navList: [{
                img: require('img/home_nav_1.png'),
                name: '待我签署',
                count: 0,
            }, {
                img: require('img/home_nav_2.png'),
                name: '待我审批',
                count: 0,
            }, {
                img: require('img/home_nav_3.png'),
                name: '他人签署',
                count: 0,
            }, {
                img: require('img/home_nav_4.png'),
                name: '签约完成',
                count: 0,
            }],
            allContractShotrcuts: {},
            extraParams: {},
            isCustomSearch: false,
            lockImg: require('img/home_lock.png'),
            hasLogin: false,
            onShowNum: 0,
            enterTime: 0,
            shareThumbnail: require('img/shareImg.png'),
            showPage: false,
            h5TransitionData: {},
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo', 'userAccount']),
        ...mapState(['loginText']),
    },
    async onLoad(query) {
        if (query.scene && query.scene.includes('wxLoginSign_')) {
            // wxLoginSign_{token} 详情页签署短链接进入
            const token = query.scene.split('_')[1];
            await this.handleH5Transition({ token });
        }
        if (query.fromH5TransitionPage) {
            await this.handleH5Transition(query);
        }
        this.showPage = true;
        await this.loginPromise();
        if (query.chosenEntId) {
            await this.switchEntId(query.chosenEntId);
        }
        await this.init();
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_ContractManageList_PageLeave',
            eventProperty: {
                page_name: '所有合同',
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    async onShow() {
        console.log('onShow', this.onShowNum);
        this.$sensors.track({
            eventName: 'Mp_Common_BtnClick',
            eventProperty: {
                icon_name: '合同列表',
            },
        });
        this.enterTime = new Date().getTime();
        this.$sensors.track({
            eventName: 'Mp_ContractManageList_PageView',
            eventProperty: {
                page_name: '所有合同',
            },
        });
        if (this.onShowNum) {
            this.showPage = true;
            await this.loginPromise();
            this.init();
        }
        this.onShowNum = 1;
    },
    methods: {
        ...mapActions(['loginPromise', 'switchEntId', 'oauthLogin', 'setLoading']),
        ...mapActions('user', ['getFeatures', 'getHeadInfo', 'handleEntList']),
        ...mapMutations('send', ['setCurEntIndex', 'setCurEntId']),
        async handleH5Transition(query) {
            const { token } = query;
            this.h5TransitionData.token = token;
            await this.getSignInfo();
            return new Promise((resolve, reject) => {
                this.loginPromise().then(async() => {
                    this.setLoading(true);
                    await this.getHeadInfo();
                    this.setLoading(false);
                    // this.h5TransitionData.signAccount 为空可能是代认领
                    if (this.h5TransitionData.signAccount && this.userAccount !== this.h5TransitionData.signAccount) {
                        let content = `当前登录账号${this.userAccount}与发件方指定的收件账号${this.h5TransitionData.signAccount}不一致，无法签署合同。\n您可以点击“切换账号”以新的账号登录后签署或返回原h5页面签署`;
                        let confirmText = '切换账号';
                        // #ifdef MP-ALIPAY
                        content = `您当前登录账号${this.userAccount}与发件方指定的收件账号${this.h5TransitionData.signAccount}不一致，无法签署合同。\n请点击原签署通知中的链接登录后签署`;
                        confirmText = '我知道了';
                        // #endif
                        uni.showModal({
                            content,
                            confirmText,
                            success: (res) => {
                                if (res.confirm) {
                                    // #ifdef MP-WEIXIN
                                    logout().then(() => {
                                        uni.setStorageSync('accessToken', '');
                                        uni.setStorageSync('refreshToken', '');
                                        uni.setStorageSync('thirdCorpId', '');
                                        uni.setStorageSync('sensorsUserInfo', null);
                                        uni.reLaunch({
                                            url: `/subViews/login/index?account=${this.h5TransitionData.signAccount}`,
                                        });
                                    });
                                    // #endif
                                    // #ifdef MP-ALIPAY
                                    uni.reLaunch({
                                        url: '/views/home/<USER>',
                                    });
                                    // #endif
                                    reject();
                                }
                                resolve();
                            },
                        });
                    } else {
                        const contractUrl = encodeURIComponent(`${this.$http.baseURL}/mobile/doc/detail?isBestSignApplet=true&contractId=${this.h5TransitionData.contractId}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`);
                        uni.navigateTo({
                            url: `/views/webviewRedirect/index?url=${contractUrl}`,
                        });
                        reject();
                    }
                }).catch(() => {
                    uni.setStorageSync('loginSuccessPage', `views/home/<USER>
                    // #ifdef MP-WEIXIN
                    uni.reLaunch({
                        url: `/subViews/login/index?account=${this.h5TransitionData.signAccount ? this.h5TransitionData.signAccount : ''}`,
                    });
                    // #endif
                    // #ifdef MP-ALIPAY
                    this.oauthLogin();
                    // #endif
                    reject();
                });
            });
        },
        getSignInfo() {
            return this.$http.get(`/users/ignore/info/${this.h5TransitionData.token}`).then(({ data }) => {
                this.h5TransitionData.signAccount = data.account;
                this.h5TransitionData.contractId = data.contractId;
            });
        },
        toContractList(index) {
            const hasLogin = Boolean(uni.getStorageSync('accessToken'));
            // SAAS-41258：未登录引导至账户中心页面
            if (!hasLogin) {
                uni.setStorageSync('loginSuccessPage', `/views/doc/index?tabType=${tabType}`);
                uni.navigateTo({
                    url: '/subViews/alipayLogin/index',
                });
                return;
            }
            const tabType = index + 1;
            const iconName = {
                1: '待我签署',
                2: '待我审批',
                3: '他人签署',
                4: '签约完成',
            };
            this.$sensors.track({
                eventName: 'Mp_ContractManageList_BtnClick',
                eventProperty: {
                    page_name: '所有合同',
                    first_category: '顶部金刚位',
                    icon_name: iconName[tabType],
                },
            });
            uni.navigateTo({
                url: `/views/doc/index?tabType=${tabType}`,
            });
        },
        handleSearchContract(searchContent) {
            this.$sensors.track({
                eventName: 'Mp_ContractManageList_BtnClick',
                eventProperty: {
                    page_name: '所有合同',
                    first_category: '搜索框',
                    icon_name: '搜索',
                    content: searchContent,
                },
            });
            this.isCustomSearch = true;
            this.extraParams = { 'sign_input': searchContent };
            this.$nextTick(() => {
                this.$refs['contract-list'].init();// 手动执行init
            });
        },
        async initContractList() {
            const count = await getContractCount();
            const countMap = count.data.result;
            Object.keys(countMap).forEach((el, index) => {
                this.navList[index].count = countMap[el];
            });
            const shortcuts = (await getShortcuts()).data.data.filter(el => el.name === '所有合同');
            const allContractShotrcuts = shortcuts[0] || {};
            if (allContractShotrcuts.shortcutId) {
                this.allContractShotrcuts = allContractShotrcuts;
            }
        },
        goToLogin() {
            // #ifdef MP-WEIXIN
            uni.redirectTo({
                url: '/subViews/login/index',
            });
            // #endif
            // #ifdef MP-ALIPAY
            this.oauthLogin();
            // #endif
        },
        handleAuthError(err) {
            console.log(err);
        },
        initEntInfo() {
            const index = (this.commonHeaderInfo.enterprises || []).findIndex(item => item.entId === this.commonHeaderInfo.currentEntId);
            this.setCurEntIndex(index);
            this.setCurEntId(this.commonHeaderInfo.enterprises[index].entId);
        },
        async init() {
            try {
                this.$toast.loading({ mask: true });
                this.hasLogin = Boolean(uni.getStorageSync('accessToken'));
                this.allContractShotrcuts.shortcutId = '';
                this.initContractList();
                await this.getHeadInfo();
                await this.getFeatures();
                this.handleEntList();
                this.initEntInfo();
                this.$toast.hideLoading();
            } catch {
                this.$toast.hideLoading();
            }
        },
    },
    onShareAppMessage() {
        return {
            imageUrl: this.shareThumbnail,
        };
    },
};
</script>

<style lang="scss">
.home{
    padding-top: 50rpx;
	&__nav{
		display: flex;
		margin-bottom: 44rpx;
		&-item{
			flex: 1;
			text-align: center;
			position: relative;
			&-imgbox{
				width: 50rpx;
				height: 50rpx;
				padding: 20rpx;
				margin: 0 auto 10rpx;
				border-radius: 90rpx;
				background: $--background-color-base;
				img{
					width: 50rpx;
					height: 50rpx;
				}
			}
			&-name{
				font-size: 24rpx;
			}
			&-count{
				width: 30rpx;
				height: 30rpx;
				border-radius: 30rpx;
				line-height: 30rpx;
				font-size: 18rpx;
				position: absolute;
				top: 0;
				right: 50rpx;
				text-align: center;
				background: $--color-danger;
				color: $--color-white;
			}
		}
	}
	.contract-list{
        width: 100%;
		height: unset;
		position: absolute;
		top: 340rpx;
        bottom: 0;
        // #ifdef MP-WEIXIN
        bottom: 70px;
        // #endif
	}
    &__no-login {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 70vh;
        overflow: hidden;
        img {
            width: 200rpx;
            height: 240rpx;
        }
        &-tip {
            margin-top: 60rpx;
            color: $--border-color-base;
        }
        &-button {
            margin-top: 30rpx;
            color: $--color-primary;
        }
    }
}
</style>
