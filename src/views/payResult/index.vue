<template>
    <view class="pay-result">
        <text class="icon-ic_singleoption_selected"></text>
        <view>购买成功</view>
        <view class="pay-result-btn" @click="handleReturn">返回</view>
        <CollectTipTop storageKey="payResultCollectHasShow" guideWordWX="添加到&quot;我的小程序&quot;，下次购买更便捷" guideWordAlipay="点击收藏，下次购买更便捷" />
    </view>
</template>

<script>
import CollectTipTop from 'components/collectTipTop';
export default {
    name: 'PayResult',
    components: {
        CollectTipTop,
    },
    data() {
        return {
            isFromH5: false,
            isAnniversary: false,
        };
    },
    methods: {
        handleReturn() {
            // 从H5浏览器直接打开界面时navigate不起作用，直接返回待我签署页
            if (this.isFromH5) {
                uni.navigateTo({
                    url: `/views/doc/index?tabType=1`,
                });
            } else {
                uni.navigateBack({ delta: 1 });
            }
        },
    },
    onLoad(query) {
        this.isFromH5 = query.isFromH5 === 'true';
    },
};
</script>

<style lang="scss">
.pay-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $--color-text-primary;
    .icon-ic_singleoption_selected {
        color: $--color-success;
        font-size: 150rpx;
        margin: 120rpx 0 40rpx;
    }
    &-btn {
        width: 400rpx;
        height: 90rpx;
        line-height: 90rpx;
        text-align: center;
        background-color: $--color-primary;
        color: $--color-white;
        border-radius: 8rpx;
        margin-top: 80rpx;
    }
}
</style>
