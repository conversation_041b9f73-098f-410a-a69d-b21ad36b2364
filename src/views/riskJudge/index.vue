<template>
    <view class="risk-page">
        <PackageBalance ref="packageBalance"></PackageBalance>
        <view class="risk-page__header">
            <view class="risk-page__header-title">合同分析器</view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <button type="primary" @click="handleUploadFile">从聊天软件中获取合同文件</button>
        <!-- #endif -->
        <view class="risk-page__history">
            <view class="risk-page__history-total">
                <span>历史合同记录</span><span>共{{ totalRecord }}条</span>
            </view>
            <view class="risk-page__history-list" @scrolltolower="loadMore">
                <view class="risk-page__history-item" v-for="item in historyList" :key="item.analysisId">
                    <view class="risk-page__history-text" @click="toRiskDetail(item.analysisId, item.analysisType)">
                        <view class="risk-page__history-text-name">{{ item.agreementName }}</view>
                        <view class="risk-page__history-text-time">操作类型：{{ item.showAnalysisType }}</view>
                        <view class="risk-page__history-text-time">{{ item.analysisTime }}</view>
                    </view>
                    <text class="risk-page__history-icon icon-shanchu" @click="deleteRecord(item.analysisId)"></text>
                </view>
            </view>
        </view>
        <!-- <ActivityLogo /> -->
        <!-- #ifdef MP-WEIXIN -->
        <BottomTab :current-index="3"></BottomTab>
        <!-- #endif -->
    </view>
</template>

<script>
import { mapActions } from 'vuex';
import dayjs from 'dayjs';
import BottomTab from 'components/bottomTab';
import packageBalance from './packageBalance';
import { jumpAcccountCompareMixin } from 'mixins/jumpAccountCompare';
export default {
    components: {
        BottomTab,
        packageBalance,
    },
    mixins: [jumpAcccountCompareMixin],
    data() {
        return {
            hasLogin: false,
            onShowNum: 0,
            historyList: [],
            pageNum: 0,
            totalRecord: 0,
            packageData: [],
            paymentPlanType: 'PAID',
            agentType: 'RISK_JUDGEMENT',
        };
    },
    methods: {
        ...mapActions(['loginPromise']),
        deleteRecord(analysisId) {
            uni.showModal({
                content: '确定删除该记录吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.$http.post(`/web/hubble/agreement-analysis/${analysisId}/remove-analysis-record`).then(() => {
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success',
                            });
                            this.init();
                        });
                    }
                },
            });
        },
        loadMore() {
            if (this.currentPage < this.totalPage && !this.loading) {
                this.pageNum++;
                this.getRiskHistory();
            }
        },
        getRiskHistory() {
            this.$http.get(`/web/hubble/agreement-analysis/analysis-record?pageNum=${this.pageNum}&pageSize=10`).then(({ data }) => {
                const { totalRecord, records } = data;
                this.historyList = this.historyList.concat(records.map((item) => ({
                    ...item,
                    analysisTime: dayjs(item.analysisTime).format('YYYY-MM-DD HH:mm:ss'),
                    showAnalysisType: {
                        'CONTRACT_INTERPRETATION': 'AI解读',
                        'EXTRACT': '协议要素提取',
                        'COMPARE': '协议比对',
                        'RISK_JUDGEMENT': 'AI律师',
                        'RISK_JUDGEMENT_REASONING': 'AI律师(深度推理)',
                    }[item.analysisType],
                })));
                this.totalRecord = totalRecord;
            });
        },
        handleUploadFile() {
            if (!this.hasLogin) {
                uni.showToast({
                    title: '用户尚未登录，请前往账号管理登录',
                    icon: 'none',
                });
                return;
            }
            const _this = this;
            uni.showActionSheet({
                title: '请选择想要进行的操作',
                itemList: ['AI解读', 'AI律师', 'AI律师（深度推理）', '信息提取', '合同比对'],
                success: async(res) => {
                    if (res.tapIndex === 0) {
                        _this.agentType = 'RISK_JUDGEMENT';
                    } else if (res.tapIndex === 1) {
                        _this.agentType = 'EXTRACT';
                    }
                    _this.agentType = ['CONTRACT_INTERPRETATION', 'RISK_JUDGEMENT', 'RISK_JUDGEMENT_REASONING', 'EXTRACT', 'COMPARE'][res.tapIndex];
                    // await _this.checkBalance();
                    uni.chooseMessageFile({
                        count: 1,
                        type: 'file',
                        success: (res) => {
                            _this.uploadFile(res.tempFiles[0]);
                        },
                    });
                },
                fail: function(res) {
                    console.log(res.errMsg);
                },
            });
        },
        async uploadFile(file) {
            this.$toast.loading({ title: '文件上传中', mask: true });
            const _self = this;
            await this.loginPromise();
            uni.uploadFile({
                url: `${_self.$http.baseURL}/web/hubble/agreement-analysis/upload-file-and-init`,
                filePath: file.path,
                name: 'file',
                header: { Authorization: `bearer ${uni.getStorageSync('accessToken')}` },
                formData: {
                    fileName: file.name,
                    paymentPlanType: _self.paymentPlanType,
                },
                success: (res) => {
                    const dataObj = JSON.parse(res.data);
                    _self.$toast.hideLoading();
                    if (res.statusCode === 200) {
                        _self.init();
                        _self.confirmOperateType(dataObj.analysisId).then(() => {
                            _self.toRiskDetail(dataObj.analysisId, _self.agentType);
                        }).catch((err) => {
                            uni.showToast({
                                title: err.data.message,
                                icon: 'none',
                            });
                            setTimeout(() => {
                                _self.$refs.packageBalance.showDialog = true;
                            }, 1000);
                        });
                    } else {
                        uni.showToast({
                            title: dataObj.message,
                            icon: 'none',
                        });
                    }
                },
                fail: () => {
                    _self.$toast.hideLoading();
                },
            });
        },
        toRiskDetail(analysisId, agentType) {
            uni.navigateTo({
                url:
                    `/views/webviewRedirect/index?url=${encodeURIComponent(`${this.$http.baseURL}/mobile/risk-analysis?analysisId=${analysisId}&agentType=${agentType}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`)}`,
            });
        },
        confirmOperateType(analysisId) {
            return new Promise((resolve, reject) => {
                this.$http.post(`/web/hubble/agreement-analysis/${analysisId}/confirm-analysis-type`, {
                    agreementAnalysisType: this.agentType,
                }).then(() => {
                    resolve();
                }).catch((err) => {
                    reject(err.response);
                });
            });
        },
        init() {
            this.pageNum = 1;
            this.historyList = [];
            this.getRiskHistory();
        },
    },
    async onLoad(query) {
        try {
            await this.loginPromise();
            this.hasLogin = Boolean(uni.getStorageSync('accessToken'));
        } catch (e) {
            if (query.isFromH5Home === '1' && !this.hasLogin) { // H5跳转过来未登录的话要跳转到登录页
                this.redirectToLogin('/views/riskJudge/index', true);
                return;
            }
        }
    },
    onShow() {
        this.hasLogin = Boolean(uni.getStorageSync('accessToken'));
        if (this.hasLogin) {
            this.init();
            this.$refs.packageBalance.getBalanceData();
        }
    },
};
</script>

<style lang="scss">
.risk-page {
    font-size: 28rpx;
    height: 100vh;
    background: #F5F6FA;
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25rpx 40rpx 16rpx;
    }
    &__history{
        padding: 30rpx;
        &-total{
            display: flex;
            justify-content: space-between;
        }
        &-list{
            margin-top: 20rpx;
            height: calc(100vh - 440rpx);
            overflow: auto;
        }
        &-item{
            height: 122rpx;
            background: #fff;
            border-radius: 10rpx;
            margin-bottom: 20rpx;
            padding: 32rpx;
            display: flex;
            justify-content: space-between;
        }
        &-text{
            font-size: 30rpx;
            &-name{
                width: 500rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            &-time{
                font-size: 24rpx;
                color: #A0A9B4;
                margin-top: 10rpx;
            }
        }
        &-icon{
            color: #E5E5E5;
            margin-top: 20rpx;
        }
    }
    button{
        font-size: 28rpx;
        width: 690rpx;
        height: 72rpx;
        line-height: 72rpx;
        margin: 0 auto;
    }
}
</style>
