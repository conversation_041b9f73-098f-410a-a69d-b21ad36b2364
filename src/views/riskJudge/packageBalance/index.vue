<template>
    <view class="package-balance">
        <view class="package-balance__usage">
            <view class="package-balance__usage-text">计费套餐</view>
            <view class="package-balance__usage-buy" @click="showDialog = true">
                <text class="icon-gouwuche package-balance__usage-icon"></text>
                购买
            </view>
        </view>
        <uni-popup class="package-balance__popup" ref="packagePopUp" type="center" @maskClick="showDialog = false">
            <view class="package-balance__popup-content">
                <view class="package-balance__popup-title">
                    <view>计费套餐</view>
                    <view class="icon icon-ic_close" @click="showDialog = false"></view>
                </view>
                <view class="package-balance__popup-usage-box">
                    <view class="package-balance__popup-usage" v-for="(item, index) in balanceData" :key="index">
                        <text class="package-balance__popup-usage-icon" :class="item.typeIcon"></text>
                        <view class="package-balance__popup-usage-text" :class="{ next: index > 0 }">
                            <text>{{ item.toolType }}</text>
                            <view class="package-balance__popup-usage-percent">
                                已用：{{ `${item.used}/${item.total}` }}
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 套餐列表 -->
                <view class="package-balance__popup-package-list">
                    <view class="package-balance__popup-package" :class="{first: index === 0}" v-for="(item, index) in packageData" @click="curPackageId = item.productPackageId" :key="index">
                        <radio
                            class="charge-detail__radio"
                            :value="item.productPackageId"
                            :checked="item.productPackageId == curPackageId"
                            color="#127FD2"
                        />
                        <view class="package-balance__popup-package-text">
                            <view class="package-balance__popup-package-des">
                                <text class="package-name">{{ item.name }}</text>
                                <text>份额：{{ item.contractNum }}份</text>
                                <text>有效期：{{ item.validityMonth }}月</text>
                            </view>
                            <view>
                                <span>￥{{ item.originPrice }}</span>
                                <text class="price">￥{{ item.price }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="package-balance__popup-buttons">
                    <view class="price">
                        小计
                        <text>￥{{ packageData.find((item) => item.productPackageId === curPackageId).price }}</text>
                    </view>
                    <!-- #ifdef MP-ALIPAY -->
                    <button @click="pay">
                        <text class="icon icon-zhifubaodenglu"></text>
                        &nbsp;支付宝支付
                    </button>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <button @click="pay">
                        <text class="icon icon-weixinzhifu"></text>
                        &nbsp;微信支付
                    </button>
                    <!-- #endif -->
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import { payMixin } from 'mixins/pay';
export default {
    mixins: [payMixin],
    data() {
        return {
            showDialog: false,
            balanceData: [],
            packageData: [],
            curPackageId: '',
        };
    },
    watch: {
        showDialog(val) {
            if (val) {
                this.getPackageData();
                this.getBalanceData();
                this.$refs.packagePopUp.open();
            } else {
                this.$refs.packagePopUp.close();
            }
        },
    },
    methods: {
        currentUsagePercent(item) {
            if (!item.total) {
                return '0%';
            }
            return `${(item.used / item.total) * 100}%`;
        },
        getPackageData() {
            this.$http.get('/ents/charging/hubble-app/package?').then(({ data }) => {
                this.packageData = data.filter((item) => [22, 23, 25, 26, 27].includes(item.productType));
                this.curPackageId = this.packageData[0].productPackageId;
            });
        },
        getBalanceData() {
            this.$http.get(`/web/hubble/users/tool/overview-plan-detail-v2?toolTypes=AI解读,协议风险判断,协议风险判断(深度推理),协议比对,协议要素提取`).then(({ data }) => {
                this.balanceData = [
                    { toolType: 'AI解读', typeIcon: 'icon-AIjiedu-xian', productOfferingCount: 0, productBalanceCount: 0, total: 0, used: 0 },
                    { toolType: '协议风险判断', typeIcon: 'icon-risk', productOfferingCount: 0, productBalanceCount: 0, total: 0, used: 0 },
                    { toolType: '协议要素提取', typeIcon: 'icon-tiqu', productOfferingCount: 0, productBalanceCount: 0, total: 0, used: 0 },
                    { toolType: '协议风险判断(深度推理)', typeIcon: 'icon-shendusikao1', productOfferingCount: 0, productBalanceCount: 0, total: 0, used: 0 },
                    { toolType: '协议比对', typeIcon: 'icon-bidui', productOfferingCount: 0, productBalanceCount: 0, total: 0, used: 0 },
                ];
                data.forEach((item) => {
                    const index = this.balanceData.findIndex((i) => i.toolType === item.toolType);
                    if (index !== -1) {
                        const arrItem = this.balanceData[index];
                        arrItem.productOfferingCount += item.productOfferingCount;
                        arrItem.productBalanceCount += item.productBalanceCount;
                        arrItem.total = arrItem.productOfferingCount;
                        arrItem.used = arrItem.productOfferingCount - arrItem.productBalanceCount;
                    } else {
                        this.balanceData.push({
                            ...item,
                            total: item.productOfferingCount,
                            used: item.productOfferingCount - item.productBalanceCount,
                        });
                    }
                });
                this.balanceData.forEach(tool => {
                    if (tool.toolType === '协议风险判断') {
                        tool.toolType = 'AI律师';
                    } else if (tool.toolType === '协议风险判断(深度推理)') {
                        tool.toolType = 'AI律师(深度推理)';
                    }
                });
            });
        },
        async pay() {
            const that = this;
            const { data: { orderId } } = await this.$http.post(`/ents/charging/package/${this.curPackageId}/ordering`);
            const requestData = await this.getPaymentData(orderId);
            uni.requestPayment({
                ...requestData,
                success: (res) => {
                    // #ifdef MP-Alipay
                    if (res.resultCode !== '9000') {
                        return;
                    }
                    // #endif
                    uni.showToast({
                        title: '支付成功',
                        icon: 'success',
                        duration: 2000,
                    });
                    this.showDialog = false;
                    setTimeout(() => {
                        that.getBalanceData();
                    }, 1000);
                },
                fail: (err) => {
                    console.log(err);
                },
            });
        },
    },
    created() {
        this.getPackageData();
    },
};
</script>

<style lang="scss">
.package-balance{
    &__usage {
        height: 80rpx;
        border-radius: 5px;
        background: #FFFFFF;
        border: 1px solid #EBECF2;
        display: flex;
        align-items: center;
        padding: 0 16rpx;
        justify-content: space-between;
        &-text {
            width: 120rpx;
            flex-shrink: 0;
        }
        &-buy {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            border-radius: 6rpx;
            border: 1rpx solid #EBECF2;
            padding: 10rpx;
        }
        &-icon {
            width: 36rpx;
            font-size: 26rpx;
            flex-shrink: 0;
            margin-right: 5rpx;
        }
        &-usage {
            flex-grow: 1;
            // height: 28rpx;
            font-size: 18rpx;
            line-height: 28rpx;
            color: #A9B1C2;
            margin: 0 12rpx;
            padding-right: 12rpx;
            border-right: 1px solid #D6D6D6;
            display: flex;
            flex-direction: column;
        }
    }
    &__popup {
        &-content {
            max-height: calc(100vh - 80px);
            width: 700rpx;
            padding: 30rpx 30rpx 128rpx;
            box-sizing: border-box;
            border-radius: 20rpx;
            background: #F5F6FA;
            position: relative;
            overflow: hidden;
            .package {
                margin: 10rpx 0;
                button {
                    margin-right: 10rpx;
                }
            }
        }
        &-title {
            font-size: 28rpx;
            line-height: 40rpx;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30rpx;
            .icon{
                font-size: 20rpx;
                color: #A9B1C2;
            }
        }
        &-usage{
            display: flex;
            margin-bottom: 10rpx;
            line-height: 1.2;
            line-height: 60rpx;
            &-box{
                background: #fff;
                border-radius: 10rpx;
                padding: 0 30rpx;
            }
            &-icon{
                color: #127FD2;
                margin-right: 20rpx;
            }
            &-text{
                display: flex;
                justify-content: space-between;
                font-size: 20rpx;
                flex-grow: 1;
                &.next{
                    border-top: 1rpx solid #EBECF2;
                }
            }
            &-percent{
                color: #A9B1C2;
            }
        }
        &-package{
            padding: 20rpx;
            border-radius: 10rpx;
            background: #fff;
            display: flex;
            align-items: center;
            margin-top: 24rpx;
            &.first{
                margin-top: 0;
            }
            &-list{
                max-height: 550rpx;
                overflow: auto;
                margin-top: 24rpx;
            }
            &-text{
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 20rpx;
                color: #A0A9B4;
                flex-grow: 1;
                .package-name{
                    font-size: 24rpx;
                    display: block;
                    color: #333;
                }
                .price{
                    font-size: 36rpx;
                    color: #43526C;
                }
                span{
                    text-decoration: line-through;
                }
            }
        }
        &-buttons{
            width: 100%;
            height: 108rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: absolute;
            bottom: 0;
            left: 0;
            background: #fff;
            padding: 0 30rpx;
            box-sizing: border-box;
            .price{
                white-space: nowrap;
                font-size: 24rpx;
                text{
                    font-size: 36rpx;
                }
            }
            button{
                width: 280rpx;
                margin: 0;
                background: #fff;
                .icon{
                    color: #127FD2;
                    margin-right: 20rpx;
                    &-weixinzhifu{
                        color: #2DC100;
                    }
                }
            }
        }
    }
}
</style>
