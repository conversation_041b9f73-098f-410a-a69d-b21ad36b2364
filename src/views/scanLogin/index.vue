<template>
    <div class="scan-login" v-show="showPage">
        <img :src="imgSrc" alt="test" @click="changeImg" />
        <p>上上签账号登录确认</p>
        <button :loading="loginSubmitLoading" type="primary" @click="toLogin">登录</button>
        <button type="default" @click="toCancel">取消</button>
    </div>
</template>
<script>
import { mapActions } from 'vuex';
export default {
    data() {
        return {
            imgSrc: require('img/scan_login.png'),
            showPage: false,
            qrCodeId: '',
        };
    },
    onLoad({ scene }) {
        this.qrCodeId = scene && scene.split('qrCodeId_')[1];
        this.loginPromise().then(() => {
            this.showPage = true;
        }).catch(() => {
            uni.setStorageSync('loginSuccessPage', `views/scanLogin/index?scene=qrCodeId_${this.qrCodeId}`);
            uni.redirectTo({
                url: `/subViews/login/index`,
            });
        });
    },
    methods: {
        ...mapActions(['loginPromise']),
        toLogin() {
            this.$http.post(`/users/appletQR/login/confirm?qrCodeId=${this.qrCodeId}`).then(() => {
                uni.reLaunch({
                    url: 'views/basic/index',
                });
            });
        },
        toCancel() {
            uni.reLaunch({
                url: 'views/basic/index',
            });
        },
    },
};
</script>
<style lang="scss">
.scan-login{
    img{
        margin: 80px auto 20px;
        width:150px;
        height: 120px;
        display: block;
    }
    p{
        text-align: center;
        color: #43526C;
        font-size: 14px;
        margin-bottom: 60px;
    }
}
</style>
