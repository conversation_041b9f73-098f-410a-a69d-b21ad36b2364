<template>
    <view class="send-guide-page" @click="handleHidePopover">
        <template v-if="hasLogin">
            <view class="send-guide-page__sender">
                <view class="send-guide-page__sender-title">
                    发件方：
                </view>
                <view class="send-guide-page__sender-picker">
                    <SelectSender class="current-switch__access" :fromLocalSendPage="true" :activeTab="activeTab"></SelectSender>
                </view>
            </view>
            <view v-if="ifHybridUser" class="send-guide-page__hybrid-limit">暂不支持混合云客户发送合同</view>
            <template v-else>
                <view class="send-type-switch">
                    <view class="send-type-switch__tab">
                        <uni-segmented-control
                            :current="activeTabIndex"
                            :values="tabItems.map(a => a.value)"
                            @clickItem="tabItemClick"
                            styleType="text"
                            activeColor="#127FD2"
                        ></uni-segmented-control>

                    </view>
                    <view class="send-type-switch__icon" @click.stop="showDefaultConfig = !showDefaultConfig">
                        <text class="icon-ic_tabbar_list"></text>
                        <view class="config-popover" v-show="showDefaultConfig">
                            <view class="config-popover__arrow-icon"></view>
                            <view class="config-popover__title">
                                默认发起方式
                            </view>
                            <view class="config-popover__select">
                                <uni-data-checkbox mode="tag"
                                    v-model="defaultActiveTab"
                                    :localdata="tabConfigItems"
                                    @change="handleDefaultActiveChange"
                                ></uni-data-checkbox>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-show="activeTab === 'sample'" class="send-guide-page__static">
                    <SampleList :samples-data="enabledSamples"></SampleList>
                </view>
                <view v-if="activeTab === 'local'" class="send-guide-page__local">
                    <view class="send-guide-page__send">
                        <view class="send-guide-page__send-icon" @click="goToLocalSend">
                            <text class="icon-fasong"></text>
                        </view>
                        <view class="send-guide-page__send-text" @click="goToLocalSend">发送合同</view>
                    </view>
                </view>
                <view v-if="activeTab === 'template'" class="send-guide-page__template">
                    <TemplateList
                        :shouldUpdate="shouldUpdate"
                    ></TemplateList>
                </view>
            </template>
        </template>
        <view v-else
            class="send-guide-page__no-login"
        >
            <img :src="lockImg" alt="">
            <span class="send-guide-page__no-login-tip">未登录，暂无数据</span>
            <span class="send-guide-page__no-login-button" @click="goToLogin">{{ loginText }} >></span>
        </view>
        <ActivityLogo />
        <!-- #ifdef MP-WEIXIN -->
        <BottomTab :current-index="1"></BottomTab>
        <!-- #endif -->

    </view>
</template>
<script>
import { mapState, mapMutations, mapActions } from 'vuex';
import TemplateList from 'components/templateList';
import SelectSender from 'components/selectSender/index.vue';
import SampleList from 'components/sampleList/index.vue';
import BottomTab from 'components/bottomTab';
import ActivityLogo from 'components/activityLogo';
import { getAllSampleList } from 'api/sample.js';
import { PERSON_SAMPLES, ENTERPRISE_SAMPLES } from '@/utils/sample';
import { jumpAcccountCompareMixin } from 'mixins/jumpAccountCompare';
import { getUserConfigByKey, postUserConfig } from 'api/account';
const pageNameMap = {
    'sample': '文件范本',
    'template': '模板发起',
    'local': '本地发起',
};
export default {
    components: {
        SampleList,
        SelectSender,
        TemplateList,
        BottomTab,
        ActivityLogo,
    },
    mixins: [jumpAcccountCompareMixin],
    data() {
        return {
            hasLogin: false,
            lockImg: require('img/home_lock.png'),
            shouldUpdate: false,
            activeTabIndex: 0,
            defaultActiveTab: '0',
            showDefaultConfig: false,
            menuData: [{
            }, {
                key: 'template',
                value: '模板发起',
            }, {
                key: 'local',
                value: '本地文档',
            }],
            sampleList: [],
            enterTime: 0,
            shareThumbnail: require('img/shareImg.png'),
            ACTIVE_TAB_CACHE_KEY: 'APPLET_SEND_ACTIVE_TAB',
        };
    },
    computed: {
        ...mapState('user', ['commonHeaderInfo']),
        ...mapState('send', ['curEntId']),
        ...mapState(['loginText']),
        isPerson() {
            return this.commonHeaderInfo.userType === 'Person';
        },
        ifShowLocalSend() {
            return !this.commonHeaderInfo.openNewContractTemplate || this.isPerson;
        },
        ifShowTemplateSend() {
            return !!this.commonHeaderInfo.openNewContractTemplate && !this.ifHybridUser;
        },
        ifHybridUser() {
            return this.commonHeaderInfo?.deltaHybridVersion === '3.0';
        },
        activeTab() {
            return this.tabItems[this.activeTabIndex].key;
        },
        tabConfigItems() {
            return [{
                value: '0',
                text: '文件范本',
            }, {
                value: '1',
                text: this.ifShowLocalSend ? '本地文档' : '模板发起',
            }];
        },
        tabItems() {
            const tabsArr = [{
                key: 'sample',
                value: '文件范本',
            }];
            if (this.ifShowLocalSend) {
                tabsArr.push({
                    key: 'local',
                    value: '本地文档',
                });
            }
            if (this.ifShowTemplateSend) {
                tabsArr.push({
                    key: 'template',
                    value: '模板发起',
                });
            }
            return tabsArr;
        },
        enabledSamples() {
            return this.sampleList.filter(a => {
                if (this.isPerson) {
                    return PERSON_SAMPLES.includes(a.sampleTitle);
                } else {
                    return ENTERPRISE_SAMPLES.includes(a.sampleTitle);
                }
            });
        },
    },
    watch: {
        curEntId() {
            // this.activeTabIndex = Number(this.defaultActiveTab);
            this.sendFirstTabPoint(true);
        },
        activeTabIndex(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.$sensors.track({
                    eventName: 'Mp_ContractSendList_PageLeave',
                    eventProperty: {
                        page_name: pageNameMap[this.tabItems[oldVal].key],
                        $event_duration: (new Date().getTime() - this.enterTime) / 1000,
                    },
                });
            }
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.enterTime = new Date().getTime();
            this.$sensors.track({
                eventName: 'Mp_ContractSendList_PageView',
                eventProperty: {
                    page_name: pageNameMap[this.tabItems[this.activeTabIndex].key],
                },
            });
        },
    },
    methods: {
        ...mapMutations('send', ['setSendType', 'setCurEntIndex', 'setCurEntId']),
        ...mapActions(['setLoading', 'loginPromise', 'oauthLogin']),
        ...mapActions('user', ['getHeadInfo', 'handleEntList']),
        async handleDefaultActiveChange(e) {
            await postUserConfig(this.ACTIVE_TAB_CACHE_KEY, e.detail.value);
            this.activeTabIndex = Number(e.detail.value);
            this.handleHidePopover();
        },
        handleHidePopover() {
            this.showDefaultConfig = false;
        },
        tabItemClick(e) {
            if (this.activeTabIndex !== e.currentIndex) {
                this.activeTabIndex = e.currentIndex;
                if (this.activeTabIndex === 0) {
                    this.sendFirstTabPoint();
                } else {
                    this.sendSecondTabPoint();
                }
            }
        },
        sendSecondTabPoint() {
            const event = this.ifShowLocalSend ? 'enter_local_document_page' : 'enter_template_selection_page';
            this.$point(event, {
                biz_id: '',
            });
        },
        sendFirstTabPoint(previous_action = false) {
            this.$point('enter_sample_selection_page', {
                biz_id: '',
                utm_source: '',
                previous_action: previous_action ? '是' : '否',
            });
        },
        goToLocalSend() {
            this.$sensors.track({
                eventName: 'Mp_ContractSendList_BtnClick',
                eventProperty: {
                    page_name: '本地发起',
                    icon_name: '发送合同',
                },
            });
            if (this.hasLogin) {
                this.setSendType('localSend');
                uni.navigateTo({ url: '/subSendViews/localSend/index' });
            }
        },
        goToLogin() {
            // #ifdef MP-WEIXIN
            uni.redirectTo({
                url: '/subViews/alipayLogin/index',
            });
            // #endif
            // #ifdef MP-ALIPAY
            this.oauthLogin();
            // #endif
        },
        initSampleList() {
            getAllSampleList().then(res => {
                this.sampleList = res.data;
            });
        },
        initEntInfo() {
            const index = (this.commonHeaderInfo.enterprises || []).findIndex(item => item.entId === this.commonHeaderInfo.currentEntId);
            this.setCurEntIndex(index);
            this.setCurEntId(this.commonHeaderInfo.enterprises[index].entId);
        },
        initDefaultActiveTab() {
            getUserConfigByKey(this.ACTIVE_TAB_CACHE_KEY)
                .then(({ data }) => {
                    if (data) {
                        this.defaultActiveTab = data.value || '0';
                        this.activeTabIndex = Number(data.value);
                    }
                });
        },
        initData() {
            this.handleEntList();
            this.initEntInfo();
            this.initSampleList();

            this.resolveHomeAccountCompare('/views/sendGuide/index');
        },
    },
    async onLoad(query) {
        try {
            await this.loginPromise();
            this.hasLogin = Boolean(uni.getStorageSync('accessToken'));
            await this.getHeadInfo();
            this.initData();
        } catch (e) {
            if (query.isFromH5Home === '1' && !this.hasLogin) { // H5跳转过来未登录的话要跳转到登录页
                this.redirectToLogin('/views/sendGuide/index', true);
                return;
            }
        }
    },
    async onShow() {
        this.$sensors.track({
            eventName: 'Mp_Common_BtnClick',
            eventProperty: {
                icon_name: '发送合同',
            },
        });
        this.enterTime = new Date().getTime();
        this.$sensors.track({
            eventName: 'Mp_ContractSendList_PageView',
            eventProperty: {
                page_name: pageNameMap[this.tabItems[this.activeTabIndex].key],
            },
        });
        this.shouldUpdate = !this.shouldUpdate;
        this.initDefaultActiveTab();
        this.sendFirstTabPoint(true);
        this.$point('click_send_contact', {
            biz_id: '',
        });
    },
    onHide() {
        this.$sensors.track({
            eventName: 'Mp_ContractSendList_PageLeave',
            eventProperty: {
                page_name: pageNameMap[this.tabItems[this.activeTabIndex].key],
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
    onShareAppMessage() {
        return {
            imageUrl: this.shareThumbnail,
        };
    },
};
</script>
<style lang="scss">
.send-guide-page {
    height: 100%;
    color: $--color-text-primary;
    background-color: $--background-color-base;
    overflow: hidden;
    &__hybrid-limit {
        background-color: $--color-white;
        height: calc(100% - 91rpx);
        text-align: center;
        line-height: 80px;
        color: $--color-text-secondary;
    }
    &__sender {
        background-color: $--background-color-regular;
        height: 90rpx;
        line-height: 90rpx;
        display: flex;
        justify-content: space-between;
        color: $--color-primary-light-3;
        font-size: 14px;
        &-title {
            display: inline-block;
            padding-left: 30rpx;
            width: 160rpx;
        }
        &-picker {
            display: inline-block;
            padding: 0 30rpx;
            width: calc(100% - 190rpx);
            .switch-ent__name {
				width: calc(100% - 40rpx);
                text-align: right;
                line-height: 90rpx;
            }
            .icon-ic_forward {
                color: $--color-primary-light-3 !important;
            }
        }
    }
    .send-type-switch {
        display: flex;
        border-bottom: 1px solid $--border-color-light;
        background-color: $--color-white;
        box-sizing: border-box;
        &__tab {
            flex: 1;
            height: 90rpx;
            line-height: 90rpx;
            .segmented-control {
                font-size: 16px;
                height: 90rpx !important;
                line-height: 90rpx !important;
                &__text {
                    font-size: 16px;
                    padding: 13px 0;
                }
            }
        }
        &__icon {
            width: 100rpx;
            position: relative;
            text-align: center;
            .icon-ic_tabbar_list {
                line-height: 90rpx;
            }
            .config-popover {
                position: absolute;
                width: 100px;
                top: 95rpx;
                right: 16rpx;
                padding: 6px 8px 6px 10px;
                z-index: 9;
                border: 1px solid $--border-color-light;
                background-color: $--color-white;
                border-radius: 3px;
                &__title {
                    font-size: 14px;
                    margin-bottom: 5px;
                }
                &__arrow-icon {
                    position: absolute;
                    right: 30rpx;
                    top: -10px;
                    width: 0;
                    height: 0;
                    border: 6px solid transparent;
                    border-bottom: 6px solid $--color-white;
                }
                &__select {
                    .uni-data-checklist {
                        padding-left: 8px;
                        .checklist-group {
                            justify-content: center;
                        }
                    }
                }
            }
        }
    }
    &__btn {
        height: 100rpx;
        line-height: 100rpx;
        color: $--color-primary;
        background-color: $--color-white;
        text-align: center;
    }
    &__tip {
        color: $--color-text-secondary;
    }
    &__static {
        height: calc(100vh - 90rpx);
        // #ifdef MP-WEIXIN
        height: calc(100vh - 300rpx);
        // #endif
        overflow: auto;
    }
    &__template {
        height: 100%;
        text-align: center;
        overflow-y: hidden;
        img {
            width: 480rpx;
            height: 240rpx;
            margin: 100rpx 0 0;
        }
    }
    &__no-login {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 70vh;
        overflow: hidden;
        img {
            width: 200rpx;
            height: 240rpx;
        }
        &-tip {
            margin-top: 60rpx;
            color: $--border-color-base;
        }
        &-button {
            margin-top: 30rpx;
            color: $--color-primary;
        }
    }
    &__local {
        height: 100%;
        overflow-y: hidden;
    }
    &__send {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 200rpx;
        height: 100%;
        background-color: $--color-white;
        &-icon {
            width: 140rpx;
            height: 140rpx;
            line-height: 140rpx;
            border-radius: 70rpx;
            text-align: center;
            background-color: $--color-primary;
            margin-bottom: 20rpx;
            .icon-fasong {
                color: $--color-white;
                font-size: 60rpx;
            }
        }
        &-text {
            color: $--color-primary;
        }
    }
}
</style>
