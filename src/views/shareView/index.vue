/* eslint-disable vue/valid-template-root */
<!--
页面功能： 范本发起
路由参数：type，范本类型
-->
<template>
    <div class="share-webview">
        <img class="share-webview__logo" src="~img/logo.png" alt="">
        <view class="share-webview__error" v-if="errorInfo">{{ errorInfo }}</view>
        <view class="share-webview__info" v-if="showInfo">
            {{ shareViewText }}
            <view class="opt-row">
                <button
                    class="opt-btn"
                    type="primary"
                    @click="toReviewPage"
                >
                    去审阅
                </button>
            </view>
        </view>
    </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { logout } from 'api/account';
import dayjs from 'dayjs';

export default {
    data() {
        return {
            showInfo: false,
            shareId: '',
            errorInfo: '',
            shareInfo: {
                'shareId': '', // 分享ID
                'initiatorName': '', // 分享发起人名称
                'initiatorEntName': '', // 分享发起人企业名称
                'contractTitle': '', // 合同标题
                'reviewerAccount': '', // 审阅人账号
                'expiredDate': '', // 分享截止时间戳
            },
        };
    },
    computed: {
        ...mapState('send', ['curEntIndex']),
        ...mapState('user', ['entList', 'commonHeaderInfo', 'currentEntName', 'userAccount']),
        currentPath() {
            return `views/shareView/index?shareId=${this.shareId}`;
        },
        shareViewText() {
            const { initiatorName, expiredDate, contractTitle } = this.shareInfo;
            return `${initiatorName}邀请您审阅《${contractTitle}》，该链接有效期截止到${dayjs(expiredDate).format('YYYY-MM-DD HH:mm:ss')}`;
        },
    },
    methods: {
        ...mapActions(['loginPromise']),
        ...mapActions('user', ['getHeadInfo']),
        async init() {
            this.accessToken = uni.getStorageSync('accessToken');
            const isLogined = Boolean(this.accessToken);
            try {
                const { data } = await this.initShareData();
                this.shareInfo = data;
                this.errorInfo = '';
            } catch (e) {
                this.errorInfo = e.response?.data?.message;
            }
            if (this.errorInfo) {
                return;
            } else if (!isLogined) {
                this.redirectToLogin(false);
            } else {
                const currentAccount = this.commonHeaderInfo?.platformUser?.account;
                console.log('currentAccount = ', currentAccount, ' redirect to = ', this.shareInfo.reviewerAccount);
                if (currentAccount !== this.shareInfo.reviewerAccount) {
                    this.resolveAccountCompare(true);
                } else {
                    this.showInfo = true;
                }
            }
        },
        toReviewPage() {
            const url =
                `${this.$http.baseURL}/mobile/sign/share-view?shareId=${this.shareId}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`;
            uni.navigateTo({
                url: `/views/webviewRedirect/index?url=${encodeURIComponent(url)}`,
            });
        },
        redirectToLogin(isLogined) {
            uni.setStorageSync('loginSuccessPage', this.currentPath);
            if (isLogined) {
                logout().then(() => {
                    uni.setStorageSync('accessToken', '');
                    uni.setStorageSync('refreshToken', '');
                    uni.setStorageSync('thirdCorpId', '');
                    uni.setStorageSync('sensorsUserInfo', null);
                    uni.reLaunch({
                        url: `/subViews/login/index?account=${this.shareInfo.reviewerAccount}`,
                    });
                }).catch(() => {

                });
            } else {
                uni.reLaunch({
                    url: `/subViews/login/index?account=${this.shareInfo.reviewerAccount}`,
                });
            }
        },
        resolveAccountCompare() {
            const content = `审阅人账号与小程序当前登录账号不一致，您可选择切换账号登录进入后续页面，或者点击我知道了，停留在原账号的小程序页面。`;
            // 如果需要处理账号不一致提示(H5首页跳转场景)，则在用户选择完成后继续请求
            const that = this;
            uni.showModal({
                title: '账号不一致',
                content,
                confirmText: '我知道了',
                confirmColor: '#127fd2',
                cancelText: '切换账号',
                showCancel: true,
                success: (res) =>  {
                    if (res.cancel) {
                        that.redirectToLogin(true);
                    } else {
                        uni.reLaunch({
                            url: '/views/basic/index',
                        });
                    }
                },
            });
        },
        initShareData() {
            return this.$http.get(`/contract-api/ignore/share-review/${this.shareId}?platform=WX_APPLET`);
        },
    },
    async onLoad({ scene, shareId }) {
        if (!shareId && !scene) {
            uni.showToast({
                icon: 'none',
                title: '无效的参数，缺少转发审阅id',
            });
            return;
        }
        this.shareId = shareId || scene.split('shareId_')[1];
        try {
            await this.loginPromise();
            await this.getHeadInfo(); // 已登录时需要获取当前账号数据进行account对比
        } catch (e) {
            console.error('err = ', e);
        }
        await this.init();
    },
};
</script>

<style lang="scss">
.share-webview{
    text-align: center;
    &__logo{
        width: 128px;
        height: 55px;
        margin: 80px auto 20px;
    }
    &__error {
        margin-top: 30px;
    }
    &__info{
        text-align: center;
        padding: 20px;
        color: #333;
        font-size: 14px;
        .opt-row {
            text-align: center;
            .opt-btn {
                width: 120px;
                font-size: 14px;
                height: 40px;
                line-height: 40px;
            }
        }
    }
}
</style>
