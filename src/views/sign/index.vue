<template>
    <Webview :url="url"></Webview>
</template>

<script>
import Webview from 'components/webview';

export default {
    components: { Webview },
    data() {
        return {
            url: '',
        };
    },
    onLoad() {
        if (this.$Route.query.url) {
            this.url = decodeURIComponent(this.$Route.query.url);
        } else if (this.$Route.query.contractId) {
            this.url = `${this.$http.baseURL}/sign?channel=notice&contractId=${this.$Route.query.contractId}&type=sign&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`;
        }
    },
};
</script>

<style>

</style>
