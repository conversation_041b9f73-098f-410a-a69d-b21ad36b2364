<template>
    <view class="video-container">
        <video class="video-wrapper"
            src="https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/5510d65f-d3f1-4f42-80f9-d7903ccddae5.mp4"
            :controls="true"
            :direction="90"
            :show-fullscreen-btn="false"
            object-fit="fill"
        ></video>
    </view>
</template>

<script>
export default {
    name: 'SignVideo',
};
</script>

<style lang="scss">
.video-container {
    width: 100vw;
    height: 100vh;
    background: $--color-black;
    .video-wrapper {
        width: 100%;
        height: 100%;
        background: $--color-black;
        box-sizing: content-box;
        &.half-screen {
            // #ifdef MP-WEIXIN
            margin-top: 100px;
            // #endif
            height: 200px;
        }
    }
    .play-btn {
        background: red;
    }
}
</style>
