<template>
    <div class="webview" v-show="showImg">
        <img class="webview__back" src="~img/go_back.png" alt="">
        <img class="webview__logo" src="~img/logo.png" alt="">
    </div>
</template>

<script>
export default {
    data() {
        return {
            showImg: false,
        };
    },
    mounted() {
        uni.showLoading();
        let query = '';
        Object.keys(this.$Route.query).forEach((el, index) => {
            query += `${index > 0 ? '&' : ''}${el}=${this.$Route.query[el]}`;
        });
        setTimeout(() => {
            uni.hideLoading();
            uni.navigateTo({
                url: `/views/webviewRedirect/index?${query}`,
            });
        }, 1000);
        setTimeout(() => {
            this.showImg = true;
        }, 2000);
    },
};
</script>

<style lang="scss">
.webview{
    &__back{
        width: 192px;
        height: 50px;
    }
    &__logo{
        width: 128px;
        height: 55px;
        position: absolute;
        left: 50%;
        margin-left: -64px;
        top: 40%;
    }
}
</style>
