<template>
    <Webview :url="url"></Webview>
</template>

<script>
import Webview from 'components/webview';
import { mapState, mapActions } from 'vuex';

export default {
    components: { Webview },
    data() {
        return {
            url: '',
        };
    },
    computed: {
        ...mapState(['isWeWork']),
    },
    methods: {
        ...mapActions(['loginPromise']),
    },
    onLoad() {
        const userId = uni.getStorageSync('userId') || '';
        const queryUrl = this.$Route.query.url;
        const { fromShare, contractId } = this.$Route.query;
        if (queryUrl) {
            const jumpUrl = decodeURIComponent(queryUrl);
            console.log('queryUrl', `${queryUrl}`);
            console.log('jumpUrl', `${jumpUrl}`);
            const linkMark = jumpUrl && (jumpUrl.includes('?') ? '&' : '?');
            this.url = `${jumpUrl}${linkMark}userId=${userId}`;
        } else if (fromShare || contractId) { // 从范本分享卡片,或单带contractId（到小程序审批）来的
            fromShare && this.$point('click_sample_contact_foward_card', {
                biz_id: contractId,
            });
            this.loginPromise()
                .then(() => {
                    this.url = `${this.$http.baseURL}/mobile/doc/detail?isBestSignApplet=true&userId=${userId}&contractId=${contractId}&access_token=${uni.getStorageSync('accessToken')}&refresh_token=${uni.getStorageSync('refreshToken')}`;
                })
                .catch(() => {
                    uni.setStorageSync('loginSuccessPage', `/views/webviewRedirect/index?contractId=${contractId}`);
                    uni.redirectTo({
                        url: '/subViews/login/index',
                    });
                });
        }
    },
};
</script>

<style>

</style>
