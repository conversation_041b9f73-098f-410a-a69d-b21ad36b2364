const path = require('path');
const TransformPages = require('uni-read-pages');
const { webpack } = new TransformPages();
function resolve(dir) {
    return path.join(__dirname, dir);
}
module.exports = {
    transpileDependencies: ['@dcloudio/uni-ui'],
    configureWebpack: {
        resolve: {
            extensions: ['*', '.js', '.vue', '.json', '.css', '.scss', '.less', '.ts'],
            alias: {
                'src': resolve('src'),
                'assets': resolve('src/assets'),
                'img': resolve('src/assets/images'),
                'iconfont': resolve('src/assets/iconfont'),
                'http': resolve('src/http'),
                'api': resolve('src/api'),
                'styles': resolve('src/styles'),
                'store': resolve('src/store'),
                'plugins': resolve('src/plugins'),
                'utils': resolve('src/utils'),
                'mixins': resolve('src/mixins'),
                'components': resolve('src/components'),
                'views': resolve('src/views/'),
            },
        },
        optimization: {
            splitChunks: {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        name: 'vendor',
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: 'initial',
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        priority: 5,
                        chunks: 'initial',
                        reuseExistingChunk: true,
                    },
                },
            },
            usedExports: true,
            sideEffects: false,
        },
        plugins: [
            new webpack.DefinePlugin({
                ROUTES: webpack.DefinePlugin.runtimeValue(() => {
                    const tfPages = new TransformPages({
                        includes: ['path', 'name', 'aliasPath'],
                    });
                    return JSON.stringify(tfPages.routes);
                }, true),
            }),
        ],
    },
};
